@model Cerebrum.ViewModels.RadDicom.VMAccessionSearch



<script type="text/javascript">

         $(function() {

             $("#badpatientsearch").autocomplete({
                 source: function (request, response) {
                     var searchUrl = '/patients/GetPracticePatientsRAD/';
                     var search = request.term;

                     $.ajax({
                         url: searchUrl,
                         type: 'GET',
                         data:
                             {
                                 patientSearch: search
                             },
                         success: function (data) {
                             response(data);
                         },
                         error: function (jqXHR, textStatus, errorThrown) {
                             checkAjaxError(jqXHR);
                         },
                         complete: function () {

                         },
                     });
                 },
                 minLength: 2,
                 select: function (event, ui) {
                     $("#badpatientid").val(ui.item.id);
                     $("#badpatientsearch").val(ui.item.PatientFirstName + " " + ui.item.PatientLastName);
                     return false;
                 }
             }).autocomplete("instance")._renderItem = function (ul, item) {
                 return $('<li style="background-color:white;">' +
                     '<div style="__item"><p>' + item.PatientFirstName + " " + item.PatientLastName + '</p>'
                      + '</div>'
                     + '</li>'
                     ).appendTo(ul);
             };
         });

         function clearpatientnamebad() {

             $("#badpatientid").val("");
             $("#badpatientsearch").val("");
         }


</script>




@*@using (Html.BeginForm("FixLostDicomExam", "DICOM", new { area = "" }, FormMethod.Post, new { @class = "form-inline", @id = "frm-triage-search" }))
{*@
    @*@Html.AntiForgeryToken()*@


    <div class="form-group form-group-sm">
        @Html.Label("Accession number", "", new { @class = "control-label" })  
         @Html.EditorFor(model => model.badaccessionnumber, new { htmlAttributes = new { @class = "form-control" } })
    </div>


    <div class="form-group form-group-sm">
        @Html.Label("Exam Date", "", new { @class = "control-label" })
        @Html.EditorFor(model => model.badexamdate, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>



        <div class="form-group form-group-sm">
            <input name="badpatientsearch" id="badpatientsearch" type="text" class="form-control" placeholder="Patient Search "  onclick="clearpatientnamebad();" >
            <input name="badpatientid" id="badpatientid" type="hidden"/>
        </div>

    <div class="form-group form-group-sm">
        @Html.Label("Patient DOB", "", new { @class = "control-label" })
        @Html.EditorFor(model => model.badPatDOB, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>

    <input type="hidden" name="accentype" value="badaccession">

    <button type="submit" class="btn btn-default btn-sm btn-dicom" style="margin-left: 20px;">Search</button>

<div>
    @*@Html.ValidationMessageFor(model => model.DoctorId, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.TriageUrgencyId, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.StartDate, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.EndDate, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.Patient, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })*@
</div>




