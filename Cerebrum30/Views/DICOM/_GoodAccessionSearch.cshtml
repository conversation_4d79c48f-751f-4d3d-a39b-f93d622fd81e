@model Cerebrum.ViewModels.RadDicom.VMAccessionSearch

<script type="text/javascript">


         $(function() {

             $("#goodpatientsearch").autocomplete({
                 source: function (request, response) {
                     var searchUrl = '/patients/getpracticepatients/';
                     var search = request.term;


                     $.ajax({
                         url: searchUrl,
                         type: 'GET',
                         data:
                             {
                                 patientSearch: search
                             },
                         success: function (data) {
                             response(data);
                         },
                         error: function (jqXHR, textStatus, errorThrown) {
                             checkAjaxError(jqXHR);
                         },
                         complete: function () {

                         },
                     });
                 },
                 minLength: 2,
                 select: function (event, ui) {

                     document.getElementById("goodpatientid").value = ui.item.PatientId;

                     document.getElementById("goodpatientsearch").value = ui.item.FullName;


                     return false;
                 }
             }).autocomplete("instance")._renderItem = function (ul, item) {
                 return $('<li style="background-color:white;">' +
                     '<div style="__item"><p>' + item.FullName + '</p>'
                      + '</div>'
                     + '</li>'
                     ).appendTo(ul);
             };


         });


         function clearpatientnamegood() {

             document.getElementById("goodpatientid").value = "";

             document.getElementById("goodpatientsearch").value = "";
         }



</script>


@*@using (Html.BeginForm("FixLostDicomExam", "DICOM", new { area = "" }, FormMethod.Post, new { @class = "form-inline", @id = "frm-triage-search" }))
{*@
    @*@Html.AntiForgeryToken()*@


    <div class="form-group form-group-sm">
        @Html.Label("Accession number", "", new { @class = "control-label" })
        @Html.EditorFor(model => model.goodaccessionnumber, new { htmlAttributes = new { @class = "form-control" } })
    </div>


    <div class="form-group form-group-sm">
        @Html.Label("Test date", "", new { @class = "control-label" })
        @Html.EditorFor(model => model.goodexamdate, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>

    <div class="form-group form-group-sm">
        <input name="goodpatientsearch" id="goodpatientsearch" type="text" class="form-control" placeholder="Patient Search " onclick="clearpatientnamegood();w">
        <input name="goodpatientid" id="goodpatientid" type="hidden" />
    </div>

    <div class="form-group form-group-sm">
        @Html.Label("Patient DOB", "", new { @class = "control-label" })
        @Html.EditorFor(model => model.goodPatDOB, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>

    <input type="hidden" name="accentype" value="goodaccession">
    <button type="submit" style="margin-left: 20px;" class="btn btn-default btn-sm btn-dicom">Search</button>
    

    @*<button type="submit" style="margin-right:5px;margin-left:5px;" class="btn btn-default btn-sm">Search</button>*@

@*}*@
<div>
    @*@Html.ValidationMessageFor(model => model.DoctorId, "", new { @class = "text-danger" })
        @Html.ValidationMessageFor(model => model.TriageUrgencyId, "", new { @class = "text-danger" })
        @Html.ValidationMessageFor(model => model.StartDate, "", new { @class = "text-danger" })
        @Html.ValidationMessageFor(model => model.EndDate, "", new { @class = "text-danger" })
        @Html.ValidationMessageFor(model => model.Patient, "", new { @class = "text-danger" })
        @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })*@
</div>