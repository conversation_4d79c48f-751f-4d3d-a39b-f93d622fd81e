@model Cerebrum.ViewModels.HealthCardValidation.HCValidationModel
@{
    ViewBag.Title = "OhipCheck";
    //Layout = "~/Views/Shared/_LayoutDemographics.cshtml";
}

<link href="~/Content/forms.css" rel="stylesheet" />
@*<script src="../Scripts/jquery-1.10.2.min.js"></script>*@
<script src="~/Scripts/jquery-ui-1.10.4.js"></script>
<script src="~/Scripts/jquery.maskedinput.js"></script>
<link href="~/Content/themes/base/jquery-ui.css" rel="stylesheet" />
<link href="~/Content/themes/base/jquery.ui.theme.css" rel="stylesheet" />
@*<link href="~/Content/toastr.css" rel="stylesheet" />
    <script src="~/Scripts/toastr.min.js"></script>*@
<script src="~/Scripts/formGB.js"></script>
<div id="mainHC_Div">
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <div class="form-group hc_Header">
        <div class="col-md-11 ">
            HealthCard Validation
        </div>
        <div class="col-md-1">
            <button type="button"  style="margin-top:3px;display:inline-block;float:right;" class="btn btn-sm" data-dismiss="modal">X</button>
        </div>
    </div>
    @using (Html.BeginForm("OhipCheck", "HealthCardCheckGB", FormMethod.Post, new { @id = "ohip_check_id" }))
    {
        @Html.AntiForgeryToken()
        <div id="hc_div">
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.healthCard)
                </div>
                <div class="col-md-2">
                    @Html.EditorFor(model => model.healthCard, new { htmlAttributes = new { @class = "form-control", tabindex = 1 } })
                </div>
                <div class="col-md-1 rem_left_padd rem_right_padd">
                    @Html.LabelFor(model => model.feeCode)
                </div>
                <div class="col-md-6">
                    @Html.DropDownListFor(model => model.feeCodesListId, Model.feeCodesList, "Select Fee code", htmlAttributes: new { @class = "form-control", tabindex = 8 })
                    @*@Html.EditorFor(model => model.feeCode, new { htmlattributes = new { @class = "form-control", tabindex = 1 } })*@
                    @Html.HiddenFor(model => model.patientRecordId)
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-3">
                    <button type="submit" class="checkHC_Btn">Check</button>
                </div>
                <div class="col-md-9">
                    <span id="_messageId">@Html.LabelFor(m => m.msg, Model.msg, new {})</span>
                </div>
            </div>
        </div>
        //if (Model.showResult)
        //{
        <div id="resp_div" style="@(Model.showResult ? "display:block" : "display:none")">
            <div class="form-group">
                <div class="col-md-3 hc_head">
                    Response
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.responceCode)
                </div>
                <div class="col-md-2">
                    @Html.EditorFor(model => model.responceCode, new { htmlattributes = new { @class = "form-control", tabindex = 1 } })
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.description)
                </div>
                <div class="col-md-10">
                    @Html.EditorFor(model => model.description, new { htmlattributes = new { @class = "form-control max_midth", tabindex = 1 } })
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.action)
                </div>
                <div class="col-md-10">
                    @Html.EditorFor(model => model.action, new { htmlattributes = new { @class = "form-control max_midth", tabindex = 1 } })
                </div>
            </div>
        </div>
        //}
        //if (Model.showPatient)
        //{
        <div id="ptnt_div" style="@(Model.showPatient ? "display:block" : "display:none")">
            <div class="form-group">
                <div class="col-md-3 hc_head">
                    Patient
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.lastName)
                </div>
                <div class="col-md-2">
                    @Html.EditorFor(model => model.lastName, new { htmlattributes = new { @class = "form-control", tabindex = 1 } })
                </div>
                <div class="col-md-1">
                    @Html.LabelFor(model => model.dob)
                </div>
                <div class="col-md-2">
                    @Html.EditorFor(model => model.dob, new { htmlattributes = new { @class = "form-control", tabindex = 1 } })
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-2">
                    @Html.LabelFor(model => model.firstName)
                </div>
                <div class="col-md-2">
                    @Html.EditorFor(model => model.firstName, new { htmlattributes = new { @class = "form-control max_midth", tabindex = 1 } })
                </div>
                <div class="col-md-1">
                    @Html.LabelFor(model => model.ExpiryDate)
                </div>
                <div class="col-md-2">
                    @Html.EditorFor(model => model.ExpiryDateStr, new { htmlattributes = new { @class = "form-control", tabindex = 1 } })
                </div>
            </div>
        </div>
            @*}
                if (Model.showFee)
                {*@
            <div id="fee_div" style="@(Model.showFee ? "display:block" : "display:none")">
                <div class="form-group">
                    <div class="col-md-3 hc_head">
                        Fee
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-2">
                        @Html.LabelFor(model => model.feeServiceCode)
                    </div>
                    <div class="col-md-2">
                        @Html.EditorFor(model => model.feeServiceCode, new { htmlattributes = new { @class = "form-control", tabindex = 1 } })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-2">
                        @Html.LabelFor(model => model.feeDate)
                    </div>
                    <div class="col-md-2">
                        @Html.EditorFor(model => model.feeDate, new { htmlattributes = new { @class = "form-control", tabindex = 1 } })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-2">
                        @Html.LabelFor(model => model.feeCode)
                    </div>
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.feeCode, new { htmlattributes = new { @class = "form-control", tabindex = 1 } })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-2">
                        @Html.LabelFor(model => model.feeDescription)
                    </div>
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.feeDescription, new { htmlattributes = new { @class = "form-control max_midth", tabindex = 1 } })
                    </div>
                </div>
            </div>
        //}
        <div class="modal-footer">
            @Html.HiddenFor(model => model.Gender)
            @Html.HiddenFor(model => model.GenderStr)
            <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
        </div>
    }
</div>














@*@using (Html.BeginForm("NewPatient", "DemographicsGB", FormMethod.Post, new { demographicsViewModel = Model }))
    {
        <div class="form-group">
            <div class="col-md-2">
                @Html.EnumDropDownListFor(model => model.ohipType, htmlAttributes: new { @class = "form-control", tabindex = 31 })
            </div>
        </div>
    }*@






