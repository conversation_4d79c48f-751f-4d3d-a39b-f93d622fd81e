@model IEnumerable<Cerebrum.ViewModels.Practice.VMPractice>

@{
    ViewBag.Title = "Practice";
    Layout = "~/Views/Shared/_LayoutFixed.cshtml";
}

<h2>Practices</h2>

<p>
    @Html.ActionLink("Create New", "Create")
</p>
<table class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.PracticeName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.PracticeNumber)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.PracticeFolder)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.OntarioHealthClientId)
        </th>
        <th></th>

    </tr>

    @foreach (var item in Model)
    {
        <tr>
            <td>
                @item.PracticeName
            </td>
            <td>
                @item.PracticeNumber
            </td>
            <td>
                @item.PracticeFolder
            </td>
            <td>
                @item.OntarioHealthClientId
            </td>
            <td>
                @Html.ActionLink("Edit", "Edit", new { id = item.Id }) |
                @Html.ActionLink("Preset", "PresetInit", new { id = item.Id })
            </td>
        </tr>
    }

</table>
<br />
<br />