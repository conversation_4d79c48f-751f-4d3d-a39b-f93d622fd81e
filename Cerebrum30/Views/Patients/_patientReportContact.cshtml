@model Cerebrum.ViewModels.Common.VMExternalDoctorReportContact

@{ 
    var refreshApp = Model.RefreshAppointment ? 1 : 0;
    var doctorName = Model.ExternalDoctor.LastName + ", " + Model.ExternalDoctor.FirstName;
}

<style>
    #table-ref-doc-rep-addresses .expand-btn, 
    #table-ref-doc-rep-addresses .collapse-btn {
    font-size: small;
}

.tr-child-row-white-bg-rp{
        background-color:#ffffff;
    }

    .tr-child-row-white-bg-rp td{
        background-color:#ffffff;
        font-size: 11px;
        font-style:italic;
    }
</style>

@Html.ModalHeader("Doctor: "+ doctorN<PERSON> +" for " + Model.PatientName)

<div class="modal-body">
    @if (Model.AppointmentId > 0)
    {
        <div style="font-size:16px;">Appointment: @Model.AppointmentId</div>
    }

    <div class="panel panel-info content-height300">
        <div class="panel-heading">
            <h3 class="panel-title">Addresses <span class="badge cbadge">@Model.ExternalDoctor.Addresses.Count</span></h3>
        </div>
        <table data-refresh-app="@refreshApp" id="table-ref-doc-rep-addresses" class="table tbl-parent-child-rows">
            <thead>
                <tr>
                    <th></th>
                    <th>
                        Address
                    </th>    
                    <th>
                        Phone/Fax Numbers
                    </th>   
                    <th>
                        Active
                    </th> 
                    <th></th>              
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.ExternalDoctor.Addresses)
                {
                    bool showExpandLink = item.PhoneNumbers.Any() ? true : false;
                    bool isSelected = item.Id == Model.ExternalDoctorAddressId ? true : false;                    
                    string disabled = isSelected ? "disabled" : "";
                    string btnText = isSelected ? "Selected" : "Select";
                    var totalPhoneNumbers = item.PhoneNumbers.Count();

                    <tr id="<EMAIL>">
                        <td style="width:10px;">
                            @if (showExpandLink)
                            {<a href="#" class="collapse-btn"><small><span class="glyphicon glyphicon-minus"></span></small></a>}
                        </td>
                        <td>
                            @item.AddressLine1
                            @if (!String.IsNullOrWhiteSpace(item.AddressLine2))
                            {
                                <span> @item.AddressLine2</span>
                            }
                            @if (!String.IsNullOrWhiteSpace(item.City))
                            {
                                <span>, @item.City</span>
                            }

                            @if (!String.IsNullOrWhiteSpace(item.Province))
                            {
                                <span> @item.Province</span>
                            }

                            @if (!String.IsNullOrWhiteSpace(item.PostalCode))
                            {
                                <span> @item.PostalCode</span>
                            }
                        </td>
                        <td class="td-doc-phone-fax-num">                           
                            @if (totalPhoneNumbers > 0)
                            {
                                @totalPhoneNumbers
                            }
                            else
                            {
                                <span class="text-danger"> Please add a Fax/Phone number in the edit doctor page.</span>
                            }
                        </td>
                        <td>
                            @item.IsActive
                        </td>
                        <td style="width:15%;">                           
                           
                        </td>
                    </tr>

                    foreach(var phoneItem in item.PhoneNumbers)
                    {
                        bool isActivePhone = phoneItem.IsActive;
                        bool isPhoneSelected = Model.ExternalDoctorAddressId == item.Id && phoneItem.Id == Model.ExternalDoctorPhoneId ? true : false;
                        bool hasFax = !String.IsNullOrWhiteSpace(phoneItem.FaxNumber) ? true : false;
                        string disabledPhone = isPhoneSelected ? "disabled" : "";
                        string btnTextPhone = isPhoneSelected ? "Selected" : "Select";    
                                          

                        <tr class="tr-child-row-white-bg-rp" id="<EMAIL><EMAIL>">
                            <td></td>
                            <td colspan="2">
                                @if (!String.IsNullOrWhiteSpace(phoneItem.PhoneNumber))
                                {
                                    <span><strong>Phone:</strong> @phoneItem.PhoneNumber</span>
                                }
                                @if (!String.IsNullOrWhiteSpace(phoneItem.PhoneNumber) && !String.IsNullOrWhiteSpace(phoneItem.PhoneExtension))
                                {
                                    <span> ext. @phoneItem.PhoneExtension</span>
                                }

                                @if (hasFax)
                                {
                                    <span> <strong>Fax:</strong> @phoneItem.FaxNumber</span>
                                }

                            </td>
                            <td>
                                @phoneItem.IsActive
                            </td>
                            <td style="width:15%;">
                                @if (hasFax && isActivePhone)
                                {
                                    <form data-form-type="phone" class="frm-ref-doc-rep-phone" method="post" action="@Url.Action("SetPatientDoctorReportContact", "Patients", new { area = "" })">
                                        @Html.AntiForgeryToken()
                                        @Html.Hidden("PatientId", Model.PatientId)
                                        @Html.Hidden("ExternalDoctorId", item.ExternalDoctorId)
                                        @Html.Hidden("ExternalDoctorAddressId", item.Id)
                                        @Html.Hidden("ExternalDoctorPhoneId", phoneItem.Id)
                                        @Html.Hidden("ExternalDoctorLocationId", phoneItem.ExternalDoctorLocationId)
                                        @Html.Hidden("AppointmentId", Model.AppointmentId)
                                        @Html.Hidden("DoctorType", Model.DoctorType)
                                        @Html.Hidden("IsEditPatient", Model.IsEditPatient)
                                        <button data-item-id="@phoneItem.Id" type="submit" class="btn btn-xs btn-primary btn-set-ref-doc-rep-phone" @disabledPhone>@btnTextPhone</button>
                                    </form>

                                    @*if (isPhoneSelected)
                                    {
                                        <button data-item-id="@phoneItem.Id" type="submit" class="btn btn-xs btn-primary btn-set-ref-doc-rep-phone" @disabledPhone>@btnTextPhone</button>
                                    }*@
                                }
                            </td>
                        </tr>
                    }
                }
            </tbody>
        </table>
    </div>
</div>

@Html.ModalFooter(isInfoModal: true)
