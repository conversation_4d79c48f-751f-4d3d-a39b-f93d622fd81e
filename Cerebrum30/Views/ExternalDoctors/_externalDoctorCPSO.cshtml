@model Cerebrum30.Models.VM_Ext_CPSO
<style>

    #ext_cpso_edit input, select, button {
        height: 25px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    .ui-widget-header {
        background-color: white;
    }

    .marginTop2 {
        margin-top: 2px;
    }

    .marginBott2 {
        margin-bottom: 2px;
    }
</style>

@using (Html.BeginForm("ext_cpso", "DemographicsGB", new { area = "" }, FormMethod.Post, true, new { @id = "ext_cpso_id" }))
{
    @Html.ModalHeader("Active HRM Users")
    <div id="ext_cpso_edit" title="" style="padding-left:30px;padding-right:30px;">
        <div class="row marginTop2">
            <div class="col-md-3">
                @Html.LabelFor(model => model.ext_cpso_FullName)
            </div>
            <div class="col-md-4">
                @Html.LabelFor(x => x.ext_cpso_FullName, @Model.ext_cpso_FullName ?? "", new {})
                @*@Html.EditorFor(model => model.ext_cpso_FullName, new { htmlAttributes = new { @class = "form-control", tabindex = 1 } })*@
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-3">
                @Html.LabelFor(model => model.ext_cpso_num)
            </div>
            <div class="col-md-4">
                @Html.LabelFor(x => x.ext_cpso_num, @Model.ext_cpso_num ?? "", new {})
                @*@Html.EditorFor(model => model.ext_cpso_num, new { htmlAttributes = new { @class = "form-control", tabindex = 3 } })*@
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-3">
                @Html.LabelFor(model => model.ext_cpso_date)
            </div>
            <div class="col-md-4">
                @Html.LabelFor(x => x.ext_cpso_date, @Model.ext_cpso_date ?? "", new {})
                @*@Html.EditorFor(model => model.ext_cpso_FName, new { htmlAttributes = new { @class = "form-control", tabindex = 2 } })*@
            </div>
        </div>

        <div class="row">
            <div class="form-group">
                <div class="col-md-12 text-center green">
                    <span id="ex_cpso_msgId">@Html.LabelFor(x => x.ext_cpso_message, @Model.ext_cpso_message ?? "", new {})</span>

                </div>
            </div>
        </div>
    </div>


}


