@model IEnumerable<Cerebrum.ViewModels.Patient.VMPatientRecordMessage>
<script>
    $(document).ready(function () {
       
    });
    function TakeAction(elementId, targetid) {
        var URL = $('#'+elementId).attr('href');
        var Data = $('#'+elementId).data();
        var serializedData = JSON.stringify(Data);
        $.get({ URL, serializedData }).done(function () {
            $(targetid).addClass("done");
        });
    }

</script>

<table class="table">
    <tr>
        
        <th>
            @Html.DisplayNameFor(model => model.PatientLastName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Message)
        </th>
        <th></th>
    </tr>

@foreach (var item in Model) {
    <tr>
        <td>
            @item.PatientLastName
        </td>
        <td>
            @item.Message
        </td>
       
        <td>
            @Html.ActionLink("Mark Seen", "MessageSeen",
            new { Area = "PatientRecordMessageController" },
           new {
               id = item.Id,
               data_messageId = item.Id,
               data_patientRecordId=item.PatientRecordId,

               onclick = "TakeAction(@item.Id,'some-target');"
           }) 
        </td>
    </tr>
}

</table>
