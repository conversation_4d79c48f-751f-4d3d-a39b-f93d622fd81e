@model  Cerebrum.ViewModels.Patient.VMPatientMenu
@{
    int appointmentId = Model.AppointmentId != null && Model.AppointmentId > 0 ? (int)Model.AppointmentId : 0;
    int appReferralDoctorId = Model.AppointmentReferralDoctorId != null && Model.AppointmentReferralDoctorId > 0 ? (int)Model.AppointmentReferralDoctorId : 0;
    string spanAlertClass = Model.PatientHasAlert ? " class=text-danger"  : "";
}
<ul class="ul-patient-menu __776432 ul-patient-menu-inline">

    <li><a class="btn-edit-patient" data-el="modal-lg" href="#" data-modal-url="@Url.Action("edit","patients",new { area="", patientId = Model.PatientId, appointmentId = appointmentId, appReferralDocId = appReferralDoctorId })"><span id="patientname"></span>Edit Patient</a></li>

    <li><a class="btn-patient-appointments" data-el="modal-lg" href="#" data-modal-url="/Schedule/appointments/GetPatientsAppointmentsPage?patientid=@Model.PatientId">Appointments</a></li>

    <li><a class="" href="@Url.Action("index","patientmedications",new { area="medications", patientId = Model.PatientId  })" target="_blank">Medications</a></li>
    <li>
        @Html.ActionLink("Letter History", "VPReportHistory", "Reports", new { area = "Documents", patientID = Model.PatientId, appointmentId = appointmentId }, new { target = "_blank" })
    </li>
    <li>
        @Html.ActionLink("VP History", "VPHistory", "reports", new { area = "documents", patientID = Model.PatientId }, new { target = "_blank" })
    </li>
    <li>
        @Html.ActionLink("Additional Notes", "Notes", "Patients", new { area = "", patientId = Model.PatientId }, new { target = "_blank" })
    </li>
    <li>
        @Html.ActionLink("CPP", "CPP_Patient", "VP", new { area = "VP", PatientID = Model.PatientId }, new { target = "_blank" })
    </li>
    <li>
        @Html.ActionLink("CDF", "TemplateData", "VP", new { area = "VP", PatientID = Model.PatientId }, new { target = "_blank" })
    </li>

    <li><a class="" href="@Url.Action("index", "externaldocument", new { area="externaldocument", PatientRecordId=Model.PatientId })" target="_blank">External Documents</a></li>

    <li><a href="ContactManagers/Index?patientRecordId=@Model.PatientId" target="_blank">Contact Manager</a></li>

    <li><a class="" href="Requisition?PatientRecordId=@Model.PatientId&closePage=1" target="_blank">Flow Sheet</a></li>

    <li><a class="" config-cat="integrations_ocean-e-referral" href="externaldocument/assignment?externaldocument=ereferral&officeId=@Model.OfficeId&practiceId=@Model.Practiceid&PatientId=@Model.PatientFhirId">Service Requests</a></li>

    <li><a class="" href="Bill/report?reportType=billinghistory&patientRecordId=@Model.PatientId">Billing History</a></li>

    <li><a class="" href="Bill/report?reportType=statement&patientRecordId=@Model.PatientId">Patient Statement</a></li>

    <li><a class="btn-loose-report-upload" href="#" data-modal-url="@Url.Action("UploadLooseReport", "uploads",new { area = "documents", patientId = Model.PatientId })">Upload Loose Reports</a></li>

    <li><a class="" href="/AdminUser/PatientChart/GetPatientDataById?patientRecordId=@Model.PatientId" target="_blank">Patient Chart</a></li>

    @{
        int? varAppointmentId = 0;

        if ((@Model.AppointmentId != null) && (@Model.AppointmentId > 0))
        {

            varAppointmentId = @Model.AppointmentId;
        }

        <li><a target="_blank" href='Measurements/Measurement/OpenRawDataClassicByPatient?appointmentId=@varAppointmentId&patientID=@Model.PatientId'>Legacy Data</a></li>
    }
    @if (CerebrumUser.HasPermission("OLISUser"))
    {

        <li><a class="" href="@Url.Action("OLISReportSearch", "OLIS", new { area = "Labs", patientId = Model.PatientId })" target="_blank">OLIS Report Search</a></li>
    }
    <li><a class="" href="@Url.Action("PrintPatientLabel", "appointments", new { area = "schedule", patientRecordId = Model.PatientId })" target="_blank">Patient Label</a></li>
    <li>
        <a class="btn-view-comments c-pointer" data-cb-tp-placement="top" data-app-id="@Model.AppointmentId" data-modal-url="@Url.Action("GetComments", "Appointments", new { area = "schedule", patientId = Model.PatientId, appointmentId = appointmentId })">
            Patient Comments
        </a>
    </li>
    <li>
        <a class="btn-view-patient-alert c-pointer" data-cb-tp-placement="top" data-patient-alert-id="@Model.PatientAlertId" data-modal-url="@Url.Action("GetPatientAlertsMain", "Appointments", new { area = "schedule", patientId = Model.PatientId, appointmentId = appointmentId })">
            <span @spanAlertClass id="<EMAIL>">Patient Alert</span>
        </a>
    </li>
    @{
        var isPharmacyModuleActiveVal = System.Configuration.ConfigurationManager.AppSettings["IsPharmacyModuleActive"];
        bool isPharmacyModuleActive = string.IsNullOrEmpty(isPharmacyModuleActiveVal) ? false : Convert.ToBoolean(isPharmacyModuleActiveVal);
        if (isPharmacyModuleActive)
        {
            <li><a class="" href="@Url.Action("Pharmacies","PatientPharmacy",new { area="Pharmacy", patientId = Model.PatientId })" target="_blank">Patient Pharmacies</a></li>
        }
    }
    @{
        var eConsultActive = System.Configuration.ConfigurationManager.AppSettings["eConsultActive"];
        bool isActive = string.IsNullOrEmpty(eConsultActive) ? false : Convert.ToBoolean(eConsultActive);
        if (isActive)
        {
            if (CerebrumUser.HasRole("Access Econsult")) // change role here
            {
                <li><a href="@Url.Action("LoadPatient", "Consult", new { area = "eConsult", patientId = Model.PatientId })">Request eConsult</a></li>
            }
            if (CerebrumUser.HasPermission("View Medications"))
            {
                <li><a href="@Url.Action("PatientDrafts", "PatientConsult", new { area = "eConsult", patientId = Model.PatientId })">View eConsult Drafts</a></li>
                <li><a href="@Url.Action("Index", "PatientConsult", new { area = "eConsult", patientId = Model.PatientId })">View eConsults</a></li>
            }
        }
    }

    @{
        var eFormsActive = System.Configuration.ConfigurationManager.AppSettings["IsEFormsEnabled"];
        bool isEFormsActive = string.IsNullOrEmpty(eConsultActive) ? false : Convert.ToBoolean(eFormsActive);// do we need this isEFormsActive?
        if (isEFormsActive && CerebrumUser.IsEformsEnabled)
        {
            <li><a href="javascript:void(0);" onclick="go(0)">EForms</a></li>
            @*<li><a href="javascript:void(0);" onclick="go(1)">CCO</a></li>
                <li><a href="javascript:void(0);" onclick="go(2)">HPG</a></li>
                <li><a href="javascript:void(0);" onclick="go(3)">OTN</a></li>*@
        }
    }
</ul>

<script type="text/javascript">
    function go(typeId) {
        var params = {
            baseUrl: '@Url.Action("Go", "EForms", new { area = "EForms" })'
        };

        var queryString = $.param({ id: @Model.PatientId, type: typeId });
        var uri = params.baseUrl + '?' + queryString;
        window.open(uri, 'theWin', 'width=1024, height=780, status=no');
    }
</script>

<style type="text/css">
    .ul-patient-menu-inline {
        display: inline-block;
    }
</style>