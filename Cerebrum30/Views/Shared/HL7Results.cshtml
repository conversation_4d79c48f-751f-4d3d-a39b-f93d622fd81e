@model IEnumerable<Cerebrum.ViewModels.HL7.HL7ResultVM>
@using Cerebrum30.Utility;
@if (Model != null && Model.Count() > 0)
{

    foreach (var item in Model)
    {
        var flg = (item.abnormalFlag != null && item.abnormalFlag.Trim() != "" ? (item.abnormalFlag.Trim().Equals("N") ? "" : item.abnormalFlag) : "");
        <tr id="<EMAIL>" class="@((flg.Trim() == "" || flg.Trim().Equals("N")) ? "" : "danger")">
            <td id="<EMAIL>">
                @{
                    var url = HttpContext.Current.Request.UrlReferrer != null ? HttpContext.Current.Request.UrlReferrer.AbsoluteUri : HttpContext.Current.Request.Url.ToString();
                }
                @if (item.showResult && item.TestName != "")
                {
                    @Html.ActionLink(item.TestName, "Index", "HL7ResultChart", new { area = "labs", vid = item.HL7ReportVersionId, testCode = item.testCodeIdentifier, returnUrl = url }, new { target = "_parent", data_toggle = "tooltip", data_placement = "left", title = item.LabRequestCode })
                }
            </td>
            <td id="<EMAIL>">
                @if (item.showResult)
                {
                    @flg
                }
            </td>
            @{
                var length = string.IsNullOrWhiteSpace(item.testResult) ? 0 : item.testResult.Length;
                var lengthClass = length > 20 ? "dont-break-out fixedWidthfont" : "";
            }
            <td id="<EMAIL>" class="@lengthClass">
                <div id="<EMAIL>">
                    @if (item.showResult)
                    {
                        var result = item.testResult.FormatHL7Report();
                        @Html.Raw(result)
                    }
                    else
                    {
                        <button type="button" class="btn btn-danger" data-resultid="@item.Id" data-toggle="modal" data-target="#model-consent">Display Sensitive Information</button>
                    }
                </div>
            </td>
            <td id="<EMAIL>">
                @if (item.showResult)
                {
                    @item.refRange
                }
            </td>
            <td id="<EMAIL>">
                @if (item.showResult)
                {
                    @item.units
                }
            </td>
            <td id="<EMAIL>">
                @if (item.showResult)
                {
                    @item.resultStatus
                }
            </td>
            <td id="<EMAIL>">
                <button id="@item.Id" class="btn-HL7-Result-Note btn btn-default btn-xs printNot" data-value="@item.Id"> Add Note</button>
            </td>
            <td>
                @if (!item.showResult)
                {
                    <a href="#" data-resultid="@item.Id" data-URL="@Url.Action("HL7SensitiveResultAccessedLog", new { controller = "HL7Report", area = "Labs" })" class="btn-sensitive-accessed-info">  <span class="glyphicon glyphicon-eye-open"></span></a>
                }
            </td>
            <td>
                @if (!string.IsNullOrEmpty(item.ReportURL))
                {
                    <a href="@item.ReportURL" target="_blank" class="btn btn-default btn-xs">Download Report</a>
                }
            </td>
        </tr>

        @(await Html.PartialAsync("HL7ResultNotes", item.HL7ResultNotes));
        
                }


            }
            else
            {
                    <hr />
                    <p style="color:#da7373">Result not found</p>
                }