@model Cerebrum30.Models.VM_Ext_Phone
<style>
    #ext_phone_List td {
        padding-bottom: 0px;
        padding-top: 0px;
        border-bottom: 1px solid #ddd;
        font-weight: 700;
    }

        #ext_phone_List td:hover {
            background-color: gainsboro;
            cursor: pointer;
        }

    #ext_phone_dialog_edit input, select, button {
        height: 25px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    .ui-widget-header {
        background-color: white;
    }

    .marginTop2 {
        margin-top: 2px;
    }

    .marginBott2 {
        margin-bottom: 2px;
    }
</style>

@using (Html.BeginForm("ext_phone", "DemographicsGB", new { area = "" }, FormMethod.Post, true, new { @id = "ext_phone_id" }))
{
    @Html.ModalHeader("Add Phone")
    @Html.HiddenFor(x => x.ex_p_type)
    @Html.HiddenFor(x => x.ex_p_id)
    @Html.HiddenFor(x => x.ex_p_docId)
    <div id="ext_phone_dialog_edit" title=""  style="padding-left:30px;padding-right:30px;">
        <div class="row marginTop2">
            <div class="col-md-4">
                @Html.LabelFor(model => model.ext_phoneNumber)
            </div>
            <div class="col-md-4">
                @Html.EditorFor(model => model.ext_phoneNumber, new { htmlAttributes = new { @class = "form-control ext_phone",autocomplete="off", tabindex = 1 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-4">
                @Html.LabelFor(model => model.ext_extention)
            </div>
            <div class="col-md-2">
                @Html.EditorFor(model => model.ext_extention, new { htmlAttributes = new { @class = "form-control numbersOnly", autocomplete = "off", tabindex = 2 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-4">
                @Html.LabelFor(model => model.ext_faxNumber)
            </div>
            <div class="col-md-3">
                @Html.EditorFor(model => model.ext_faxNumber, new { htmlAttributes = new { @class = "form-control ext_fax", autocomplete = "off", tabindex = 3 } })
            </div>
        </div>
        <div class="row marginTop2">
            <div class="col-md-4">
                @Html.LabelFor(model => model.ext_typeOfPhoneNumber)
            </div>
            <div class="col-md-2">
                @Html.EnumDropDownListFor(model => model.ext_typeOfPhoneNumber, htmlAttributes: new { @class = "form-control", tabindex = 4 })
            </div>
        </div>

        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-12 text-center green">
                    <span id="ex_p_messageId">@Html.LabelFor(x => x.ex_p_message, @Model.ex_p_message ?? "", new {})</span>

                </div>
            </div>
        </div>
        <div class="row marginTop2" style="border-bottom:1px solid #e2dada;">
            <div class="form-group">
                <div class="col-md-2 text-left">
                    <button  class="btn btn-default marginTop2 marginBott2" type="button" id="newExPhone">New</button>
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button  class="btn btn-default marginTop2 marginBott2" type="submit" id="ex_phone_submit">Save</button>
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-1">

                </div>
                <div class="col-md-11 pull-left">
                    <table class="table" id="ext_phone_List" name="ext_phone_List" style="font-size: 12px;margin-bottom: 0px;">
                        @if (Model != null && Model.ex_p_List != null && Model.ex_p_List.Count > 0)
                {
                            <thead>
                                <tr class="vertical-center">
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.ex_p_List.Count; i++)
                                {
                                    <tr>
                                        <td class="exPhList">
                                            @Html.DisplayFor(model => model.ex_p_List[i].name)
                                            @Html.HiddenFor(model => model.ex_p_List[i].id, new { @id = Model.ex_p_List[i].id })
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        }
                    </table>
                </div>

            </div>
        </div>
    </div>


}

