@model Cerebrum30.Models.VM_RefDoctor
<style>
    #ref_doc__List td {
        padding-bottom: 0px;
        padding-top: 0px;
        border-bottom: 1px solid #ddd;
        font-weight: 700;
    }

        #ref_doc__List td:hover {
            background-color: gainsboro;
            cursor: pointer;
        }

    #ref_doctor_dialog input, select, button {
        height: 25px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

    .ui-widget-header {
        background-color: white;
    }

    .marginTop2 {
        margin-top: 2px;
    }

    .marginBott2 {
        margin-bottom: 2px;
    }
</style>

@using (Html.BeginForm("ref_doctor_", "DemographicsGB", new { area = "" }, FormMethod.Post, true, new { @id = "ref_doctor_id" }))
{
    @Html.ModalHeader("Add Referal Doctor")
    <div class="modal-body">
        @Html.HiddenFor(x => x.rd_type)
        @Html.HiddenFor(x => x.rd_exDocId)
        @Html.HiddenFor(x => x.rd_patientId)
        @Html.HiddenFor(x => x.saved_ref_doc_name)
        <div id="ref_doctor_dialog" title="Add Referal Doctor"  style="z-index:9999;">
            <div class="form-group">
                <div class="col-md-4">
                    @Html.LabelFor(model => model.referal_doctor_name,@Model.referal_doctor_name??"", new {})
                </div>
                <div class="col-md-4">
                    @Html.EditorFor(model => model.referal_doctor_name, new { htmlAttributes = new { @class = "form-control dem_phone demographic-doctors", tabindex = 1 } })
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <div class="col-md-12 text-center green">
                        <span id="dem_p_messageId">@Html.LabelFor(x => x.message_rd, @Model.message_rd ?? "", new {})</span>

                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <div class="col-md-1">

                    </div>
                    <div class="col-md-11 pull-left">
                        <table class="table" id="ref_doc__List" name="ref_doc__List" style="font-size: 12px;">
                            @if (Model != null && Model.rd_List != null && Model.rd_List.Count > 0)
                {
                                <tbody>
                                    @for (int i = 0; i < Model.rd_List.Count; i++)
                                    {
                                        <tr>
                                            <td class="ref_d_List">
                                                @Html.DisplayFor(model => model.rd_List[i].name)
                                            </td>
                                            <td class="ref_d_List">
                                                @Html.DisplayFor(model => model.rd_List[i].value)                                        
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            }
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-default btn-sm ref-doc-cl" data-dismiss="modal">Close</button>
    </div>
}



