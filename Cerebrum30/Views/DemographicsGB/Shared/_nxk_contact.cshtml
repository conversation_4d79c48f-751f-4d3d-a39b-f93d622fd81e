@model Cerebrum30.Models.NextOfKeen
<style>
    #conntactList td {
        padding-bottom: 0px;
        padding-top: 0px;
        border-bottom: 1px solid #ddd;
        font-weight: 700;
    }

        #conntactList td:hover {
            background-color: gainsboro;
            cursor: pointer;
        }

    #nxk_dialog input, select, button {
        height: 25px !important;
        padding-top: 0px !important;
        padding-bottom: 0px !important;
    }

        #nxk_dialog_list input {
        height: 15px !important;
    }

    .ui-widget-header {
        background-color: white;
    }

    .marginTop2 {
        margin-top: 2px;
    }

    .marginBott2 {
        margin-bottom: 2px;
    }

    .t_a {
        height: 50px !important;
    }

    .chb_corr span {
        margin-top: 7px;
        display: inline-block;
    }
</style>
@using (Html.BeginForm("nkx_contact", "DemographicsGB", new { area = "" }, FormMethod.Post, true, new { @id = "nkx_contact_id" }))
{
    @Html.ModalHeader("Add Contact")
    @Html.HiddenFor(x => x.nxk_type)
    @Html.HiddenFor(x => x.nxk_id)
    @Html.HiddenFor(x => x.nxk_patientId)
    @Html.HiddenFor(x => x.nxk_contactPurposeEnum_A)
    @Html.HiddenFor(x => x.nxk_firstName_A)
    @Html.HiddenFor(x => x.nxk_lastName_A)
    @Html.HiddenFor(x => x.nxk_phone_A)
    <div id="nxk_dialog" title="Add Next Of Kin" style="padding-left:40px;padding-right:40px;">
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    First Name
                </div>
                <div class="col-md-5">
                    @Html.EditorFor(model => model.nxk_firstName, new { htmlAttributes = new { @class = "form-control", tabindex = 2 } })
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Middle Name
                </div>
                <div class="col-md-5">
                    @Html.EditorFor(model => model.nxk_middleName, new { htmlAttributes = new { @class = "form-control", tabindex = 3 } })
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Last Name
                </div>
                <div class="col-md-8">
                    @Html.EditorFor(model => model.nxk_lastName, new { htmlAttributes = new { @class = "form-control", tabindex = 4 } })
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Contact Purpose
                </div>
                <div class="col-md-8">
                    @Html.EnumDropDownListFor(model => model.nxk_contactPurposeEnum, htmlAttributes: new { @class = "form-control", tabindex = 1 })
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Email
                </div>
                <div class="col-md-4">
                    @Html.EditorFor(model => model.nxk_emailAddress, new { htmlAttributes = new { @class = "form-control", tabindex = 5 } })
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Phone
                </div>
                <div class="col-md-4">
                    @Html.EditorFor(model => model.nxk_phone, new { htmlAttributes = new { @class = "form-control nxk_c nxk_c_ph", @readonly = "readonly", tabindex = 6 } })
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-default btn-sm add_nxk_p" style="height:25px;margin-left:1px;">Phone</button>
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Notes
                </div>
                <div class="col-md-4">
                    @Html.TextAreaFor(x => x.nxk_notes, 20, 15, new { @class = "form-control text-box multy-line t_a", tabindex = 7 })
                </div>
                <div class="col-md-4 text_right">
                    <div class="col-md-4">
                        <div class="checkbox">
                            <label class="chb_corr">
                                @Html.EditorFor(model => model.IsActive) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsActive)</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="checkbox">
                            <label class="chb_corr">
                                @Html.EditorFor(model => model.IsRemoved) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsRemoved)</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="checkbox">
                            <label class="chb_corr">
                                @Html.EditorFor(model => model.showAll) <span class="checkbox-text">@Html.DisplayNameFor(model => model.showAll)</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-12 text-center green">
                    <span id="nkx_messageId">@Html.LabelFor(x => x.nxk_message, @Model.nxk_message ?? "", new {})</span>

                </div>
            </div>
        </div>
        <div class="row marginTop2" style="border-bottom:1px solid #e2dada;">
            <div class="form-group">
                <div class="col-md-2 text-left">
                    <button class="btn btn-default marginTop2 marginBott2" type="button" id="newContact">New</button>
                </div>
                <div class="col-md-8">
                </div>
                <div class="col-md-2 text-right">
                    <button class="btn btn-default marginTop2 marginBott2" type="submit" id="newContact_submit">Save</button>
                </div>
            </div>
        </div>
        <div class="row marginTop2">
            <div class="form-group">
                <div class="col-md-1">

                </div>
                <div id="nxk_dialog_list" class="col-md-11 pull-left">
                    <table class="table" id="conntactList" name="conntactList" style="font-size: 12px;">
                        @if (Model != null && Model.nxksList != null && Model.nxksList.Count > 0)
                        {
                            <thead>
                                <tr class="vertical-center" style="background-color:#dddddd;">
                                    <th width="30%">Contact Purpose</th>
                                    <th width="30%">Name</th>
                                    <th width="20%">Phone</th>
                                    <th width="10%">Type</th>
                                    <th width="10%">Prime</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.nxksList.Count; i++)
                                {
                                    <tr>
                                        <td class="contactList">
                                            @Html.DisplayFor(model => model.nxksList[i].purpose)
                                            @Html.HiddenFor(model => model.nxksList[i].id, new { @id = Model.nxksList[i].id })
                                        </td>
                                        <td class="contactList">
                                            @Html.DisplayFor(model => model.nxksList[i].name)
                                            @Html.HiddenFor(model => model.nxksList[i].id, new { @id = Model.nxksList[i].id })
                                        </td>
                                        <td class="contactList">
                                            @Html.DisplayFor(model => model.nxksList[i].phone)
                                            @Html.HiddenFor(model => model.nxksList[i].id, new { @id = Model.nxksList[i].id })
                                        </td>
                                        <td class="contactList">
                                            @Html.DisplayFor(model => model.nxksList[i].phoneType)
                                            @Html.HiddenFor(model => model.nxksList[i].id, new { @id = Model.nxksList[i].id })
                                        </td>
                                        <td class="contactList">
                                            @Html.DisplayFor(model => model.nxksList[i].IsActive)
                                            @Html.HiddenFor(model => model.nxksList[i].id, new { @id = Model.nxksList[i].id })
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        }
                    </table>
                </div>

            </div>
        </div>
    </div>


}