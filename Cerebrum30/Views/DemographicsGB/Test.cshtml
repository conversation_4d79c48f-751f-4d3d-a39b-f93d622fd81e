
@{
    ViewBag.Title = "Test";
}

<h2>Test</h2>/schedule/daysheet
@Html.ActionLink("New Demographics", "NewPatient", "DemographicsGB", new { practiceId = "1" }, null)
@Html.ActionLink("Demographics By Id", "NewPatientEdit", "DemographicsGB", new { demographicId = "1" }, null)<br />
@Html.ActionLink("Daysheet", "index", "daysheet", new { area = "Schedule" }, null)<br /><br /><br />

@*@Html.ActionLink("Edit Patient", "NewPatient", "DemographicsGB", new { practiceId = "1" }, null)*@