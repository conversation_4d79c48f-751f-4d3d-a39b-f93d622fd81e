@model Cerebrum30.Models.DemographicsViewModel
@{
    ViewBag.Title = "Demographics";
    Layout = "~/Views/Shared/_LayoutDemographics.cshtml";
}
<link href="~/Content/forms.css" rel="stylesheet" />
@*<script src="../Scripts/jquery-1.10.2.min.js"></script>*@
@*<script src="~/Scripts/jquery-ui-1.10.4.js"></script>*@
<script src="~/Scripts/jquery-3.1.0.min.js"></script>
<script src="~/Scripts/jquery-ui-1.12.1.min.js"></script>
<script src="~/Scripts/jquery.maskedinput.js"></script>
<link href="~/Content/themes/base/jquery-ui.css" rel="stylesheet" />
<link href="~/Content/themes/base/jquery.ui.theme.css" rel="stylesheet" />
@*<link href="~/Content/toastr.css" rel="stylesheet" />
<script src="~/Scripts/toastr.min.js"></script>*@
@*<script src="~/Scripts/formGB.js"></script>*@
<script src="~/Scripts/cerebrum3-demographics.js"></script>
<div id="mainDiv">
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <div class="form-group navBar_">
        <div class="btn-group">
            <button type="button" data-toggle="dropdown" class="btn btn-default dropdown-toggle buttonHeader">Previous Tests <span class="caret"></span></button>
            <ul class="dropdown-menu">
                <li><a href="#">Action</a></li>
                <li><a href="#">Another action</a></li>
                <li class="divider"></li>
                <li><a href="#">Separated link</a></li>
            </ul>
        </div>
        <div class="dropdown forZ">
            @*<button type="button" data-toggle="dropdown" class="btn btn-default dropdown-toggle buttonHeader">Available Actions <span class="caret"></span></button>*@
            <button type="button" class="btn btn-default dropbtn buttonHeader">Available Actions <span class="caret"></span></button>
            <div class="dropdown-content">
                @*<a href="#">Medication.History</a>*@
                @Html.ActionLink("Medication.History", "index", "patientmedications", new { area = "medications", patientId = @Model.patientRecordId }, new { target = "_blank" })
                <a href="#">Test History</a>
                @Html.ActionLink("External Reports", "Index", "ExternalDocument", new { PatientRecordId = @Model.patientRecordId }, new { target = "_blank" })
                @*<a href="#">External Reports</a>*@
                @Html.ActionLink("NEW Contact Manager", "NewTask", "ContactManagers", new { PatientRecordId = @Model.patientRecordId }, new { target = "_blank" })
                @*<a href="#">NEW Contact Manager</a>*@
                <a href="#">Billing History</a>
                <a href="#">Upload Loose Report</a>
                <a href="#">Letter History</a>
                <a href="#">Alert Manager</a>
                <a href="#">Patient Statement</a>
                <a href="#">Prosthetic Valves</a>
                <a href="#">Upcoming Appointments</a>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="col-md-1">
            <div id="photoId" style="background-color:brown; height:60px; width:60px;">
                <img src="@Url.Content(Model.picture)" alt="Image" style="height:60px; width:60px;" />
            </div>
        </div>
        <div class="col-md-3">
            <h2>Demographics</h2>
        </div>
        <div class="col-md-8">
            @*@Model.ohipInfo*@
            @Html.LabelFor(m => m.ohipInfo, Model.ohipInfo, new {})
        </div>
    </div>

    @using (Html.BeginForm("NewPatientEdit", "DemographicsGB", FormMethod.Post, new { demographicsViewModel = Model }))
    {
        <div class="form-group" style="position:relative;">
            <div class="flow_" style="margin-left:15px; font-weight:700;">
                OHIP #
            </div>
            <div id="ohipDiv" class="flow_ width_50" style="margin-left:102px;">
                @Html.EditorFor(model => model.ohip, new { htmlAttributes = new { @class = "form-control backGreen numbersOnly", maxlength = "10", autocomplete = "off", tabindex = 1 } })
                <span id="ohip_str" class="" style="font-size:8px;color:red;" hidden>*Required(10)</span>
            </div>

            <div class="flow_">
                @Html.LabelFor(model => model.version)
            </div>

            <div class="flow_  width_50">
                @Html.EditorFor(model => model.version, new { htmlAttributes = new { @class = "form-control backGreen1", maxlength = "2", tabindex = 4 } })
            </div>
            <div class="flow_">
                @Html.LabelFor(model => model.skipOHIPCheck)
            </div>
            <div class="flow_">
                @Html.EditorFor(x => x.skipOHIPCheck, new { htmlAttributes = new { tabindex = 5 } })
            </div>
            <div class="flow_ ohipCall" style="margin-left:30px;">
                <input type="submit" value="Call OHIP" class="btn btn-default" style="width:100px; background-color:rgb(200, 206, 210);" />
            </div>
            <div class="flow_" style="font-weight:700;">
                Payment
            </div>
            <div class="flow_" style="width:100px;">
                @Html.EnumDropDownListFor(model => model.payment, htmlAttributes: new { @class = "form-control", tabindex = 29 })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.lastName)
            </div>
            <div class="col-md-4 rem_right_padd">
                @Html.EditorFor(model => model.lastName, new { htmlAttributes = new { @class = "form-control backGreen", tabindex = 6 } })
                @Html.ValidationMessageFor(model => model.lastName, "", new { @class = "text-danger" })
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.mainDoctor)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-8 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.mainDoctor, new { htmlAttributes = new { @class = "form-control", tabindex = 30 } })
                        @Html.HiddenFor(model => model.mainDoctorHid)
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd enrlClk">
                        <div id="mainDocEnrlEdit" class="newBtDiv"><span class="newBut">Enroll</span></div>
                        @*<div id="mainDocEnrlEdit___" class="newBtDiv"><span class="newBut">Enroll</span></div>*@
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd">
                        <div id="mainDocEdit" class="newBtDiv">
                            <span class="newBut">
                                <span class="newBut removeAncorLine">
                                    @Html.ActionLink("New", "Index", "AdminUser/AdminUsers", new { }, new { target = "_blank", id = "mrp_link_e", @class = Model.isAdminRole })
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.namePrefix)
            </div>
            <div class="col-md-4">
                @Html.EnumDropDownListFor(model => model.namePrefix, htmlAttributes: new { @class = "form-control salutation", tabindex = 7 })
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.famDoctor)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-10 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.famDoctor, new { htmlAttributes = new { @class = "form-control backGreen", tabindex = 31 } })
                        @Html.HiddenFor(model => model.famDoctorHid)
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd">
                        <div id="famDocEdit" class="newBtDiv"><span class="newBut">New</span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.firstName)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-5 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.firstName, new { htmlAttributes = new { @class = "form-control", tabindex = 8 } })
                    </div>
                    <div class="col-md-3 rem_right_padd rem_left_padd">
                        @Html.LabelFor(model => model.middleName)
                    </div>
                    <div class="col-md-4 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.middleName, new { htmlAttributes = new { @class = "form-control", tabindex = 9 } })
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.refDoctor)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-10 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.refDoctor, new { htmlAttributes = new { @class = "form-control", tabindex = 32 } })
                        @Html.HiddenFor(model => model.refDoctorHid)
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd">
                        <div id="refDocEdit" class="newBtDiv"><span class="newBut">New</span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.dateOfBirth)
            </div>
            <div class="col-md-2 rem_right_padd">
                @Html.EditorFor(model => model.dateOfBirth, new { htmlAttributes = new { @class = "form-control backGreen", tabindex = 10 } })
                @Html.ValidationMessageFor(model => model.dateOfBirth, "", new { @class = "text-danger" })
            </div>
            <div class="col-md-1 rem_right_padd">
                @Html.LabelFor(model => model.gender)
            </div>
            <div class="col-md-1 rem_right_padd">
                @Html.EnumDropDownListFor(model => model.gender, htmlAttributes: new { @class = "form-control", tabindex = 11 })
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.assosDoctor)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-10 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.assosDoctor, new { htmlAttributes = new { @class = "form-control", tabindex = 33 } })
                        @Html.HiddenFor(model => model.assosDoctorHid)
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd">
                        <div id="assosDocEdit" class="newBtDiv"><span class="newBut">New</span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.phone)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-5 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.phone, new { htmlAttributes = new { @class = "form-control", tabindex = 12 } })
                    </div>
                    <div class="col-md-2 rem_right_padd">
                        @Html.LabelFor(model => model.extentionPhone)
                    </div>
                    <div class="col-md-5 rem_right_padd">
                        @Html.EditorFor(model => model.extentionPhone, new { htmlAttributes = new { @class = "form-control", tabindex = 13 } })
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.issueDate)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-4 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.issueDate, new { htmlAttributes = new { @class = "form-control", tabindex = 34 } })
                    </div>
                    <div class="col-md-4 rem_right_padd">
                        @Html.LabelFor(model => model.validDate)
                    </div>
                    <div class="col-md-4 rem_right_padd">
                        @Html.EditorFor(model => model.validDate, new { htmlAttributes = new { @class = "form-control", tabindex = 35 } })
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.phoneContact)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-5 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.phoneContact, new { htmlAttributes = new { @class = "form-control", tabindex = 14 } })
                    </div>
                    <div class="col-md-2 rem_right_padd">
                        @Html.LabelFor(model => model.extentionContact)
                    </div>
                    <div class="col-md-5 rem_right_padd">
                        @Html.EditorFor(model => model.extentionContact, new { htmlAttributes = new { @class = "form-control", tabindex = 15 } })
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.expairyDate)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-4 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.expairyDate, new { htmlAttributes = new { @class = "form-control", tabindex = 36 } })
                    </div>
                    <div class="col-md-2 ohipCall">
                    </div>
                    <div class="col-md-6 ohipCall rem_left_padd rem_right_padd">
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.faxPharmacy)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-5 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.faxPharmacy, new { htmlAttributes = new { @class = "form-control", tabindex = 16 } })
                    </div>
                    <div class="col-md-2 rem_right_padd">
                        @Html.LabelFor(model => model.cell)
                    </div>
                    <div class="col-md-5 rem_right_padd">
                        @Html.EditorFor(model => model.cell, new { htmlAttributes = new { @class = "form-control", tabindex = 17 } })
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.ConsentEmail)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-4 rem_right_padd rem_left_padd">
                        @Html.EnumDropDownListFor(model => model.ConsentEmail, htmlAttributes: new { @class = "form-control", tabindex = 37 })
                    </div>
                    <div class="col-md-3 rem_right_padd">
                        @Html.LabelFor(model => model.email)
                    </div>
                    <div class="col-md-5 rem_right_padd">
                        @Html.EditorFor(model => model.email, new { htmlAttributes = new { @class = "form-control", tabindex = 38 } })
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.address)
            </div>
            <div class="col-md-4 rem_right_padd">
                @Html.EditorFor(model => model.address, new { htmlAttributes = new { @class = "form-control", tabindex = 18 } })
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.kin)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-5 rem_right_padd rem_left_padd">
                        @Html.EditorFor(model => model.kin, new { htmlAttributes = new { @class = "form-control", tabindex = 39 } })
                    </div>
                    <div class="col-md-1 rem_right_padd rem_left_padd">
                        @Html.LabelFor(model => model.kin_phone)
                    </div>
                    <div class="col-md-6 rem_right_padd rem_left_padd">
                        <div class="form-group">
                            <div class="col-md-9 rem_right_padd rem_left_padd">
                                @Html.EditorFor(model => model.kin_phone, new { htmlAttributes = new { @class = "form-control", tabindex = 40 } })
                            </div>
                            <div class="col-md-3 rem_right_padd rem_left_padd">
                                <div id="nxtkDid" class="newBtAdd"><span class="newBut">Add</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.city)
            </div>
            <div class="col-md-2 rem_right_padd">
                @Html.EditorFor(model => model.city, new { htmlAttributes = new { @class = "form-control", tabindex = 19 } })
            </div>
            <div class="col-md-1 rem_right_padd paddL3">
                @Html.LabelFor(model => model.province)
            </div>
            <div class="col-md-1 rem_left_padd rem_right_padd">
                @Html.EnumDropDownListFor(model => model.province, htmlAttributes: new { @class = "form-control", tabindex = 20 })
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.active)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-5 rem_right_padd rem_left_padd">
                        @Html.EnumDropDownListFor(model => model.active, htmlAttributes: new { @class = "form-control", tabindex = 41 })
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd">
                    </div>
                    <div class="col-md-5 rem_right_padd">
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.postalCode)
            </div>
            <div class="col-md-1 rem_right_padd">
                @Html.EditorFor(model => model.postalCode, new { htmlAttributes = new { @class = "form-control", tabindex = 21 } })
            </div>
            <div class="col-md-1 rem_right_padd">
                @Html.LabelFor(model => model.country)
            </div>
            <div class="col-md-2 rem_right_padd">
                @Html.EnumDropDownListFor(model => model.country, htmlAttributes: new { @class = "form-control", tabindex = 22 })
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.PreferedLanguage)
            </div>
            <div class="col-md-4 rem_right_padd">
                <div class="form-group">
                    <div class="col-md-5 rem_right_padd rem_left_padd">
                        @Html.EnumDropDownListFor(model => model.PreferedLanguage, htmlAttributes: new { @class = "form-control", tabindex = 42 })
                    </div>
                    <div class="col-md-5 rem_right_padd rem_left_padd">
                        @Html.LabelFor(model => model.PreferredOfficialLanguageSpecified)
                    </div>
                    <div class="col-md-2 rem_right_padd">
                        @Html.EditorFor(x => x.PreferredOfficialLanguageSpecified, new { htmlAttributes = new { tabindex = 43 } })
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-2">
                @Html.LabelFor(model => model.mrnHospital)
            </div>
            <div class="col-md-2 rem_right_padd">
                @Html.DropDownListFor(model => model.SelectedHospitalId, Model.hospitalsList, "", new { @class = "form-control", tabindex = 23 })
            </div>
            <div class="col-md-2 rem_right_padd">
                @Html.EditorFor(model => model.mrnCode, new { htmlAttributes = new { @class = "form-control", @placeholder = "MRN", tabindex = 24 } })
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.notes)
            </div>
            <div class="col-md-4 rem_right_padd">
                @Html.TextAreaFor(x => x.notes, 20, 15, new { @class = "form-control text-box multy-line txtHeight", tabindex = 44 })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-6 padding0">
                <div class="form-group">
                    <div class="col-md-4">
                        @Html.LabelFor(model => model.RMB)
                    </div>
                    <div class="col-md-4 rem_right_padd">
                        @Html.EditorFor(model => model.RMB, new { htmlAttributes = new { @class = "form-control backGreen", @placeholder = "MRB", tabindex = 25 } })
                    </div>
                    <div class="col-md-2 rem_right_padd">
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-4">
                        @Html.LabelFor(model => model.insurance)
                    </div>
                    <div class="col-md-4 rem_right_padd">
                        @Html.EnumDropDownListFor(model => model.insurance, htmlAttributes: new { @class = "form-control", tabindex = 26 })
                    </div>
                    <div class="col-md-4 rem_right_padd">
                        @Html.DropDownListFor(model => model.SelectedInsurCompanyId, Model.insuranceCompanyList, "", new { @class = "form-control", tabindex = 27 })
                    </div>
                </div>
            </div>
            <div class="col-md-6 padding0">
                <div class="form-group">
                    <div class="col-md-2">
                        <div id="cohortP_Id" class="newBtDiv" style="width:70px;"><span class="newBut">Add Cohort</span></div>
                    </div>
                    <div class="col-md-4 rem_right_padd">
                        <div id="ss" class="scroll_div ph_provider_1 checkboxes">
                            @if (Model.addedCohorts != null && Model.addedCohorts.Count > 0)
                            {
                                foreach (var item in Model.addedCohorts)
                                {
                                    <label><input checked class='sss_check' type='checkbox' id=@item.value /> <span> @item.text </span></label>
                                }
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group footer_ margV1">
            @*@using (Html.BeginForm("Save", "DemographicsGB", FormMethod.Post, new { demographicsViewModel = Model }))
                {*@
            <div class="col-md-offset-2 col-md-4">
                <input id="submitBtn" type="submit" value="Save" class="btn btn-default" />
            </div>
            @*}*@
            <div class="col-md-offset-2 col-md-4">
                @*<input type="submit" value="Next" class="btn btn-default" />*@
            </div>
        </div>

        @*<div class="abo" style="width:30px;height:50px;background-color:aqua;">nxk</div><br />
                    <div id="dialog_cont" style="z-index:1000;"></div>
                    <div id="dialog_cont_ph" style="z-index:1010;"></div>
                    <div id="dialog_enroll" style="z-index:1000;"></div>
                    <div id="dialog_ext_addr" style="z-index:1000;"></div>
                    <div id="dialog_ext_phone" style="z-index:1000;"></div>
                    <div id="dialog_dem_addr" style="z-index:1000;"></div>
                    <div id="dialog_dem_phone" style="z-index:1000;"></div>
                    <div class="ext_addr1" style="width:50px;height:30px;background-color:blue;">ex add</div><br />
                    <div class="ext_phone1" style="width:50px;height:30px;background-color:chartreuse;">ex ph</div><br />
                    <div class="dem_addr1" style="width:50px;height:30px;background-color:burlywood;">dem add</div><br />
                    <div class="dem_phone1" style="width:50px;height:30px;background-color:blueviolet;">dem ph</div><br />*@



        @Html.HiddenFor(x => x.practiceId)
        @Html.HiddenFor(x => x.patientRecordId)
        @Html.HiddenFor(x => x.Id)
        @Html.HiddenFor(model => model.nxk_contactPurpose)
        @Html.HiddenFor(model => model.nxk_firstName)
        @Html.HiddenFor(model => model.nxk_middleName)
        @Html.HiddenFor(model => model.nxk_lastName)
        @Html.HiddenFor(model => model.nxk_emailAddress)
        @Html.HiddenFor(model => model.nxk_notes)
        @Html.HiddenFor(model => model.nxk_phone)

        @Html.HiddenFor(model => model.ed_enrolledHid)
        @Html.HiddenFor(model => model.ed_enrollmentStatusSpecifiedHid)
        @*@Html.HiddenFor(model => model.ed_enrollmentDate)*@
        @Html.HiddenFor(model => model.ed_enrollmentDateSpecifiedHid)
        @*@Html.HiddenFor(model => model.ed_enrollmentTerminationDate)*@
        @Html.HiddenFor(model => model.ed_enrollmentTerminationDateSpecifiedHid)
        @*@Html.HiddenFor(model => model.ed_terminationReason)*@
        @Html.HiddenFor(x => x.ed_terminationReasonHid)
        @Html.HiddenFor(model => model.ed_terminationReasonSpecifiedHid)
        @Html.HiddenFor(x => x.isNewEnrollmentHid)
        @Html.HiddenFor(x => x.ed_id)

        @Html.HiddenFor(x => x.ext_addressLine1)
        @Html.HiddenFor(x => x.ext_faxNumber)
        @Html.HiddenFor(x => x.ext_city)
        @Html.HiddenFor(x => x.ext_postalCode)
        @Html.HiddenFor(x => x.ext_province)
        @Html.HiddenFor(x => x.ext_country)
        @Html.HiddenFor(x => x.assosDoctorsListStr)

    }
    <div id="ex_d_dialog_edit" title="Add External Doctor" doctor_id="">
        <input id="ex_practiceId" type="hidden" />
        <input id="ex_assosDoctorsListStr" type="hidden" />
        <div class="form-group">
            <div class="col-md-3">
                <input type="hidden" id="ex_doctorId" />
                <span>HIN</span>
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ex_d_OHIP" name="ex_d_OHIP" tabindex="1000">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>Last Name</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="ex_d_lastName" name="ex_d_lastName" tabindex="1001">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>First Name</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="ex_d_firstName" name="ex_d_firstName" tabindex="1002">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>Middle Name</span>
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="ex_d_middleName" name="ex_d_middleName" tabindex="1003">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>Phone</span>
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control" id="ex_d_phone" name="ex_d_phone" tabindex="1004">
                <input id="ex_doc_phone_id" type="hidden" />
            </div>
            <div class="col-md-1">
                <span>Ext.</span>
            </div>
            <div class="col-md-2">
                <input type="text" class="form-control" id="ex_d_ext" name="ex_d_ext" tabindex="1005">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <div class="col-md-9 rem_right_padd rem_left_padd">
                    <span>Fax</span>
                </div>
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control" id="ex_d_faxNumber" name="ex_d_faxNumber" tabindex="1006">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <div class="col-md-9 rem_right_padd rem_left_padd">
                    <span>email</span>
                </div>
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ex_d_email" name="ex_d_email" tabindex="1007">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>Address</span>
            </div>
            <div class="col-md-7">
                <input type="text" class="form-control" id="ex_address_main" name="ex_address_main" tabindex="1008">
                <input id="ex_doc_addr_id" type="hidden" />
            </div>
            <div class="col-md-2 rem_right_padd rem_left_padd">
                <div id="ex_docAddr_Edit" class="newBtDivEx"><span class="newBut">Address</span></div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>City</span>
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ex_city" name="ex_city" tabindex="1009">
            </div>
            <div class="col-md-6 rem_left_padd rem_right_padd">
                <div class="form-group rem_left_padd rem_right_padd">
                    <div class="col-md-2 rem_left_padd rem_right_padd">
                        <span>Prov</span>
                    </div>
                    <div class="col-md-3 rem_left_padd rem_right_padd">
                        @*<input type="text" class="form-control" id="ex_province" name="ex_province" tabindex="1010">*@
                        <select class="form-control" id="ex_province" tabindex="1">
                            <option value="0">CAAB</option>
                            <option value="1">CABC</option>
                            <option value="2">CAMB</option>
                            <option value="3">CANB</option>
                            <option value="4">CANL</option>
                            <option value="5">CANS</option>
                            <option value="6">CANT</option>
                            <option value="7">CANU</option>
                            <option value="8">CAON</option>
                            <option value="9">CAPE</option>
                            <option value="10">CAQC</option>
                            <option value="11">CASK</option>
                            <option value="12">CAYT</option>
                        </select>
                    </div>
                    <div class="col-md-3 rem_left_padd rem_right_padd">
                        <span>Country</span>
                    </div>
                    <div class="col-md-4 rem_left_padd rem_right_padd">
                        @*<input type="text" class="form-control" id="ex_country" name="ex_country" tabindex="1012">*@
                        <select class="form-control" id="ex_country" tabindex="1">
                            <option value="0">Canada</option>
                            <option value="1">USA</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>Postal Code</span>
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ex_postal" name="ex_postal" tabindex="1011">
            </div>
            <div class="col-md-1">
                <span>Fax</span>
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control" id="ex_d_AddFax" name="ex_d_AddFax" tabindex="1012">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>Description</span>
            </div>
            <div class="col-md-9">
                <textarea rows="4" cols="50" type="text" class="form-control" id="ex_d_description" name="ex_d_description" tabindex="1013"></textarea>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>Comment</span>
            </div>
            <div class="col-md-9">
                <textarea rows="4" cols="50" type="text" class="form-control" id="ex_d_comment" name="ex_d_comment" tabindex="1014"></textarea>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>HRM ID</span>
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ex_d_HRMId" name="ex_d_HRMId" tabindex="1015">
            </div>
            <div class="col-md-3 checkboxes">
                <label><input type="checkbox" tabindex="1016" id="ex_d_active" /> <span>Active</span></label>
            </div>
            <div class="col-md-3 checkboxes">
                <label><input type="checkbox" tabindex="1017" id="ex_d_locked" /> <span>Locked</span></label>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                <span>Contact by:</span>
            </div>
            <div class="col-md-9">
                <div class="col-md-2 checkboxes">
                    <label><input type="checkbox" tabindex="1018" id="ex_d_hrm_chk" /> <span>HRM</span></label>
                </div>
                <div class="col-md-2 checkboxes">
                    <label><input type="checkbox" tabindex="1018" id="ex_d_fax_chk" /> <span>Fax</span></label>
                </div>
                <div class="col-md-2 checkboxes">
                    <label><input type="checkbox" tabindex="1019" id="ex_d_email_chk" /> <span>Email</span></label>
                </div>
                <div class="col-md-2 checkboxes margL3">
                    <label><input type="checkbox" tabindex="1020" id="ex_d_mail_chk" /> <span>Mail</span></label>
                </div>
                <div class="col-md-2 checkboxes">
                    <label><input type="checkbox" tabindex="1021" id="ex_d_phone_chk" /> <span>Phone</span></label>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="form-group">
                <div class="col-md-3">
                    <span>Password</span>
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control" id="ex_d_Password" name="ex_d_Password" tabindex="1022">
                </div>
                <div class="col-md-5">
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="form-group">
                <div class="col-md-3">
                    <span>CPSO</span>
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control" id="ex_d_CPSO" name="ex_d_CPSO" tabindex="1023">
                </div>
                <div class="col-md-5">
                    <button type="button" tabindex="1024" class="form-control clssButton">Check CPSO in HRM</button>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-5">
                <a href="#">CPSO Doctor Search</a>
            </div>
            <div class="col-md-7">
                @*<span>Last Name</span>*@
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-5">
                <a href="#">Set Credentials</a>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-7">
                <select class="form-control forSelect" onchange="assosSelChanged(event);" id="ext_main_doc_sselect" tabindex="1"></select>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-12">
                <label class="red_" id="extDocLabel"></label>
            </div>
        </div>

    </div>

    <div id="nxk_dialog" title="Add Next Of Keen">
        <div class="row">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Contact Purpose
                </div>
                <div class="col-md-8">
                    <input type="text" class="form-control" id="nxk_contactPurpose_" name="nxk_contactPurpose_" tabindex="1">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    First Name
                </div>
                <div class="col-md-5">
                    <input type="text" class="form-control" id="nxk_firstName_" name="nxk_firstName_" tabindex="2">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Middle Name
                </div>
                <div class="col-md-5">
                    <input type="text" class="form-control" id="nxk_middleName_" name="nxk_middleName_" tabindex="3">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Last Name
                </div>
                <div class="col-md-8">
                    <input type="text" class="form-control" id="nxk_lastName_" name="nxk_lastName_" tabindex="4">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Email
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control" id="nxk_emailAddress_" name="nxk_emailAddress_" tabindex="5">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Phone
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control" id="nxk_phone_" name="nxk_phone_" tabindex="7">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                <div class="col-md-4 text_right">
                    Notes
                </div>
                <div class="col-md-8">
                    <textarea class="form-control" id="nxk_notes_" name="nxk_notes_" tabindex="6"></textarea>
                </div>
            </div>
        </div>
    </div>

    <div id="enr_d_dialog" title="Add Doctors Enrollment Information">
        <div class="form-group">
            <div class="col-md-9">
                @Html.LabelFor(model => model.ed_enrolled)
            </div>
            <div class="col-md-1">
            </div>
            <div class="col-md-2">
                @Html.EnumDropDownListFor(model => model.ed_enrolled, htmlAttributes: new { @class = "form-control", tabindex = 1 })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-9">
                @Html.LabelFor(model => model.ed_enrollmentStatusSpecified)
            </div>
            <div class="col-md-1">
            </div>
            <div class="col-md-2">

                @Html.EnumDropDownListFor(model => model.ed_enrollmentStatusSpecified, htmlAttributes: new { @class = "form-control", tabindex = 2 })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-9">
                @Html.LabelFor(model => model.ed_enrollmentDate)
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ed_enrollmentDate_" name="ed_enrollmentDate_" tabindex="3">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-9">
                @Html.LabelFor(model => model.ed_enrollmentDateSpecified)
            </div>
            <div class="col-md-1">
            </div>
            <div class="col-md-2">
                @Html.EnumDropDownListFor(model => model.ed_enrollmentDateSpecified, htmlAttributes: new { @class = "form-control", tabindex = 4 })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-9">
                @Html.LabelFor(model => model.ed_enrollmentTerminationDate)
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ed_enrollmentTerminationDate_" name="ed_enrollmentTerminationDate_" tabindex="5">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-9" style="font-weight:700;">
                Enrollment Termination Date Specified
            </div>
            <div class="col-md-1">
            </div>
            <div class="col-md-2">
                @Html.EnumDropDownListFor(model => model.ed_enrollmentTerminationDateSpecified, htmlAttributes: new { @class = "form-control", tabindex = 6 })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-9">
                @Html.LabelFor(model => model.ed_terminationReason)
            </div>
            <div class="col-md-1">
            </div>
            <div class="col-md-2">
                @Html.EnumDropDownListFor(model => model.ed_terminationReason, htmlAttributes: new { @class = "form-control", tabindex = 7 })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-9">
                @Html.LabelFor(model => model.ed_terminationReasonSpecified)
            </div>
            <div class="col-md-1">
            </div>
            <div class="col-md-2">
                @Html.EnumDropDownListFor(model => model.ed_terminationReasonSpecified, htmlAttributes: new { @class = "form-control", tabindex = 8 })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-9 checkbox">
                <label style="font-weight:700">
                    @Html.EditorFor(x => x.isNewEnrollment, new { htmlAttributes = new { tabindex = 5 } })
                    @Html.DisplayNameFor(m => m.isNewEnrollment)
                </label>
            </div>
            <div class="col-md-3">
            </div>
        </div>
    </div>

    <div id="addres_dialog_edit" title="Add Doctor's Address">
        <div class="form-group">
            <div class="col-md-3">
                Address
            </div>
            <div class="col-md-9">
                <input type="text" class="form-control" id="ext_addressLine1_" name="ext_addressLine1_" tabindex="1">
                <input id="ex_doc_addr_id_" type="hidden" />
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                Fax
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control" id="ext_faxNumber_" name="ext_faxNumber_" tabindex="2">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                City
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control" id="ext_city_" name="ext_city_" tabindex="3">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                Postal Code
            </div>
            <div class="col-md-3">
                <input type="text" class="form-control" id="ext_postalCode_" name="ext_postalCode_" tabindex="4">
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                Province
            </div>
            <div class="col-md-2">
                <select class="form-control" id="ext_province_" tabindex="1">
                    <option value="0">CAAB</option>
                    <option value="1">CABC</option>
                    <option value="2">CAMB</option>
                    <option value="3">CANB</option>
                    <option value="4">CANL</option>
                    <option value="5">CANS</option>
                    <option value="6">CANT</option>
                    <option value="7">CANU</option>
                    <option value="8">CAON</option>
                    <option value="9">CAPE</option>
                    <option value="10">CAQC</option>
                    <option value="11">CASK</option>
                    <option value="12">CAYT</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-3">
                Country
            </div>
            <div class="col-md-3">
                <select class="form-control" id="ext_country_" tabindex="1">
                    <option value="0">Canada</option>
                    <option value="1">USA</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-7">
                <select class="form-control forSelect" onchange="extDocAddressChanged(event);" id="ext_doc_addes_select" tabindex="1"></select>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-12">
                <label class="red_" id="extAddLabel"></label>
            </div>
        </div>
    </div>

    <div id="addCohort" class="panel panel-default" style="">
        <div style="width:600px;position:relative;float:left;">
            <div style="background-color:rgb(201, 207, 211);height:40px;width:600px;position:relative;float:left;">
                <span style="display:inline-block;margin-left:45px;margin-left:225px;margin-top: 10px;font-weight:700;">Add Patient to Cohort</span>
            </div>
            <div style="width:600px;position:relative;float:left;">
                <div class="form-group wrapper" style="margin-top: 10px;">
                    <div class="col-md-4 float_r">
                        <span>Patient:</span>
                    </div>
                    <div class="col-md-7 marg_bott height25">
                        <input id="cohortPatientId" class="input_ form-control" placeholder="" tabindex=201 />
                    </div>
                </div>
                <div class="form-group wrapper" style="margin-top: 10px;">
                    <div class="col-md-4 float_r">
                        <span>Cohort:</span>
                        <input type="hidden" id="hiddCohortSelected" />
                    </div>
                    <div class="col-md-4 marg_bott height25">
                        @Html.DropDownListFor(model => model.SelectedCohort_1_Id, Model.cohortsList_1, "", new { @class = "form-control selec_", tabindex = 202 })
                    </div>
                </div>
                <div class="form-group wrapper marg_bott" style="margin-top: 10px;">
                    <div class="col-md-4 float_r">
                        <span>Started:</span>
                    </div>
                    <div class="col-md-3 marg_bott height25">
                        <input id="dateStarted" class="input_ form-control" placeholder="" tabindex=203 />
                    </div>
                </div>
                <div class="form-group wrapper marg_bott">
                    <div class="col-md-4 float_r">
                        <span>Terminated:</span>
                    </div>
                    <div class="col-md-3 marg_bott input_maxW height25">
                        <input id="dateTerminated" class="input_ form-control" placeholder="" tabindex=204 />
                    </div>
                </div>
                <div class="form-group wrapper marg_bott" style="margin-top: 10px;">
                    <div class="col-md-4 float_r">
                        <span>Doctor:</span>
                    </div>
                    <div class="col-md-5 marg_bott height25">
                        @Html.DropDownListFor(model => Model.SelectedDoctor_1_Id, Model.doctorsList_1, "", new { @class = "form-control selec_", tabindex = 205 })
                        @*<input id="cohortDoc_Id" class="input_ form-control" placeholder="" tabindex=205 />*@
                    </div>
                </div>
                <div class="form-group wrapper marg_bott">
                    <div class="col-md-4 float_r">
                        <span>Notes:</span>
                    </div>
                    <div class="col-md-8
                              marg_bott input_maxW">
                        <textarea id="cohortNotes" class="input_ form-control" placeholder="" tabindex=206></textarea>
                    </div>
                </div>
                <div class="form-group wrapper">
                    <div class="col-md-12 marg_bott input_maxW">
                        <label class="red_" id="cohortLabel"></label>
                    </div>
                </div>
            </div>
        </div>
        @*<div style="clear:both;"></div>*@
    </div>

</div>





