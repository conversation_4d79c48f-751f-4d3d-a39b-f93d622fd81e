@model Cerebrum.ViewModels.ContactManager.VMContactMessage

@using (Html.BeginForm("sendmessage", "contactmanagers", new { area = "contactmanagers" }, FormMethod.Post, true, new { @id = "frm-contactmessage" }))
{
    @Html.Modal<PERSON>eader("Send Message")
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.PatientId)
    @Html.HiddenFor(model => model.PatientFullName)
    @Html.HiddenFor(model => model.OfficeId)
    @Html.HiddenFor(model => model.Office)

    <div class="modal-body">
        <div class="form-horizontal">
            
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                        
            <div class="row">
                <div class="col-md-8">

                    @if (Model.PatientId > 0)
                    {
                        <div class="form-group form-group-sm">
                            @Html.LabelFor(model => model.PatientFullName, htmlAttributes: new { @class = "control-label col-md-2" })
                            <div class="col-md-10">
                                <p class="form-control-static">@Html.DisplayFor(model => model.PatientFullName)</p>
                            </div>
                        </div>
                    }

                    <div class="form-group form-group-sm">
                        @Html.LabelFor(model => model.DueDate, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.EditorFor(model => model.DueDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
                            @Html.ValidationMessageFor(model => model.DueDate, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        @Html.LabelFor(model => model.UrgencyId, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.UrgencyId, new SelectList(ViewBag.LKContactUrgencies, "Value", "Text", Model.UrgencyId), new { @class = "form-control" })
                            @Html.ValidationMessageFor(model => model.UrgencyId, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        @Html.LabelFor(model => model.Subject, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.EditorFor(model => model.Subject, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.Subject, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group form-group-sm">
                        @Html.LabelFor(model => model.Message, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.TextAreaFor(model => model.Message, new { @class = "form-control count-text", @rows = 8, data_max_length = 3000 })
                            @Html.ValidationMessageFor(model => model.Message, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    
                </div>
                <div class="col-md-4">
                    <div class="form-group form-group-sm">
                        <div class="col-md-12">
                            <div class="pull-left">
                                <label for="Recipients" class="control-label">
                                    Recipients 
                                </label>
                            </div> 
                            <span id="total-selected-recipients"></span>
                            <div class="clearfix"></div>
                       </div>                      
                    </div>
                    <div class="form-group form-group-sm">  
                        <div class="col-md-12">
                            <div id="recipients-container" style="height:300px;overflow-y:auto;">
                                @Html.ValidationMessageFor(model => model.Recipients, "", new { @class = "text-danger" })
                                @Html.EditorFor(model => model.Recipients)
                            </div>
                        </div>                   
                    </div> 
                </div>
            </div>
                       
        </div>
    </div>
    @Html.ModalFooter("Send", "blue")
}


