﻿@model Cerebrum.ViewModels.ContactManager.VMRecipient

@Html.HiddenFor(model => model.Id)
@Html.HiddenFor(model => model.UserId)
@Html.HiddenFor(model => model.UserFullName)
<div class="checkbox">
    <label>
        @Html.EditorFor(model => model.IsSelected, new { htmlAttributes = new { @class = "" } }) <span class="checkbox-text">@Model.UserFullName</span>
    </label>
    @Html.ValidationMessageFor(model => model.IsSelected, "", new { @class = "text-danger" })
</div>