@model Cerebrum.ViewModels.Econsult.VMMetadataConsult
@using Cerebrum.ViewModels.Patient;
@using Cerebrum.BLL.Utility;
@using AwareMD.Cerebrum.Shared.Enums;

@if (Model.Consults.Count > 0)
{
    foreach (var item in Model.Consults)
    {

        var state = " Note added";
        if (!(item.Notes.Count > 1 && item.ConsultState.ToLower() == "submitted"
                              && item.Notes.Last().State.ToLower() == "submitted"))
        {
            state = CustomHelpers.GetStatusText(item.ConsultState);
        }
        //var patientName = item.PatientGiven + " " + item.PatientFamily;
        var patientName = item.PatientFamily + ", " + item.PatientGiven;

        <tr data-id="@item.CaseId">
            <td class="text-nowrap">
                @Html.Truncate(item.Title, 17, true, false)
            </td>
            <td class="text-nowrap">
                @patientName
            </td>

            <td class="text-nowrap">
                @item.RequesterName
            </td>
            <td class="text-nowrap">
                @item.RecipientName
            </td>
            <td class="text-nowrap">
                @state
            </td>
            <td class="text-nowrap">
                @item.DateSubmitted.ToString("MMM dd, yyyy h:mm tt")
            </td>
            <td class="text-center">
                @if (item.SavedCompletedFlag)
                {
                    <input type="checkbox" onclick="return false;" checked readonly />
                }
            </td>
            <td class="text-center">
                @if (item.PatientNeedsToBeSeen)
                {
                    <input type="checkbox"  onclick="return false;" checked readonly />
                }
            </td>
            <td class="text-center">
                @if (item.PatientAssociatedFlag)
                {
                    <input type="checkbox"  onclick="return false;" checked readonly />
                }
            </td>
            <td class="text-center">
                @if (item.ConsultFlag != "NOFLAG")
                {
                    <span class="glyphicon glyphicon-flag" style="color:@CustomHelpers.GetFlagColor(item.ConsultFlag);margin-left:10px;" data-toggle="tooltip" title="@item.ConsultFlag"></span>
                }
            </td>
            <td class="text-center">
                @if (item.SavedCompletedFlag)
                {
                    <a href="javascript:void(null)" class="btn-view-consult-details" data-case-id="@item.CaseId" data-id="@item.Id">View</a>
                }
            </td>
        </tr>
    }
}
else
{
    <tr>
        <td colspan="11">
            <div class="col-sm-12">
                <h4>There are no cases found</h4>
            </div>
        </td>
    </tr>
}
