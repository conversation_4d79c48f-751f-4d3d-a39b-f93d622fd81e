@model  OtnConsult
@using Cerebrum.ViewModels.Econsult.Models;
<script src="~/Areas/eConsult/Scripts/upload-file-common.js"></script>
@{
    var reasons = (List<ReturnReason>)ViewBag.ReturnReasons;
}
<input type="hidden" id="hdMainAnswer" />
<input type="hidden" id="hdExtAnswer1" />
<input type="hidden" id="hdExtAnswer2" />
<input type="hidden" id="hdValidTimeSpent" value="false" />
<input type="hidden" id="hdCaseId" value="@Model.CaseId" />
<input type="hidden" id="hdClinicFileIds" value="" />
<input type="hidden" id="hdClinicFileNames" value="" />
<input type="hidden" id="hdClinicFileUrls" value="" />
<input type="hidden" id="hdClinicFileDiv" value="" />
<input type="hidden" id="hdClinicOfficeIds" value="" />

<table class="table table-condensed table-responsive borderless" id="tableHTMLExport">
    <tr>
        <td class="col-md-4" style="vertical-align:top;">
            @(await Html.PartialAsync("_consultInfoMain", Model))
        </td>
        <td class="col-md-8" style="vertical-align:top;">
            <table class="table-no-pointer">
                <tr>
                    <td class="col-md-6 align-text-top" style="vertical-align:top;">
                        <div class="row">
                            <div class="col-md-12 div-note" style="margin-bottom:10px;">
                                <div id="accordion-provide-consult" class="accordion">
                                    <h3 class="header-provide-return-consult">Provide Consult</h3>
                                    <div>
                                        <textarea class="form-control consult-note" id="consult-provided-note" rows="5" placeholder="Enter note..." maxlength="4000"></textarea>
                                        <div style="margin-top:6px;">
                                            <div>
                                                <input type="checkbox" id="patientNeedsToBeSeen" name="patientNeedsToBeSeen">
                                                <label for="patientNeedsToBeSeen">Recommendation: patient should be seen (in person or by video)</label>
                                            </div>
                                            <div style="margin-top:6px;" id="div-consult-provided-spanError">
                                                <span id="consult-provided-spanError" style="color:#a94442;"></span><input type="button" class="btn btn-primary pull-right" id="btnProvideConsult" value="Send" /><input type="button" class="btn btn-default pull-right discard-all-data" value="Discard" />
                                                <br />
                                                <label for="FilesProvideConsult" class="custom-file-upload">
                                                    <a class="btn btn-primary btn-sm">Choose Local Files...</a>
                                                </label>
                                                <input style="margin-top:3px" type="file" id="FilesProvideConsult" name="FilesProvideConsult" class="form-control-file generate-description-box" multiple />
                                                @if (Convert.ToInt32(ViewBag.PatientId) > 0)
                                                {
                                                    <label for="btn" class="custom-file-upload">
                                                        <input type="button" data-show-div-id="" data-patient-id="@ViewBag.PatientId" value="Choose EMR Files..." class="btn btn-primary btn-sm clinic-file-generate-description-box" />
                                                    </label>
                                                }
                                            </div>
                                            <div class="col-md-12 text-left div-upload-file-description" id="div-FilesProvideConsult"></div>
                                            <div class="col-md-12 text-left div-upload-file-description" id="div-clinic-Files"></div>
                                        </div>
                                    </div>
                                    <h3 class="header-provide-return-consult">Add Note</h3>
                                    <div>
                                        <textarea class="form-control consult-note" id="consult-provided-add-note" rows="5" placeholder="Enter note..." maxlength="4000"></textarea>
                                        <div style="margin-top:6px;" id="div-consult-provided-add-note-spanError">
                                            <span id="consult-provided-add-note-spanError" style="color:#a94442;"></span><input type="button" class="btn btn-primary pull-right" id="btnConsultProvidedAddNote" value="Send" /><input type="button" class="btn btn-default pull-right discard-all-data" value="Discard" />
                                            <br />
                                            <label for="FilesConsultProvidedAddNote" class="custom-file-upload">
                                                <a class="btn btn-primary btn-sm">Choose Local Files...</a>
                                            </label>
                                            <input style="margin-top:3px" type="file" id="FilesConsultProvidedAddNote" name="FilesConsultProvidedAddNote" class="form-control-file generate-description-box" multiple />
                                            @if (Convert.ToInt32(ViewBag.PatientId) > 0)
                                            {
                                                <label for="btn" class="custom-file-upload">
                                                    <input type="button" data-show-div-id="2" data-patient-id="@ViewBag.PatientId" value="Choose EMR Files..." class="btn btn-primary btn-sm clinic-file-generate-description-box" />
                                                </label>
                                            }
                                        </div>
                                        <div class="col-md-12 text-left div-upload-file-description" id="div-FilesConsultProvidedAddNote"></div>
                                        <div class="col-md-12 text-left div-upload-file-description" id="div-clinic-Files2"></div>
                                    </div>
                                    <h3 class="header-provide-return-consult">Return Consult</h3>
                                    <div>
                                        <div class="form-group">
                                            <div class="col-md-12">
                                                <h5 style="padding-top:0px;margin-top:0px;">Please select reason for returning</h5>
                                            </div>
                                            @foreach (var item in reasons)
                                            {
                                                <div class="col-md-12">
                                                    <input type="radio" name="rbReasonsToReturn" value="@item.ReasonId" />
                                                    @Html.Label(item.Text, "", new { @class = "control-label" })
                                                </div>
                                            }
                                        </div>
                                        <span style="height:3px;">&nbsp;</span>
                                        <textarea class="form-control" id="consult-return-note" rows="1" placeholder="Enter comment..." maxlength="4000"></textarea>
                                        <div style="margin-top:6px;">
                                            <span id="consult-return-spanError" style="color:#a94442;"></span><input type="button" class="btn btn-primary pull-right" id="btnReturnConsult" value="Return" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                @(await Html.PartialAsync("_consultInfoNotes", Model))
            </table>
        </td>
    </tr>
</table>
<br /><br />
