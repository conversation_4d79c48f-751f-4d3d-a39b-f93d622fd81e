@model  Cerebrum.ViewModels.Econsult.VMAssociatePatientRequest
@{ 
    var ohip = Model.Patient.HCN + " " + Model.Patient.HCN_VC;
}
<script src="~/Areas/eConsult/Scripts/associate-patient.js"></script>

@Html.ModalHeader("Patient Search")
<div style="margin-left:15px;width:96%" id="div-associate-patient-search">
    <h5>Patient cannot be found. You can search patient and associate selected patient with consult or patient from consult will be added to EMR</h5>
    <div style="min-width:350px; margin-bottom:20px;" class="pull-left">
        EMR Search: <input type="text" placeholder="seacrh patient" id="SearchToAssociatePatient" class="form-control" />
    </div>
    <input type="hidden" id="PatientId" value="0" />
    <table class="table table-condensed borderless">
        <thead>
            <tr>
                <th></th>
                <th>Patient From</th>
                <th>First Name</th>
                <th>Last Name</th>
                <th>Gender</th>
                <th>Date of Birth</th>
                <th>HIN</th>
             </tr>
        </thead>
        <tbody>
            <tr>
                <td align="center"><input type="radio" name="rbSelectedpatient" value="1" checked /></td>
                <td>Consult</td>
                <td>@Model.Patient.Given</td>
                <td>@Model.Patient.Family</td>
                <td>@Model.Patient.Gender</td>
                <td>@Model.Patient.BirthDate.ToString("MMM dd yyyy")</td>
                <td>@ohip</td>
            </tr>
            <tr>
                <td align="center"><input type="radio" name="rbSelectedpatient" value="0" /></td>
                <td>EMR Search</td>
                <td id="tdPatientFirstName"></td>
                <td id="tdPatientLastName"></td>
                <td id="tdGender"></td>
                <td id="tdPatientDOB"></td>
                <td id="tdOHIP"></td>
            </tr>
        </tbody>
    </table>
    <div class="modal-footer">
        <span id="spanError-associate-patient" class="error-associate-patient">* Patient not selected. Please search patient.</span>
        <input type="button" id="btnAssociateSelectedPatient" class="btn btn-primary" data-case-id="@Model.CaseId" value="Associate Patient" />
        <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Cancel</button>
    </div>
</div>