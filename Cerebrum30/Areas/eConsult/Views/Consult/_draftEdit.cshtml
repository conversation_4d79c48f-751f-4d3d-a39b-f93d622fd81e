@model Cerebrum.ViewModels.Econsult.VMConsultDraft
@using Cerebrum30.Helpers;
@using Cerebrum.BLL.Utility;
@using Cerebrum.ViewModels.Econsult.HealthcareService;
@using Cerebrum.ViewModels.Econsult.Dto;
@{
    var cerebrumUser = (Cerebrum30.Security.CerebrumUser)ViewBag.CerebrumUser;
    var patientFullName = Model.PatientFirstName + " " + Model.PatientLastName;
    var header = Model.Id == 0 ? "Create Draft" : "Edit Draft";

    var defaultMessage = "There is no additional information";

    var popoverTextPractitioner = defaultMessage;
    var popoverTextPractitionerMoreInfo = defaultMessage;
    if (ViewBag.Practitioner != null)
    {
        var practitioner = (PractitionerDetails)ViewBag.Practitioner;

        if (!string.IsNullOrEmpty(practitioner.AdditionalInformation))
        {
            popoverTextPractitioner = practitioner.AdditionalInformation;
        }
        if (!string.IsNullOrEmpty(practitioner.MoreAdditionalInformation))
        {
            popoverTextPractitionerMoreInfo = practitioner.MoreAdditionalInformation;
        }
    }

    var popoverTextOrganization = defaultMessage;
    if (ViewBag.Organization != null)
    {
        var organization = (OrganizationDto)ViewBag.Organization;

        if (!string.IsNullOrEmpty(organization.AdditionalInformation))
        {
            popoverTextOrganization = organization.AdditionalInformation;
        }
    }
    var popoverTextDelivery = "<b>Base Managed Specialty: </b>This option enables you to submit cases to either a regional (where available) or provincial managed specialty group, which is a group of specialists responding to eConsult cases for a given specialty (e.g. pediatric cardiology). The case is then assigned based on specialist availability." +
                              "<br /><br />" +
                              "<b>Specific Provider or Group: </b>This option allows you to submit cases to specific consultants by name or to organizational or regional groups (ex. UHN’s SCOPE group). Cases are sent directly to the consultant or group.";

}
<script src="~/Areas/eConsult/Scripts/draft-edit.js"></script>
<script src="~/Areas/eConsult/Scripts/upload-file-common.js"></script>

<h5><i>@header</i></h5>
<div id="frm-create-draft" style="margin-bottom:40px;">
    <input type="hidden" id="Id" value="@Model.Id" />
    <input type="hidden" id="OfficeId" value="@cerebrumUser.OfficeId" />
    <input type="hidden" id="PracticeId" value="@cerebrumUser.PracticeId" />
    <input type="hidden" id="PatientId" value="@Model.PatientId" />
    <input type="hidden" id="RecipientPractitionerId" value="@Model.RecipientPractitionerId" />
    <input type="hidden" id="RecipientOrganizationId" value="@Model.RecipientOrganizationId" />
    <input type="hidden" id="ManagedTypeCode" value="@Model.ManagedTypeCode" />
    <input type="hidden" id="ManagedSpecialtyCode" value="@Model.ManagedSpecialtyCode" />
    <input type="hidden" id="RecipientServiceId" value="@Model.RecipientServiceId" />
    <input type="hidden" id="hdClinicFileIds" value="" />
    <input type="hidden" id="hdClinicFileNames" value="" />
    <input type="hidden" id="hdClinicFileUrls" value="" />
    <input type="hidden" id="hdClinicOfficeIds" value="" />
    @Html.AntiForgeryToken()

    <table class="table table-condensed table-responsive borderless">
        <tr>
            <td class="col-md-2" style="vertical-align:top; padding-top: 15px;">
                <a data-toggle="collapse" href="#patient-info" id="hrefPatientName">Patient</a>
            </td>
            <td class="col-md-10">
                <table>
                    <tr>
                        <td class="col-md-12">
                            <div style="min-width:350px;" class="pull-left">
                                <input type="text" placeholder="seacrh patient" id="PatientName" name="PatientName" style="width:480px;" class="form-control" value="@patientFullName" />
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <table style="width:100%;">
                                <tr>
                                    <td class="col-md-12">
                                        <div id="patient-info" class="panel-collapse collapse" style="margin-top:5px;">
                                            <table class="table table-condensed table-bordered table-responsive" style="width:100%;">
                                                <tr>
                                                    <td class="col-md-4 text-nowrap"><span class='text-danger'>* </span>First Name</td>
                                                    <td class="col-md-8 text-nowrap" id="tdPatientFirstName">@Model.PatientFirstName</td>
                                                </tr>
                                                <tr>
                                                    <td class="col-md-4"><span class='text-danger'>* </span>Last Name</td>
                                                    <td class="col-md-8" id="tdPatientLastName">@Model.PatientLastName</td>
                                                </tr>
                                                <tr>
                                                    <td class="col-md-4">&nbsp;&nbsp;Middle Name</td>
                                                    <td class="col-md-8" id="tdPatientMiddleName">@Model.PatientMiddleName</td>
                                                </tr>
                                                <tr>
                                                    <td class="col-md-4"><span class='text-danger'>* </span>DOB</td>
                                                    <td class="col-md-8" id="tdPatientDOB">@Model.DOB_MMM_dd_yyyy</td>
                                                </tr>
                                                <tr>
                                                    <td class="col-md-4"><span class='text-danger'>* </span>Gender</td>
                                                    <td class="col-md-8" id="tdGender">@UtilityHelper.GetDescriptionFromEnumValue(Model.Gender)</td>
                                                </tr>
                                                <tr>
                                                    <td class="col-md-4"><span id="span-ohip-not-required">&nbsp;</span> <span class='text-danger' id="span-ohip-required">* </span>HIN</td>
                                                    <td class="col-md-8" id="tdOHIP">@Model.OHIP</td>
                                                </tr>
                                            </table>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td>
                @Html.LabelFor(model => model.BillingCoverage, htmlAttributes: new { @class = "control-label" })
            </td>
            <td>
                <div class="col-md-3">
                    @Html.CustomEnumDropDownListFor(model => model.BillingCoverage, htmlAttributes: new { @class = "form-control" })
                </div>
            </td>
        </tr>
        <tr id="trInsurer">
            <td>Insurance</td>
            <td>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-1" style="margin-top:6px;">@Html.Label("Name", htmlAttributes: new { @class = "control-label" })</div>
                        <div class="col-md-6">
                            @Html.EditorFor(model => model.InsurerName, new { htmlAttributes = new { @class = "form-control", @maxlength = 20 } })
                        </div>
                        <div class="col-md-2 text-nowrap" style="margin-top:6px; text-align:right">@Html.Label("Group Number", htmlAttributes: new { @class = "control-label" })</div>
                        <div class="col-md-3">
                            @Html.EditorFor(model => model.InsuranceGroupNumber, new { htmlAttributes = new { @class = "form-control", @maxlength = 20 } })
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        <tr>
            <td>

                @Html.LabelFor(model => model.Delivery, htmlAttributes: new { @class = "control-label" })
                <div class='glyphicon glyphicon-question-sign c-pointer pull-right' style="padding-right:0px;margin-right:0px;" data-placement='right' data-toggle="popover" title="About Delivery Model"
                     data-content="@popoverTextDelivery"></div>
            </td>
            <td>
                <div class="col-md-4">
                    @Html.CustomEnumDropDownListFor(model => model.Delivery, htmlAttributes: new { @class = "form-control" })
                </div>
            </td>
        </tr>
        <tr id="trDeliveryModel1">
            <td>Specialty/Sub-Specialty</td>
            <td>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-4">
                            <select id="Specialty" name="Specialty" class="form-control"></select>
                        </div>
                        <div class="col-md-4 div-sub-specialty">
                            <select id="SubSpecialty" name="SubSpecialty" class="form-control"></select>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="trRegion">
            <td>Name</td>
            <td>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-4">
                            <select id="RecipientService" name="RecipientService" class="form-control"></select>
                        </div>
                        <div style="padding-top:6px;">
                            <div id="recipientService-additional-information" class='glyphicon glyphicon-info-sign c-pointer' data-placement='left' data-toggle="popover" title="About Healthcare Service"
                                 data-content="@defaultMessage"></div>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="trProvider">
            <td>Provider</td>
            <td>
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-4">
                            <select id="SpecificProviderOrGroup" name="SpecificProviderOrGroup" class="form-control">
                                <option value="0">Select Provider</option>
                                <option value="1">Practitioner</option>
                                <option value="2">Organization</option>
                            </select>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="trRecipientPractitioner">
            <td>Practitioner</td>
            <td>
                <div class="col-md-12">
                    <div style="min-width:350px;" class="pull-left">
                        <input type="text" value="@ViewBag.RecipientPractitionerName" title="@ViewBag.RecipientPractitionerTooltip" placeholder="seacrh practitioner" id="PractitionerName" name="PractitionerName" style="width:480px;" class="form-control" />
                    </div>
                    <div style="padding-top:6px;">
                        &nbsp;&nbsp;&nbsp;
                        <div id="practitioner-additional-information" class='glyphicon glyphicon-info-sign c-pointer' data-toggle="popover" title="Additional Information" data-placement="left"
                             data-content="@popoverTextPractitioner"></div>
                        &nbsp;&nbsp;&nbsp;
                        <div id="practitioner-additional-information-more" class='glyphicon glyphicon-user c-pointer' data-toggle="popover" title="Practitioner Information" data-placement="left"
                             data-content="@popoverTextPractitionerMoreInfo"></div>
                    </div>
                </div>
            </td>
        </tr>
        <tr id="trRecipientOrganization">
            <td>Organization</td>
            <td>
                <div class="col-md-12">
                    <div style="min-width:350px;" class="pull-left">
                        <input type="text" value="@ViewBag.RecipientOrganizationName" placeholder="seacrh organization" id="OrganizationName" name="OrganizationName" class="form-control" />
                    </div>
                    <div class="col-lg-1" style="padding-top:6px;">
                        <div id="organization-additional-information" class='glyphicon glyphicon-info-sign c-pointer' data-toggle="popover" title="About the Group" data-placement="left"
                             data-content="@popoverTextOrganization"></div>
                    </div>
                    <div class="col-lg-4" id="view-directory" style="padding-top:5px;">
                        @if (ViewBag.RecipientDirectoryUrl != null)
                        {
                            <a href='@ViewBag.RecipientDirectoryUrl' style="padding-left:5px;" title='To view directory you have to login to OTN' target='_blank'>View Directory</a>
                        }
                    </div>
                </div>
            </td>
        </tr>
        <tr>
            <td>Subject</td>
            <td>
                <div class="col-md-12">
                    @Html.EditorFor(model => model.SubjectLine, new { htmlAttributes = new { @class = "form-control", @style = "width: 700px;", @maxlength = 1000 } })
                    <div style="font-size:smaller;"><span>Characters left </span><span id="spanSubjectLine">1000</span></div>
                </div>

            </td>
        </tr>
        <tr>
            <td style="vertical-align:top;">Note</td>
            <td>
                <div class="col-md-12">
                    @Html.TextAreaFor(x => x.Notes,
                                                    new
                                                    {
                                                        @class = "form-control",
                                                        @rows = 5,
                                                        @cols = 400,
                                                        autocmplete = "off",
                                                        style = "width: 700px;",
                                                        @maxlength = 3997

                                                    })

                    <div style="font-size:smaller;"><span>Characters left </span><span id="spanNotes">3997</span></div>
                </div>

            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <div class="col-md-4">
                    <label for="Files" class="custom-file-upload">
                        <a class="btn btn-primary btn-sm">Choose Local Files...</a>
                    </label>
                    <input type="file" id="Files" name="Files" class="form-control-file generate-description-box" multiple />
                </div>
                <div class="col-md-8 text-left">
                    @if (Model.AttachedFile.Count > 0)
                    {
                        foreach (var file in Model.AttachedFile)
                        {
                            var fileName = Html.Truncate(file.AttachedFileName + " (" + file.DateCreated.ToString("MMM dd yyyy") + ")", 25, true, false);
                            <div class="text-left">
                                <div class="col-md-6" id="<EMAIL>"><a href="#" class="href-delete-file" data-file-id="@file.Id">Delete </a> @fileName</div>
                                <div class="col-md-6" id="<EMAIL>">&nbsp;@Html.Truncate(file.Description, 30, true, false)</div>
                            </div>
                        }
                    }
                </div>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <div class="col-md-4">
                    <input type="button" id="btnSelectFromClinic" name="btnSelectFromClinic" value="Choose EMR Files..." class="btn btn-primary btn-sm" />
                </div>
                <div class="col-md-12 text-left" id="div-clinic-Files" style="padding-top:5px; padding-left:12px;padding-right:0px;"></div>
                <div class="col-md-12 text-left" id="div-Files" style="padding-top:5px; padding-left:12px;padding-right:0px;"></div>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>
                <div class="col-md-4">
                    <button id="btnSaveDraft" type="button" class="btn btn-info">Save Draft</button>
                </div>
                <div class="col-md-4">
                    @if (Model.Id > 0)
                    {
                        <button id="btnDeleteDraft" data-draft-id="@Model.Id" type="button" class="btn btn-info">Delete Draft</button>
                    }
                    else
                    {
                        <button id="btnDiscard" data-draft-id="@Model.Id" type="button" class="btn btn-info">Discard</button>
                    }
                </div>
                <div class="col-md-4">
                    <button id="btnSubmitDraft" type="button" class="btn btn-info">Submit</button>
                    <span style="visibility:hidden; padding-top:1px;" id="span-loading-submit-draft">
                        <img src="~/Content/Images/loading.gif" style="width:20px;" />
                    </span>
                </div>
            </td>
        </tr>
    </table>
    <div id="divErrorMessage" class="col-md-12"></div>
</div>
