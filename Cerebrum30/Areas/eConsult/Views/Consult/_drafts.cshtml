@model IEnumerable<Cerebrum.ViewModels.Econsult.VMConsultDraftBase>
@using Cerebrum30.Helpers;
@if (Model.Count() == 0)
{
    <h4>You don't have saved drafts</h4>
}
else
{
    <script>
    $(document).ready(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });
    </script>
}
<table style="width:100%;margin-top:5px;margin-bottom:40px;">
    @if (Model.Count() > 0)
    {
        <tr>
            <td>
                <div class="col-sm-12">
                    <br />
                    <table class="table table-condensed table-hover table-bordered table-responsive">
                        <thead>
                            <tr>
                                <th>
                                    Title
                                </th>
                                <th>
                                    Note
                                </th>
                                <th>
                                    Patient
                                </th>
                                <th class="text-nowrap">
                                    Created Date
                                </th>
                                <th class="text-nowrap">
                                    Last Updated
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr data-id="@item.Id" class="tr-draft">
                                    <td>
                                        @Html.Truncate(item.SubjectLine, 25, true, false)
                                    </td>
                                    <td>
                                        @Html.Truncate(item.Notes, 35, true, false)
                                    </td>
                                    <td>
                                        @Html.Truncate(item.PatientLastName + " " + item.PatientFirstName, 40, true, false)
                                    </td>
                                    <td class="text-nowrap">
                                        @item.DateCreated.ToString("MMM dd, yyyy h:mm tt")
                                    </td>
                                    <td class="text-nowrap">
                                        @item.DateLastModified.ToString("MMM dd, yyyy h:mm tt")
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </td>
        </tr>
    }
    <tr>
        <td style="padding-bottom:10px;">
            &nbsp;
        </td>
    </tr>
    <tr>
        <td>
            <div id="draft-details" class="col-sm-12"></div>
        </td>
    </tr>
</table>
