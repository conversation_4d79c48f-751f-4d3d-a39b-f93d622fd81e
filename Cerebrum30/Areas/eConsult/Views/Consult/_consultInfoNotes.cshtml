@model  Cerebrum.ViewModels.Econsult.Models.OtnConsult
@{
    var i = 0;
    var strText = " submitted new case";
}
<tr>
    <td class="col-md-6 align-text-top" style="vertical-align:top;">
        @{
            foreach (var item in Model.Notes)
            {
                var j = 0;
                var declineReason = string.Empty;
                if (i != Model.Notes.Count - 1)
                {
                    strText = CustomHelpers.GetConsultNoteTitle(item.State);
                    if (item.State.ToLower() == "returned")
                    {
                        declineReason = " Reason: " + item.DeclineReason.Replace("_", " ");
                    }
                }
                else { strText = " submitted new case"; }
                i = i + 1;
                var senderName = item.SenderName == item.AuthorName ? item.SenderName : item.AuthorName + " (on behalf of " + item.SenderName + ")";
                var message = senderName + strText;

                <div class="row" style="padding-bottom:10px;">
                    <div class="col-md-12 div-note">
                        <div class="col-md-8 pull-left note-heading">@message</div>
                        <div class="col-md-4 text-nowrap note-heading text-right">@item.Date.ToString("MMM dd, yyyy h:mm tt")</div>
                        @{
                            if (item.PatientNeedsToBeSeenAvailable)
                            {
                                if (item.PatientNeedsToBeSeen)
                                {
                                    <div class="col-md-12 text-danger note-needs-to-be-seen">Recommendation: patient should be seen (in person or by video)</div>
                                }
                                else
                                {
                                    <div class="col-md-12 note-needs-to-be-seen">Patient does not need to be seen (in person or by video)</div>
                                }
                            }
                        }
   
                        <div class="col-md-8 pull-left note-heading">@declineReason</div>
                        @{
                            var note = item.Note?.Replace("\n", "<br />");
                            if (i == Model.Notes.Count)
                            {
                                note = item.Note?.Replace(Model.Title + " - ", "");
                            }
                        }

                        <div class="col-md-12 note-item">@Html.Raw(note)</div>
                        @{
                            if (item.Attachments.Count > 0)
                            {
                                <div class="col-md-12 note-heading">File(s) attached:</div>
                            }
                            foreach (var file in item.Attachments)
                            {
                                j++;
                                var text = "Click to download File " + j;
                                var title = string.IsNullOrEmpty(file.Title) ? "" : file.Title;
                                <div class="col-md-12"><a herf="#" class="download-file c-pointer" data-file-id="@file.Id">@text</a>&nbsp;@Html.Truncate(title, 68, true, false)</div>
                            }
                        }

                    </div>
                </div>
            }
        }
    </td>
</tr>

