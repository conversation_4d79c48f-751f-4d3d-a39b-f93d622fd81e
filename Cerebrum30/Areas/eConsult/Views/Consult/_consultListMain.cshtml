@model Cerebrum.ViewModels.Econsult.VMConsultMain
@using Cerebrum30.Helpers;
@{ 
    var showConsultFlag = false;
    if (ViewBag.ShowConsultFlag != null)
    {
        showConsultFlag = (bool)ViewBag.ShowConsultFlag;
    }
}
<!--style="overflow:auto;max-height:200px; display:block;"-->
@if (Model.Consults.Count == 0)
{
    <table style="width:80%;margin-top:5px;margin-bottom:40px; vertical-align:top;">
        <tr>
            <td>
                <div class="col-sm-12">
                    <h4>There are no cases</h4>
                </div>
            </td>
        </tr>
    </table>
}
else
{
    <script>
        $(document).ready(function () {
            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>
    <table style="width:100%;margin-top:5px;margin-bottom:40px;">
        <tr>
            <td>
                <div class="col-sm-12">
                    <br />
                    <table class="table table-condensed table-hover table-bordered table-responsive">
                        <thead>
                            <tr>
                                <th></th>
                                <th>
                                    Message
                                </th>
                                <th>
                                    State
                                </th>
                                <th>
                                    Patient
                                </th>
                                <th class="text-nowrap">
                                    Submitted Date
                                </th>
                                <th class="text-nowrap">
                                    Last Updated
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.Consults)
                            {
                                var state = " Note added";
                                if (!(item.Notes.Count > 1 && item.ConsultStatus.ToLower() == "submitted"
                                                      && item.Notes.Last().State.ToLower() == "submitted"))
                                {
                                    state = CustomHelpers.GetStatusText(item.ConsultStatus);
                                }

                                <tr data-id="@item.CaseId" class="@Model.SelectedTab">
                                    <td>
                                        @{
                                            if (item.ConsultFlag != "NOFLAG" && showConsultFlag)
                                            {
                                                <span class="glyphicon glyphicon-flag" style="color:@CustomHelpers.GetFlagColor(item.ConsultFlag);margin-left:10px;" data-toggle="tooltip" title="@item.ConsultFlag"></span>
                                            }
                                        }

                                    </td>
                                    <td>
                                        @Html.Truncate(item.Description, 45, true, false)
                                    </td>
                                    <td class="text-nowrap">
                                        @state
                                    </td>
                                    <td class="text-nowrap">
                                        @*@Html.Truncate(item.Patient.Family + " " + item.Patient.Given, 40, true, false)*@
                                        @Html.Truncate(item.Patient.Family + ", " + item.Patient.Given, 40, true, false)
                                    </td>
                                    <td class="text-nowrap">
                                        @item.DateSubmitted.ToString("MMM dd, yyyy h:mm tt")
                                    </td>
                                    <td class="text-nowrap">
                                        @item.LastUpdated.ToString("MMM dd, yyyy h:mm tt")
                                    </td>
                                </tr>
                             }
                        </tbody>
                    </table>
                </div>
            </td>
        </tr>
        <tr>
            <td style="padding-bottom:4px;" align="center">
                <span style="visibility:hidden; padding-top:1px;" id="span-loading-list-main">
                    <img src="~/Content/Images/loading.gif" style="width:20px;" />
                </span>
            </td>
        </tr>
        <tr>
            <td>
                <div id="@Model.SelectedTab-details" class="col-sm-12">

                </div>
            </td>
        </tr>
    </table>

}

