@model  List<Cerebrum.ViewModels.Econsult.Dto.QuestionnaireDto>
@{
    var i = 0;
    Guid g = Guid.NewGuid();
}
<script src="~/Areas/eConsult/Scripts/requester-provide-feedback.js?version=@g"></script>
@Html.ModalHeader("Please answer questions below")
<form id="frm-requester-provide-feedback">
    <div class="modal-body">
        <div class="row">
            <div class="form-group col-md-12" style="vertical-align: top;">
                <!-- move these to parent window later -->
                @foreach (var item in Model)
            {
                    <input type="hidden" name="questionnaire[@i].LinkId" id="questionnaire[@i].LinkId" value="@item.LinkId" />
                    <input type="hidden" name="questionnaire[@i].Question" id="questionnaire[@i].Question" value="@item.Question" />
                    <input type="hidden" name="questionnaire[@i].Answer" id="questionnaire[@i].Answer" value="" />
                    <input type="hidden" name="questionnaire[@i].Comments" id="questionnaire[@i].Comments" value="" />
                    i++;
                    var question = i + ". " + item.Question;
                    <div class="row">
                        <div class="col-md-12">
                            @if (item.Answers[0] != "Comments")
                            {
                                <div><span style="color:red">&nbsp;&nbsp;*&nbsp;</span><strong>@question</strong></div>
                                <div class="form-group">

                                    @foreach (var m in item.Answers)
                                    {
                                        var name = "rb" + item.LinkId;
                                        <div class="col-md-12">
                                            @*@Html.RadioButton("rb" + item.LinkId, m)*@
                                            <input type="radio" name="@name" value="@m" class="radio-question-answer" />
                                            @Html.Label(m, "", new { @class = "control-label" })
                                        </div>
                                    }
                                    <div class="col-md-12">
                                        @Html.TextArea("txt-rb" + @item.LinkId, htmlAttributes: new { @class = "form-control", @placeholder = "Enter Comments...", @style= "display:none;", @maxlength = "4000" })
                                    </div>
                                    <span class="error-requester-feedback" id="<EMAIL>">* Required Field</span>
                                </div>
                            }
                            else
                            {
                                <div>&nbsp;&nbsp;&nbsp;&nbsp;<strong>@question</strong></div>
                                <div class="col-md-12">
                                    @Html.TextArea("rb" + @item.LinkId, htmlAttributes: new { @class = "form-control", @placeholder = "Enter Comments...", @maxlength = "4000" })
                                </div>
                            }

                        </div>
                    </div>
                }


            </div>
        </div>
    </div>
    <div class="modal-footer">
        <span id="spanError-requester-feedback" class="error-requester-feedback">* Please select required field(s)</span>
        <button type="button" class="btn btn-info btn-sm" id="btnRequesterFeedbackOk">OK</button>
        <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Cancel</button>
    </div>
</form>
