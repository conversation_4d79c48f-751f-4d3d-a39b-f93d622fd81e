@model Cerebrum.ViewModels.Patient.VMPatientInfo
@using Cerebrum.BLL.Utility;

@Html.ModalHeader("EMR Patient Info")

<div class="modal-body">

    <div class="row">
        <div class="form-group col-md-12" style="vertical-align: top;">

            <table class="table table-condensed table-bordered table-responsive" style="width:100%;">
                <tr>
                    <td class="col-md-3 text-nowrap">First Name</td>
                    <td class="col-md-9">@Model.FirstName</td>
                </tr>
                <tr>
                    <td class="col-md-3 text-nowrap">Last Name</td>
                    <td class="col-md-9">@Model.LastName</td>
                </tr>
                <tr>
                    <td class="col-md-3">DOB</td>
                    <td class="col-md-9">@Model.DOB_MMM_dd_yyyy</td>
                </tr>
                <tr>
                    <td class="col-md-3">Gender</td>
                    <td class="col-md-9">@UtilityHelper.GetDescriptionFromEnumValue(Model.Gender)</td>
                </tr>
            </table>

        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Close</button>
</div>



