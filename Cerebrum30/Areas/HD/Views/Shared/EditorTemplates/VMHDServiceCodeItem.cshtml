@model Cerebrum.ViewModels.HospitalDaysheet.VMHDServiceCode


<tr id="<EMAIL>">    
    <td>
        @Html.HiddenFor(modelItem => Model.HospitalBundleId)
        @Html.HiddenFor(modelItem => Model.Fee)
        @Html.HiddenFor(modelItem => Model.Code)
        @Html.HiddenFor(modelItem => Model.Id)
        @Html.HiddenFor(modelItem => Model.ServiceId)
        @Html.HiddenFor(modelItem => Model.FeeDouble)
        @Html.HiddenFor(modelItem => Model.Paid)

        @Model.Code
    </td>   
    <td>
        @Model.FeeDouble
    </td>
    <td>
        @Model.Paid
    </td>
    <td>
        @Html.EditorFor(modelItem => Model.NumberOfServices)
        @Html.ValidationMessageFor(model => model.NumberOfServices, "", new { @class = "text-danger" })
    </td>   
    <td>
        @Html.DropDownListFor(model => model.BillStatusId, new SelectList(ViewBag.BillStatuses, "Value", "Text", Model.BillStatusId), new { @class = "form-control billstatusSize" })
        @Html.ValidationMessageFor(model => model.BillStatusId, "", new { @class = "text-danger" })
    </td>
    <td>
        <button type="button" data-hd-service-id="@Model.ServiceId" data-hd-service-code-id="@Model.Id" data-confirm-msg="Delete @Model.Code?"  class="btn btn-danger btn-xs btn-delete-hd-service-code">Delete</button>
    </td>
</tr>

