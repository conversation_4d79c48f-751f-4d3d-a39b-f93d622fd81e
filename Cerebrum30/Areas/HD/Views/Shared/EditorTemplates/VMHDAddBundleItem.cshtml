﻿@model Cerebrum.ViewModels.HospitalDaysheet.VMHDAddBundleItem

@{ string active = Model.Selected ? "active" : ""; }

@Html.HiddenFor(model => model.BundleId)
@Html.HiddenFor(model => model.BundleTypeId)
@Html.HiddenFor(model => model.Description)

<div class="checkbox">
    @*<label>
        @Html.EditorFor(model => model.IsSelected) <span class="checkbox-text">@Model.TriageDispositionDesc</span>
    </label>*@
    <label>
        @Html.CheckBoxFor(model => model.Selected, htmlAttributes: new { @autocomplete = "off", @class = "bundle-item-option", data_bundle_id = Model.BundleId })
        @Html.DisplayFor(model => model.Description)

    </label>
    @Html.ValidationMessageFor(model => model.Selected, "", new { @class = "text-danger" })
</div>

@*<label class="btn btn-sm btn-default @active">
    @Html.CheckBoxFor(model => model.Selected, htmlAttributes: new { @autocomplete = "off", @class = "", data_bundle_id = Model.BundleId })
    @Model.Description

</label>*@
