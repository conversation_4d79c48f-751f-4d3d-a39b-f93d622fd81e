﻿@model Cerebrum.ViewModels.HospitalDaysheet.VMHDBonusCode


    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.Id)
    @*@Html.HiddenFor(model => model.BonusCodeId)*@
    @Html.HiddenFor(model => model.AdmissionActionId)
    @Html.HiddenFor(model => model.Date)
    @Html.HiddenFor(model => model.IsMultiDate)

    @{
        for (int i = 0; i < Model.AdmissionActionIds.Count; i++)
        {
            @Html.HiddenFor(model => Model.AdmissionActionIds[i])
        }
    }

    <div class="form-horizontal">            
        
        @if (!Model.IsMultiDate)
        {

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Date, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <span class="">@Html.DisplayFor(model => model.Date)</span>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Services, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="btn-group btn-group-xs bundle-multi-select-items" data-toggle="buttons">
                    @if (Model.Services != null)
                    {
                        for (int i = 0; i < Model.Services.Count(); i++)
                        {
                            @Html.EditorFor(model => Model.Services[i], "VMBonusCodeService", String.Format("{0}[{1}]", "Services", i))
                        }
                    }
                </div>
                @Html.ValidationMessageFor(model => model.Services, "", new { @class = "text-danger" })
            </div>
        }
        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
            <div class="col-md-4">
                @Html.DropDownListFor(model => model.BonusCodeId, new SelectList(ViewBag.BonusCodes, "Id", "Code", Model.BonusCodeId), "Choose One", new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.Code, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Id, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.AdmissionActionId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.BonusCodeId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })

            </div>
        </div>   
        @*<div class="form-group form-group-sm">
            @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
            <div class="col-md-4">
                @Html.EditorFor(model => model.Code, new { htmlAttributes = new { @class = "form-control bonus-code-autocomplete" } })
                @Html.ValidationMessageFor(model => model.Code, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Id, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.AdmissionActionId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.BonusCodeId, "", new { @class = "text-danger" })
                @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
                
            </div>
        </div>*@   
        
    </div>


