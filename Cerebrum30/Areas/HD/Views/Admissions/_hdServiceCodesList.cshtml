﻿@model IEnumerable<Cerebrum.ViewModels.HospitalDaysheet.VMHDServiceCode>

@{ 
    var serviceCodes = Model.ToList();
    int totalRows = Model.Count();
    int nextIndex = totalRows > 0 ? (totalRows + 1) : 0;
}


<table data-next-index="@nextIndex" class="table">
    <tr>       
        <th>
            @Html.DisplayNameFor(model => model.Code)
        </th>         
        <th>
            @Html.DisplayNameFor(model => model.FeeDouble)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Paid)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.NumberOfServices)
        </th>    
        <th>
            @Html.DisplayNameFor(model => model.BillStatusId)
        </th>       
        <th></th>
    </tr>

@for (int i = 0; i < serviceCodes.Count(); i++)
{    
    @Html.EditorFor(model => serviceCodes[i], "VMHDServiceCodeItem", String.Format("{0}[{1}]", "ServiceCodes", i))
}


</table>
