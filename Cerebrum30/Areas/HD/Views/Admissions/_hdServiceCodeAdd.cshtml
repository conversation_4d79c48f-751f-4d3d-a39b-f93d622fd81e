@model Cerebrum.ViewModels.HospitalDaysheet.VMHDAddCode


    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.HospitalBundleId)
    @Html.HiddenFor(model => model.AdmissionActionId)
    @Html.HiddenFor(model => model.DateServiced)
    @Html.HiddenFor(model => model.PatientName)
    @Html.HiddenFor(model => model.DateServicedStr)
    @Html.HiddenFor(model => model.Fee)
    @Html.HiddenFor(model => model.FeeDouble)
    @Html.HiddenFor(model => model.IsMultiDate)

    @{
        for (int i = 0; i < Model.AdmissionActionIds.Count; i++)
        {
            @Html.HiddenFor(model => Model.AdmissionActionIds[i])
        }
    }
    
    <div class="form-horizontal">        
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })  
        
        <div class="form-group form-group-sm">
            @Html.Label("Patient", "", new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                <span class="service-patient-name">@Html.DisplayFor(model => model.PatientName)</span>
            </div>
            @Html.LabelFor(model => model.DateServiced, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                <span class="service-date">@Html.DisplayFor(model => model.DateServiced)</span>
            </div>
        </div>         

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.PracticeDoctorId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.DropDownListFor(model => model.PracticeDoctorId, new SelectList(ViewBag.PracticeDoctors, "PracticeDoctorId", "FullNameReversed", Model.PracticeDoctorId), new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.PracticeDoctorId, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.BillStatusId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.DropDownListFor(model => model.BillStatusId, new SelectList(ViewBag.BillStatuses, "Value", "Text", Model.BillStatusId), new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.BillStatusId, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.PaymentMethod, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.DropDownListFor(model => model.PaymentMethod, new SelectList(ViewBag.PaymentTypes, "Id", "Name"), new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.PaymentMethod, "", new { @class = "text-danger" })
            </div>
        </div>    

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.Code, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
            <div class="col-md-4">
                @Html.EditorFor(model => model.Code, new { htmlAttributes = new { @class = "form-control code-autocomplete" } })
                @Html.ValidationMessageFor(model => model.Code, "", new { @class = "text-danger" })
            </div>
        </div>          

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.FeeDouble, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                <div class="col-md-4">
                    <span class="hd-code-feedouble">@Html.DisplayFor(model => model.FeeDouble)</span>
                </div>
            </div>
        </div>        

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.NumberOfServices, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.EditorFor(model => model.NumberOfServices, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.NumberOfServices, "", new { @class = "text-danger" })
            </div>
        </div>
           
       
    </div>

