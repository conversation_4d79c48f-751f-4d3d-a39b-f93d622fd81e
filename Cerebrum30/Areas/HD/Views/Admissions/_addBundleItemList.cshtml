﻿@model IEnumerable<Cerebrum.ViewModels.HospitalDaysheet.VMHDAddBundleItem>

<style>
 /*.bundle-multi-select-items .btn-default.active
{
    background-color: #428bca;
    border-color: #357ebd;
    color:#ffffff;
}

.bundle-multi-select-items .btn
{
    margin-right:10px;
}*/
</style>

@{ 
    var maxColumns = 4;
    var totalItems = Model.ToList().Count();
    var totalPerColumn = ((int)(totalItems / maxColumns) + 1);
    var htmlContent = "";
    var list = Model.ToList();
}

<div class="form-group form-group-sm">
    @for (int i = 0; i < list.Count(); i++)
    {
        if (i % totalPerColumn == 0)// start new column
        {
            if (i > 0)
            {
                htmlContent += string.Format("</div>");
            }
            htmlContent += string.Format("<div class=\"col-md-3\">");
        }

        htmlContent += Html.EditorFor(model => list[i], "VMHDAddBundleItem", String.Format("{0}[{1}]", "BundleItems", i)).ToHtmlString();

        if (totalItems == (i + 1))
        {
            htmlContent += string.Format("</div>");
        }
    }
    @Html.Raw(htmlContent)
    @*@for (int i = 0; i < list.Count(); i++)
    {
        @Html.EditorFor(model => list[i], "VMHDAddBundleItem", String.Format("{0}[{1}]", "BundleItems", i))
    }*@
</div>

@*<div class="btn-group btn-group-xs bundle-multi-select-items" data-toggle="buttons">
    @for (int i = 0; i < list.Count(); i++)
    {
        @Html.EditorFor(model => list[i], "VMHDAddBundleItem", String.Format("{0}[{1}]", "BundleItems", i))
    }
</div>*@



