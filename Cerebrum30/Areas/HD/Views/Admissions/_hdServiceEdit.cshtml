@model Cerebrum.ViewModels.HospitalDaysheet.VMHDService


@using (Html.BeginForm("EditHDService", "admissions", new { area = "hd" }, FormMethod.Post, true, new { @id = "frm-hd-service-edit", @class = "" }))
{
@Html.ModalHeader("Edit Service: " + Model.BundleName)

<div class="modal-body">
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.Id)
    @Html.HiddenFor(model => model.AdmissionActionId)
    @Html.HiddenFor(model => model.DateServiced)
    @Html.HiddenFor(model => model.PatientName)
    @Html.HiddenFor(model => model.BundleName)

    <div class="form-horizontal">

        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        <div class="form-group form-group-sm">
            @Html.Label("Patient", htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                <span class="service-patient-name">@Html.DisplayFor(model => model.PatientName)</span>
            </div>
            @Html.LabelFor(model => model.DateServiced, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                <span class="service-date">@Html.DisplayFor(model => model.DateServiced)</span>
            </div>
        </div>        

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.PracticeDoctorId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.DropDownListFor(model => model.PracticeDoctorId, new SelectList(ViewBag.PracticeDoctors, "PracticeDoctorId", "FullNameReversed", Model.PracticeDoctorId), new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.PracticeDoctorId, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.BillStatusId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.DropDownListFor(model => model.BillStatusId, new SelectList(ViewBag.BillStatuses, "Value", "Text", Model.BillStatusId), new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.BillStatusId, "", new { @class = "text-danger" })
            </div>
            @Html.LabelFor(model => model.PaymentMethod, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.DropDownListFor(model => model.PaymentMethod, new SelectList(ViewBag.PaymentTypes, "Id", "Name"), new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.PaymentMethod, "", new { @class = "text-danger" })
            </div>
        </div>
        

        <div class="hd-service-code-container">
            @Html.ValidationMessageFor(model => model.ServiceCodes, "", new { @class = "text-danger" })
            @Html.RenderPartialAsync("_hdServiceCodesList", Model.ServiceCodes);}
        </div>
    </div>
</div> <!--model body ends-->
@Html.ModalFooter("Save", "blue")
}