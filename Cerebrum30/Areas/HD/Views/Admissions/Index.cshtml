﻿@model Cerebrum.ViewModels.HospitalDaysheet.VMHDMain

@{
    ViewBag.Title = "Index";
    Layout = "~/Areas/HD/Views/Shared/_LayoutHD.cshtml";
}

@using (Html.BeginForm("getadmissions", "admissions", new { area = "hd" }, FormMethod.Get, true, new { @class = "form-inline", @id = "frm-hd-admissions-search" }))
{
    @*@Html.AntiForgeryToken()*@  

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.Date, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.Date, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>   

    <div class="form-group form-group-sm">
        <label class="control-label" for="DoctorId">Doctor(s)</label>
        @Html.DropDownList("PracticeDoctorId", new SelectList(ViewBag.PracticeDoctors, "PracticeDoctorId", "FullName", Model.PracitceDoctorId), "Choose One", new { @class = "form-control" })
    </div>
    
    <div class="form-group form-group-sm">              
        <input type="text" class="form-control" id="hd-patient-filter" name="hd-patient-filter" value="" placeholder="Filter by patient" />
    </div>
    

    <button id="btn-add-admission" type="button" style="margin-right:5px;" class="btn btn-default btn-xs">Add Admission</button>
    <button id="btn-bill-admission-date" type="button" style="margin-right:5px;" class="btn btn-default btn-xs">Bill Admissions</button>
    <span id="msg-adm-action-load" class="text-primary"></span>   

}

<div>
    @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
</div>

<div id="admissions-list">
    @{ await Html.RenderPartialAsync("_admissionsList", Model.Admissions);}
</div>

<div class="modal fade" id="billing-message-modal" role="dialog">
    <div class="modal-dialog" style="width: 900px;">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Message</h4>
            </div>
            <div class="modal-body">
                <div id="billing-message-modal-header"></div>
                <div class="modal-body" id="billing-message-modal-body" style="overflow-y: scroll; height:320px;"></div>
            </div>
            <div class="modal-footer">
                <div class="col-md-8" id="billing-message-modal-footer"></div>
                <div class="col-md-4">
                    <div id="billing-message-modal-continue-buttons">
                        <button class="btn btn-default btn-sm" id="btn-view-bill-hospital" data-dismiss="modal" data-admission-id="" data-admission-action-ids="" data-admission-action-date="" style="width: 64px;">Yes</button>
                        <button class="btn btn-default btn-sm" data-dismiss="modal" style="margin-left: 80px; width: 64px;">No</button>
                    </div>
                    <div id="billing-message-modal-close-button">
                        <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
