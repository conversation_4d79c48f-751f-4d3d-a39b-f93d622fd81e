
@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_LayoutDemographics.cshtml";
}

<h2>Index</h2>

@Html.ActionLink("Merge Pdf", "PdfMerge", "PdfTest", null, null) <br/>
@*@Html.ActionLink("EchocardiogramReport Pdf", "EchocardiogramReport", "PdfTest", null, null)*@ <br/>
@*@Html.ActionLink("EchocardiogramReport Pdf CPS", "EchocardiogramReport_CPS", "PdfTest", null, null)<br />*@
@Html.ActionLink("Upload Image AAAAAAAAAAAAAAA", "Index", "PdfTest", null, null) <br />
@Html.ActionLink("Pdf Report Call", "PdfReportCall", "PdfTest", new { appointmentId = 1 }, null) <br />
@Html.ActionLink("Pdf Report Call For Tests", "VP_Report_for_test", "PdfTest", null, null) <br />
@Html.ActionLink("Pdf Report Call For VP Page", "PdfReportFor_VP", "PdfTest", new { appointmentId = 1 }, null) <br />
@Html.ActionLink("Pdf Report Call For Work Sheet Page", "PdfReportFor_WS", "PdfTest", new { appointmentId = 1 }, null) <br />
@*@Html.ActionLink("Pdf Report PDF", "ReportFor_VP_Pdf", "PdfTest", new { appointmentId = 1, patientId = 1, officeId = 1, mainDocId = 1, refDocId = 1, ccDocId = 1, practiceID = 1, userId = 1, printDirectoryPath = @"C:\Gevorg\Cerebrum3DB_03.13.2017\Cerebrum30\Areas\PdfConversions\Pdfs\636256204852552921_EchocardiogramReport" }, null) <br />*@
@Html.ActionLink("Pdf Report PDF vp", "ReportFor_VP_Pdf", "PdfTest", new { appointmentId = 1, patientId = 1, officeId = 1, mainDocId = 1, refDocId = 1, ccDocId = 1, practiceID = 1 }, null) <br />
@Html.ActionLink("Echocardiogram Report PDF", "Echocardiogram_Report_Pdf", "PdfTest", 
    new { testId =1, appointmentId = 1, patientId = 1, officeId = 1, imageBytesId = 1, numPages = 10, refDoc = 1, technId = 1 }, null) <br />
@Html.ActionLink("Holter Report PDF", "Holter_Report_Pdf", "PdfTest",
    new { testId = 5, appointmentId = 1, patientId = 1, officeId = 1, imageBytesId = 1, numPages = 10, refDoc = 1, technId = 1 }, null) <br />
@Html.ActionLink("ECG Report PDF", "ECG_Report_Pdf", "PdfTest",
    new { testId = 4, appointmentId = 3, patientId = 4, officeId = 1, imageBytesId = 1, numPages = 10, refDoc = 1, technId = 1 }, null) <br />
@Html.ActionLink("StressTestStudy Report PDF", "StressTestStudy_Report_Pdf", "PdfTest",
    new { testId = 3, appointmentId = 1, patientId = 1, officeId = 1, imageBytesId = 1, numPages = 10, repDoc =1, refDoc = 1, famDoc = 1, ccDoc = 1 }, null) <br />
@Html.ActionLink("Stress Echo Sdudy Report PDF", "StressEchoStudy_Report_Pdf", "PdfTest",
    new { testId = 2, appointmentId = 1, patientId = 1, officeId = 1, imageBytesId = 1, numPages = 10, repDoc = 1, refDoc = 1, famDoc = 1, ccDoc = 1 }, null) <br />
@Html.ActionLink("BP Report PDF", "BP_Report_Pdf", "PdfTest",
    new { testId = 11, appointmentId = 1, patientId = 1, officeId = 1, imageBytesId = 1, numPages = 10, repDoc = 1, refDoc = 1, famDoc = 1, ccDoc = 1 }, null) <br />
@Html.ActionLink("ELR Report PDF", "ELR_Report_Pdf", "PdfTest",
    new { testId = 24, appointmentId = 1, patientId = 1, officeId = 1, imageBytesId = 1, numPages = 10, repDoc = 1, refDoc = 1, famDoc = 1, ccDoc = 1 }, null) <br />
@Html.ActionLink("Pedriatic cho Report PDF", "PedriaticEcho_Report_Pdf", "PdfTest",
    new { testId = 14, appointmentId = 1, patientId = 1, officeId = 1, imageBytesId = 1, numPages = 10, refDoc = 1, technId = 1 }, null) <br />

<br />
@Html.ActionLink("GENERIC Report PDF", "Generic_Report_Pdf", "PdfTest",
    new { testId = 7, appointmentId = 1, patientId = 6, officeId = 2, imageBytesId = 1, numPages = 10, refDoc = 1, technId = 1 }, null) <br />
<br />
@Html.ActionLink("HL7 Pdf Test", "HL7_Report_Pdf", "PatientChart",
    new { patientRecordId = 4157, hl7ReportId = 2412 ,area =""}, new { target = "" }) <br />