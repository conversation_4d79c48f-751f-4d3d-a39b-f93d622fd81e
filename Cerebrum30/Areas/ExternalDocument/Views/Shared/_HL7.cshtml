@model Cerebrum.ViewModels.ExternalDocument.ExternalDocumentAssignmentResponse

<style type="text/css">
    #hl7-container {
        padding-top: 15px;
    }

        #hl7-container input, #hl7-container select, #hl7-container textarea {
            max-width: 100%;
        }

        #hl7-container .form-group {
            margin-bottom: 6px;
        }

        #hl7-container .clearable {
            background: #fff url(Content/Images/close-button.gif) no-repeat right -10px center;
            padding: 3px 18px 3px 4px; /* Use the same right padding (18) in jQ! */
            border-radius: 3px;
            transition: background 0.4s;
        }

            #hl7-container .clearable.x {
                background-position: right 5px center;
            }
            /* (jQ) Show icon */
            #hl7-container .clearable.onX {
                cursor: pointer;
            }
            /* (jQ) hover cursor style */
            #hl7-container .clearable::-ms-clear {
                display: none;
                width: 0;
                height: 0;
            }
        /* Remove IE default X */

        #hl7-container .ui-dialog-titlebar {
            background-color: steelblue;
            color: yellow;
        }
</style>

<script type="text/javascript" src="~/Areas/ExternalDocument/Scripts/HL7.js"></script>
<script type="text/javascript">
    var hasError = true;
    var hl7OfficeId = -1;
    var tbdPatientRecordId = -1;
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        @:hasError = false;
        @:hl7OfficeId = "@Model.officeId";
        @:tbdPatientRecordId = "@Model.tbdPatientRecordId";
     }

</script>

<div id="hl7-container">
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="body-content">
            <div class="col-sm-5 form-horizontal">
                <div class="form-group">
                    <label class="col-sm-3 text-right">
                        Patient
                    </label>
                    <div class="col-sm-6" id="patientDiv" name="patientDiv"></div>
                    <div class="col-sm-3" id="acceptPatientDiv" name="acceptPatientDiv">
                        <input id="chkAcceptPatient" name="chkAcceptPatient" type="checkbox">Accept Patient
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 text-right control-label">
                        Re-assign to
                    </label>
                    <div class="col-sm-9">
                        @Html.Hidden("hl7DemographicPatientRecordDoctorId", "")
                        @Html.Hidden("hl7PatientName", "")
                        @Html.TextBox("hl7PatientNameInput", "", new { @class = "form-control clearable", @placeholder = "" })
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 text-right">
                        Test Date
                    </label>
                    <div class="col-sm-9" id="testDateDiv" name="testDateDiv"></div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">
                        Abnormal
                    </label>
                    <div class="col-sm-9" id="abnormalDiv" name="abnormalDiv" style="padding-top: 8px;"></div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">
                        Mark Seen
                    </label>
                    <div class="col-sm-9" id="markSeenDiv" name="markSeenDiv" style="padding-top: 8px;"></div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3">
                        Assign To
                    </label>
                </div>
                <div class="col-sm-12" style="height: 400px; padding-left: 0px; overflow-y: auto;">
                    <table class="table" id="hl7RequisitionList" name="hl7RequisitionList" style="font-size: 14px; width: 100%">
                        <thead>
                            <tr style="font-size: 16px; font-weight: bold;">@*class="success vertical-center"*@
                                <td>Requisition / Loose Report</td>
                                <td>Date</td>
                                <td></td>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                @*<div class="col-sm-12 text-center" style="margin-top: 16px;">
                        <input type="button" id="buttonAssign" name="buttonAssign" value="Assign" class="btn btn-info" />
                    </div>*@
            </div>
            <div class="col-sm-7">
                <div class="col-sm-12" style="padding-left: 0px; padding-right: 0px;">
                    <table class="table" id="hl7List" name="hl7List" style="font-size: 14px; width: 100%">
                        <thead>
                            <tr style="font-size: 16px; font-weight: bold;">@*class="success vertical-center"*@
                                <td>Received Date</td>
                                <td>Abnormal</td>
                                <td>Comment</td>
                                <td></td>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="col-sm-12 text-right" style="padding-right: 0px;">
                    <ul class="pagination" id="pagination" name="pagination" style="margin: 0; margin-bottom: 16px;"></ul>
                </div>
                <div id="externalDocumentHL7Div" name="externalDocumentHL7Div" class="col-sm-12" style="height: 660px; overflow-y: auto; padding-left: 0px;">
                </div>
            </div>
        </div>
        <div id="requisitionFormDialogHl7" name="requisitionFormDialogHl7" style="display:none;"></div>
    }
    else
    {
        <div class="text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>
