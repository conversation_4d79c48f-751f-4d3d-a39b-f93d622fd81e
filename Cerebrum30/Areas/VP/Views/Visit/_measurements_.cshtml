@model Cerebrum.ViewModels.VP.VMMeasurementsMain

@{ 
    var labsMain = new Cerebrum.ViewModels.VP.VMLabsMain();
    labsMain.HL7_CollectionDate = Model.HL7_CollectionDate;
    labsMain.LabResultCategories = Model.LabResultCategories;

    var hasDBCollectionDateJS = Model.DB_CollectionDate.HasValue ? "1" : "0";
    var hasLabDateValue = Model.LabResultDate.HasValue;
    var labDateStr = hasLabDateValue ? Model.LabResultDate.Value.ToString("MM/dd/yyyy") : "";
    var collectionDateStr = Model.HL7_CollectionDate.HasValue ? Model.HL7_CollectionDate.Value.ToString("MM/dd/yyyy") : "";
    var dbDateStr = Model.DB_CollectionDate.HasValue ? Model.DB_CollectionDate.Value.ToString("MM/dd/yyyy") : "";
}

<script>
    $(document).ready(function () {
        $('[data-toggle="tooltip"]').tooltip({ animation: false });        
    });
</script>
@*@for (int i = 0; i < Model.MeasurementsCategories.Count; i++)
{
    @Html.HiddenFor(x => x.MeasurementsCategories[i].CategoryType)
    for (int j = 0; j < @Model.MeasurementsCategories[i].Measurements.Count; j++)
    {
        @Html.HiddenFor(x => x.MeasurementsCategories[i].Measurements[j].Id)
        @Html.HiddenFor(x => x.MeasurementsCategories[i].Measurements[j].Name)
    }
}*@

<div id="div-vp-ms" class="__009857 panel panel-info">
    <div class="panel-heading">Measurements</div>
    <div class="panel-body">


        <div class="row">
            <div class="col-sm-12">
                <div class="col-sm-3">
                    <span class="small">Source: </span>
                    <span class="small green">@Model.Source</span>
                </div>
                <div class="col-sm-3">
                    <span class="small">CDF Date: </span>
                    <span class="small">@Model.CDF_CollectionDate</span>
                </div>
                <div class="col-sm-3">
                    <div>
                        <span class="small smallertext">HL7 Collection Date: </span>
                        <span id="hl7-lab-collection-date" class="small">@collectionDateStr</span>
                    </div>
                    <div>
                        <div class="pull-left">
                            <span class="small smallertext">Lab Results Date: </span>
                        </div>
                        <div id="vp-lab-results-date-holder" class="pull-left">
                            <input type="text" id="LabResultDate" name="LabResultDate" class="form-control date-picker " readonly="readonly" value="@labDateStr" />
                            <input type="hidden" id="HL7ReportId" name="HL7ReportId" value="@Model.HL7ReportId" />
                        </div>
                        <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Clear Lab Results Date" class="glyphicon glyphicon glyphicon-minus-sign btn-clear-lab-results-date c-pointer" style="color:red;margin-left:3px;margin-top:5px;"></span>
                    </div>
                </div>
                <div class="col-sm-3 pull-right">
                    <span class="small smallertext">DB Date: </span>
                    <span data-has-db-collection-date="@hasDBCollectionDateJS" id="db-lab-collection-date" class="small">@Model.DB_CollectionDate</span>
                </div>
            </div>
        </div>

        <div class="row">&nbsp;</div>

        @for (int i = 0; i < Model.VMCDF.TemplateDetails.Count; i++)
        {
            @Html.Hidden("VMCDF.TemplateDetails[" + i + "].TemplateItemName", Model.VMCDF.TemplateDetails[" + i + "].TemplateItemName)
            @Html.Hidden("VMCDF.TemplateDetails[" + i + "].TestCode", Model.VMCDF.TemplateDetails[i].TestCode)
            @Html.Hidden("VMCDF.TemplateDetails[" + i + "].VPTemplateField", Model.VMCDF.TemplateDetails[" + i + "].VPTemplateField)
        }
        <div class="visit-measurements">
            
                @for (int i = 0; i < Model.MeasurementsCategories.Count; i++)
                {
                <div>
                   
                    @if (Model.MeasurementsCategories[i].CategoryType == AwareMD.Cerebrum.Shared.Enums.MeasurementCategoryType.Vitals)
                    {
                        <div class="form-inline">
                            <div class="form-group">
                                <span class="custom-label">
                                    @Model.MeasurementsCategories[i].CategoryType.ToString()
                                    @Html.HiddenFor(x => x.MeasurementsCategories[i].CategoryType)
                                </span>

                            </div>
                            <div class="form-group form-group-sm">
                                <div id="div-meas-History">
                                    @Html.RenderPartialAsync("_vitalsHistory", Model.MeasurementsCategories[i].VsHistory); }
                                </div>
                            </div>
                        </div>
                        <table cellpadding="0" class="table custom-table table-condensed table-bordered _0098723 spacer-btm-7">
                                <tr>
                                    <td>
                                        <div class="form-inline">
                                            <ol>
                                                @for (int j = 0; j < @Model.MeasurementsCategories[i].Measurements.Count; j++)
                                                {
                                                    <li class="col-sm-2">
                                                        <div>
                                                            <div>

                                                                <span class="hideOverflowMeas small "
                                                                      data-toggle="tooltip"
                                                                      data-placement="top"
                                                                      tooltip="@Model.MeasurementsCategories[i].Measurements[j].Name"
                                                                      title="@Model.MeasurementsCategories[i].Measurements[j].Name">
                                                                    @Model.MeasurementsCategories[i].Measurements[j].Name
                                                                </span>


                                                                @Html.TextBoxFor(x => x.MeasurementsCategories[i].Measurements[j].Value, new { @class = " txtBox vp-meas-box form-control", @data_test_code = Model.MeasurementsCategories[i].Measurements[j].Testcode, @data_measurement_name = Model.MeasurementsCategories[i].Measurements[j].Name })
                                                                @Html.HiddenFor(x => x.MeasurementsCategories[i].Measurements[j].Id)
                                                                @Html.HiddenFor(x => x.MeasurementsCategories[i].Measurements[j].Name)

                                                                <a data-toggle="tooltip"
                                                                   data-placement="right"
                                                                   class="hideOverflowUnits" title="@Model.MeasurementsCategories[i].Measurements[j].Units">
                                                                    (@Model.MeasurementsCategories[i].Measurements[j].Units)
                                                                </a>

                                                                @{
                                                                    var history = Model.MeasurementsCategories[i].Measurements[j].HistoricalValues.Select(s => "Value :" + s.Value + " ( " + s.UserName + ")").ToArray();
                                                                    var str = string.Join(",", history);
                                                                }
                                                                @if (!string.IsNullOrWhiteSpace(str))
                                                                {
                                                                    <span data-toggle="tooltip"
                                                                          data-placement="right"
                                                                          class="glyphicon glyphicon-time" title="@str">
                                                                    </span>
                                                                }
                                                                @if (!string.IsNullOrEmpty(@Model.MeasurementsCategories[i].Measurements[j].ErrorMessage))
                                                                {
                                                                    <br />
                                                                    <span class="label label-danger">@Model.MeasurementsCategories[i].Measurements[j].ErrorMessage </span>
                                                                }
                                                            </div>
                                                        </div>
                                                    </li>
                                                                    }
                                            </ol>
                                        </div>

                                    </td>
                                </tr>
                                                                    
                        </table>
                                                                    }
                                                                    else
                                                                    {
                                                                        <div class="form-inline">
                                                                            <div class="form-group">
                                                                                <span class="custom-label">
                                                                                    @Model.MeasurementsCategories[i].CategoryType.ToString()
                                                                                    @Html.HiddenFor(x => x.MeasurementsCategories[i].CategoryType)
                                                                                </span>
                                                                                <span class="btn-refresh-labs glyphicon glyphicon-refresh c-pointer" data-categoryId="@Model.MeasurementsCategories[i].vp_measurment_categoryId" data-toggle="tooltip" data-placement="bottom" title="Refresh Labs">&nbsp;</span>
                                                                                @if (CerebrumUser.HasPermission("OLISUser"))
                                                                                {
                                                                                    <span class="pull-right"><a class="" href="@Url.Action("OLISReportSearch", "OLIS", new { area = "Labs", patientId = Model.PatientId })" target="_blank">OLIS Report Search</a></span>
                                                                                }
                                                                            </div>
                                                                            
                                                                        </div>
                                                                        
                                                                        @Html.EditorFor(model => model.MeasurementsCategories[i], "VPLabResultCategory");
                                                                    }
                </div>
             }
           
        </div>



        @if (Model.VMCDF.TemplateDetails.Count > 0)
        {
            <div>
                <span class="custom-label">
                    CDF
                </span>
                <table cellpadding="0" class="table custom-table table-condensed __77654 spacer-btm-7">
                    <tr>
                        <td>
                            @*<p>
                                    <span class="label label-primary label-app-type">
                                        CDF
                                    </span>
                                </p>*@
                            <div class="form-inline">
                                <ol>
                                    @for (int j = 0; j < Model.VMCDF.TemplateDetails.Count(); j++)
                                    {
                                        <li class="col-sm-2">
                                            <div>
                                                <div style="">

                                                    <span class="hideOverflowMeas small "
                                                          style="display:inline-block; float: left"
                                                          data-toggle="tooltip"
                                                          data-placement="top"
                                                          tooltip="@Model.VMCDF.TemplateDetails[j].TemplateItemName"
                                                          title="@Model.VMCDF.TemplateDetails[j].TemplateItemName">
                                                        @Model.VMCDF.TemplateDetails[j].TemplateItemName
                                                    </span>

                                                    @*//TextBoxFor*@

                                                    @{
                                                        var nmvt = $"vm_cdf.TemplateDetails[{j}].ValueType";
                                                        bool isUnit = false;//mutli-line
                                                        if (!string.IsNullOrEmpty(Model.VMCDF.TemplateDetails[j].Units))
                                                        {
                                                            isUnit = true;
                                                        }
                                                    }
                                                   
                                                    @if (Model.VMCDF.TemplateDetails[j].ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.YesNo)
                                                    {
                                                        var v = $"vm_cdf_TemplateDetails_{j}__Value";
                                                        var nm = $"vm_cdf.TemplateDetails[{j}].Value";
                                                       
                                                        AwareMD.Cerebrum.Shared.Enums.YesNo myval;
                                                        List<SelectListItem> items = new List<SelectListItem>();
                                                        if(Enum.TryParse(Model.VMCDF.TemplateDetails[j].Value, out myval))
                                                        {
                                                            items = EnumHelper.GetSelectList(typeof(AwareMD.Cerebrum.Shared.Enums.YesNo), myval).ToList();
                                                        }
                                                        else
                                                        {
                                                            items = EnumHelper.GetSelectList(typeof(AwareMD.Cerebrum.Shared.Enums.YesNo)).ToList();
                                                        }

                                                        @Html.DropDownListFor(x => Model.VMCDF.TemplateDetails[j].Value,items, "...", Model.VMCDF.TemplateDetails[j].Value)


                                                        @Html.Hidden(nmvt, Model.VMCDF.TemplateDetails[j].ValueType)
                                                    }
                                                    else
                                                    {

                                                        @Html.TextAreaFor(x => @Model.VMCDF.TemplateDetails[j].Value, new
                                                   {
                                                       @class = " txtBox vp-meas-box meas-cdf-item form-control",
                                                       @data = @Model.VMCDF.TemplateDetails[j].TemplateItemName,
                                                       @readonly = "readonly",
                                                       @data_toggle = "tooltip",
                                                       @data_placement = "right",
                                                       @title = Model.VMCDF.TemplateDetails[j].Value,
                                                       @style = "white-space:pre; resize: none; overflow:hidden;padding:2px",
                                                       @isunit = @isUnit,
                                                       @rows = 1
                                                   })
                                                        @Html.Hidden(nmvt, Model.VMCDF.TemplateDetails[j].ValueType)
                                                    }
                                                    @*@if (!string.IsNullOrEmpty(Model.vm_cdf.TemplateDetails[j].Units))*@
                                                    <div style="display: inline-block; position: absolute; margin-left: 3px;">
                                                        @if (isUnit)
                                                        {
                                                            <a style="margin-top: -2px"
                                                               data-toggle="tooltip"
                                                               data-placement="right"
                                                               class="hideOverflowUnits"
                                                               title="@Model.VMCDF.TemplateDetails[j].Units">
                                                                (@Model.VMCDF.TemplateDetails[j].Units)
                                                            </a>
                                                        }

                                                        @if (Model.VMCDF.TemplateDetails[j].OverDue)
                                                        {
                                                            <i data-toggle="tooltip"
                                                               title="Out of Treatment Interval" @*OverDue*@
                                                               style="color:red" class="glyphicon glyphicon-time"></i>
                                                        }
                                                    </div>                                                   
                                                </div>
                                            </div>
                                        </li>
                                    }@*For loop ends*@
                                </ol>
                            </div>
                    </tr>
                </table>

            </div>
         }@*If ends*@

    </div>
</div>



<!--------------------- ----->
<div class="container">
    <!-- Modal -->
    <div class="modal fade" id="mdlCDF" role="dialog">

        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Modal Header</h4>

                </div>
                <div class="modal-body">

                    <div id="__placeHolder__"></div>

                    <input type="hidden" id="mdlCDF_ctrlIDToUpdate" />
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default btn-sm btn-primary" data-dismiss="modal" id="btnModal-CDF-update">Update</button>

                    <button type="button" class="btn btn-default btn-sm" data-dismiss="modal" id="btnModal-CDF-close">Close</button>
                </div>
            </div>

        </div>
    </div>
</div>
<script>

    $(document).ready(function () {
        var isUnit;
        var selectedCtrlIDToUpdate;



        $('.meas-cdf-item').on('click', function () {

            selectedCtrlIDToUpdate = $(this).attr('id');
            var selectedCtrlCurrentVal = $(this).val();

            var selectedCtrl_isUnit = $.parseJSON($(this).attr('isunit').toLowerCase());


            var updateCtrl = $('<textarea/>', {
            });

            if (selectedCtrl_isUnit) {
                updateCtrl = $('<input/>', {
                    'type': 'text'
                });
            }


            $.when(updateCtrl.appendTo('#__placeHolder__')).done(function () {
                updateCtrl.attr('id', 'mdlCDF_ctrlNewContent').addClass('cls').val(selectedCtrlCurrentVal);
            });


            $('#mdlCDF .modal-title').html('CDF: ' + $(this).prev('span').html());

            $('#mdlCDF').modal();
        });

        $('#btnModal-CDF-update').on('click', function () {
            var newVal = $('#mdlCDF_ctrlNewContent').val()
                   .replace(/</g, '&lt;')
                   .replace(/>/g, '&gt;')
                   .replace(/\n/g, '<br/>');

            eval(" $('#" + selectedCtrlIDToUpdate + "').val('" + newVal.replace(/<br\s*\/?>/ig, '\\r') + "') ").css('background-color', '#ffe498');
        });


        $('#mdlCDF').on('shown.bs.modal', function (e) { // show: about to be shown
            $('.cls:first').focus();
        });

        $('#mdlCDF').on('hidden.bs.modal', function () {
            $('#__placeHolder__').empty(); //cleanup
        });
    });
</script>