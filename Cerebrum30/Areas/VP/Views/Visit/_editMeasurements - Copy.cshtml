﻿@model Cerebrum.ViewModels.VP.VMMeasurementCustom


@*@{
    var maxColumns = 4;
    var groups = Model.MeasurementItems.GroupBy(x => x.Group).ToList(); // vitals or labs
    var totalGroups = groups.Count();
    var index = 0;
    
   
}*@



@*@using (Html.BeginForm("SaveMeasurements", "visit", new { area = "VP" }, FormMethod.Post, new { @id = "frm-save-custom-measurements" }))
{
    
    <div class="form-horizontal">        
        @Html.HiddenFor(x => x.ExternalDoctorId)
        
        @foreach (var grp in groups)
        {
            var items = grp.ToList();
            var totalItems = items.Count();
            var totalPerColumn = ((int)(totalItems / maxColumns) + 1);
            var divOpen = "<div class=\"col-md-3\">";
            var divClose = "</div>";
            var printDivOpen = false;
            var printDivClose = false;

            <p>
                <span class="label label-primary label-app-type">
                    @grp.Key
                </span>

            </p>

            <div class="form-group form-group-sm">
                @for (int i = 0; i < totalItems; i++)
                {
                    var item = items[i];
                    var inputName = "MeasurementItems[" + index + "]";
                    var inputeId = "MeasurementItems" + index + "__";

                    var valueName = inputName + ".Value";
                    var valueId = inputeId + "Value";
                    var valueValue = item.Value;

                    var selectedName = inputName + ".Selected";
                    var selectedId = inputeId + "Selected";
                    var selectedValue = item.Selected;

                    var textName = inputName + ".Text";
                    var textId = inputeId + "Text";
                    var textValue = item.Text;

                    var codeName = inputName + ".Code";
                    var codeId = inputeId + "Code";
                    var codeValue = item.Code;

                    var groupName = inputName + ".Group";
                    var groupId = inputeId + "Group";
                    var groupValue = item.Group;


                    if (i % totalPerColumn == 0)// start new column
                    {
                        if (i > 0)
                        {
                            @Html.Raw(divClose)
                        }
                        printDivOpen = true;
                    }


                    if (printDivOpen)
                    {
                        printDivOpen = false;
                        @Html.Raw(divOpen)
                    }
                    <div>
                        <input type="hidden" name="MeasurementItems.Index" value="@index" />
                        <input type="hidden" name="@valueName" id="@valueId" value="@valueValue" />
                        <input type="hidden" name="@textName" id="@textId" value="@textValue" />
                        <input type="hidden" name="@codeName" id="@codeId" value="@codeValue" />
                        <input type="hidden" name="@groupName" id="@groupId" value="@groupValue" />

                        @Html.CheckBox(selectedName, selectedValue, new { @id = selectedId }) <span>@textValue</span> <span class="text-success">@codeValue</span>
                    </div>
                    if (totalItems == (i + 1))
                    {
                        printDivClose = true;
                    }

                    if (printDivClose)
                    {
                        printDivClose = false;
                        @Html.Raw(divClose)
                    }

                    index++;
                }

            </div>
        }       
       
    </div>
        
    <div style="margin-top:15px;" class="text-right">
        <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
    </div>             
      
}*@


