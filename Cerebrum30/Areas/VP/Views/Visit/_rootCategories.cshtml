@model Cerebrum.ViewModels.TestBase.VMRootCategoriesInputMain

@{ 
    var rootCategoriesCount = Model.RootCategories.Count();
    var canEditPracticeSettings = CerebrumUser.HasPermission("EditPracticeRootCategories") ? true : false;
    var canEditSettings = CerebrumUser.HasPermission("EditRootCategories") ? true : false;
}

<script>
    $(document).ready(function () {
        buildAllPhrasesForRootCategories();
        $('[data-toggle="tooltip"]').tooltip({ animation: false });
    });
</script>

<div id="div-vp-rp" data-can-edit-settings="@canEditSettings" data-can-edit-practice-settings="@canEditPracticeSettings" class="__00985 AA panel panel-info">
    <div class="panel-heading">
        <div class="form-inline div-padding-5">
            <div class="form-group form-group-sm">
                <label class="control-label" for="PhrasesPracticeRootCatTemplateId">VP Phrases - </label>
            </div>
            <div class="form-group form-group-sm">
                <label class="control-label" for="PhrasesPracticeRootCatTemplateId">Templates</label>
                @Html.DropDownList("PhrasesPracticeRootCatTemplateId", new SelectList(Model.DoctorTemplates, "PracticeTemplateId", "DisplayLookupName", Model.PracticeTemplateId), new { @class = "form-control" })
            </div>
            <div class="form-group form-group-sm pull-right div-bordered-gray">
                <select id="ddlAddToEmptyorAll" class="form-control">
                    <option value="0">Add to Empty Categories</option>
                    <option value="1">Add to All Categories</option>
                </select>
               <input type="text" id="search-root-subcategory" placeholder="search subcategory" class="form-control ui-autocomplete-input"> <input type="button" id="btn-add-category-phrase-default" value="Add" data-subcategory-text="" class="btn btn-primary btn-sm" />
            </div>
        </div>
    </div>
    <div class="panel-body">        
        <div style="margin-top:5px;" class="row">
            <ol id="vp-rep-phrases-list" data-total-phrases="@rootCategoriesCount" data-practice-template-id="@Model.PracticeTemplateId" class="vp-list">
                @for (int i = 0; i < rootCategoriesCount; i++)
                {                    
                    var rootCategory = Model.RootCategories[i];
                    var rootCategoryNameCustom = rootCategory.Name;
                    var rootCategoryNameOriginal = rootCategory.OriginalName;
                    var isMostRecent = rootCategory.OriginalName.ToLower() == "most recent investigations" ? true : false;
                    var practiceTemplateId = rootCategory.PracRootCategoryTempId;                   
                    var showInLetterClass = rootCategory.ShowInLetter ? "glyphicon-eye-open color-green" : "glyphicon-eye-close color-red";
                    var showInLetterTitle = rootCategory.ShowInLetter ? "Show in letter is on" : "Show in letter is off";
                    var enableShowInLetterClass = canEditPracticeSettings || canEditSettings? "btn-set-show-in-letter c-pointer" : "";

                    <span id="<EMAIL>">
                        @Html.Hidden("RootCategories[" + i + "].RootCategoryId", Model.RootCategories[" + i + "].RootCategoryId)
                        @Html.Hidden("RootCategories[" + i + "].PracRootCategoryTempId", Model.RootCategories[i].PracRootCategoryTempId)
                        @Html.Hidden("RootCategories[" + i + "].OriginalName", Model.RootCategories[" + i + "].OriginalName)
                        @Html.Hidden("RootCategories[" + i + "].Name", Model.RootCategories[i].Name)
                        @Html.Hidden("RootCategories[" + i + "].ControlName", Model.RootCategories[" + i + "].ControlName)
                        @Html.Hidden("RootCategories[" + i + "].ShowInLetter, new { @class = "hidden-input-showletter" }", Model.RootCategories[i].ShowInLetter, new { @class = "hidden-input-showletter" })
                    </span>
                   
                                       
                    <li class="root-cat-col-size">
                        <div class="form-inline  ">
                            <div id="<EMAIL>" data-category-phrases="@Newtonsoft.Json.JsonConvert.SerializeObject(rootCategory, Newtonsoft.Json.Formatting.Indented)" class="form-group form-group-sm ul-phrases-holder">                                
                                @* phrases are now loaded with javascript from the data-category-phrases attribute @(await Html.PartialAsync("_rootCategoryItem",rootCategory))*@                                
                            </div>
                            <div class="form-group form-group-sm ">
                                <div class="form-inline">
                                                                       
                                    <div class="form-group form-group-sm ">
                                        <div data-selected=@(Model.RootCategories[i].ShowInLetter ? "1" : "0")
                                            
                                            data-toggle="tooltip"
                                            title="@showInLetterTitle"
                                            data-placement="right"
                                            id="<EMAIL>[i].RootCategoryId"
                                            data-rootcategory-id="@Model.RootCategories[i].RootCategoryId"
                                            data-practice-template-id="@practiceTemplateId"                                          
                                            data-show-in-letter="@Model.RootCategories[i].ShowInLetter.ToString().ToLower()"
                                            data-url="@Url.Action("ShowInLetter","Visit",new { area="VP" })"
                                            style="margin-top: -7px;"
                                            class="vert-center glyphicon @showInLetterClass @enableShowInLetterClass"></div>
                                    </div>

                                    @*<div class="form-group form-group-sm ">
                                        <div data-toggle="tooltip"
                                             title="Enlarge text box"
                                             data-placement="right"
                                             id="<EMAIL>[i].RootCategoryId"
                                             data-rootcategory-id="@Model.RootCategories[i].RootCategoryId"
                                             data-rootcategory-name="@Model.RootCategories[i].Name"
                                             data-practice-template-id="@practiceTemplateId"                                             
                                             style="margin-top: -7px;"
                                             class="vert-center glyphicon glyphicon-zoom-in color-blue btn-root-category-enlarge"></div>
                                    </div>*@
                                    
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                @{
                                    var mostRecent = "";
                                    if (isMostRecent)
                                    {
                                        mostRecent = "mostRecent";
                                    }
                                }

                                @Html.TextAreaFor(x => x.RootCategories[i].Value,
                                    new
                                    {
                                        @id = Model.RootCategories[i].ControlName,
                                        @class = "txtArea txtReportPhrases form-control " + mostRecent,
                                        @cols = 50,
                                        autocmplete = "off",
                                        data_rootcategory_id = rootCategory.RootCategoryId
                                    })
                            </div>
                        </div>
                    </li>
              } @*end for loop*@
            </ol>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        autosize(document.querySelectorAll('textarea'));
    }, false);

    $(document).ready(function(){
        var cnt = @rootCategoriesCount;

        $('.txtReportPhrases').each(function (i, e) {
            var txtBox = $(this);
            var rootCategoryId = txtBox.data('rootcategory-id');
            if (txtBox.text().length == 0) {
                txtBox.css('height', '100px');
            }
            setSelectedPhrases(rootCategoryId);
            countRootCategoryText(txtBox);
        });
        
        var selectorsToExtend = '.txtReportPhrases';
        $(selectorsToExtend).on('keyup paste', function () {
            autosize(this);
            countRootCategoryText($(this));
        }).keyup();

    });
</script>   