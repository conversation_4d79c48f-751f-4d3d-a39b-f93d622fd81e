@model Cerebrum.ViewModels.TestBase.VMPhraseSubItem


@using (Html.BeginForm("SavePhraseSubItem", "visit", new { area = "VP" }, FormMethod.Post, true, new { @id = "frm-save-rp-subitem" }))
{
    var styleCategory = Model.ItemType == Cerebrum.ViewModels.TestBase.ItemType.Category ? "" : "display:none;";
    <div class="form-horizontal">
        @Html.HiddenFor(x => x.PhraseId)
        @Html.HiddenFor(x => x.RootCategoryId)
        @Html.HiddenFor(x => x.ParentId)
        @Html.HiddenFor(x => x.ExternalDoctorId)
        @Html.HiddenFor(x => x.PracticeId)
        @Html.HiddenFor(x => x.PracticePhraseId)     
        @Html.HiddenFor(x => x.PracticeTemplateId)
        @Html.HiddenFor(x => x.GroupId)
        @Html.HiddenFor(x => x.IsEdit)
        @Html.HiddenFor(x => x.ShowToolBar)
            
            
        @if (Model.IsEdit)
        {
            @Html.HiddenFor(x => x.ItemType)
            @Html.HiddenFor(x => x.CategoryName)
        }
        else
        {
            <div class="form-group">
                <label class="col-md-2 control-label" for="name">Create New</label>
                <div class="col-md-10">
                    @foreach (var value in Enum.GetValues(Model.ItemType.GetType()))
                    {
                        @Html.RadioButtonFor(x => x.ItemType, value, new { @class = "btn-spacing rdbtn-subitem-type" })
                        @Html.Label(value.ToString(, ""),new { @class="btn-spacing" })
                    }
                </div>               
            </div>
            
            <div id="subitem-category-holder" style="@styleCategory">
                <div class="form-group">
                    <label class="col-md-2 control-label" for="CategoryName">Category</label>
                    <div class="col-md-10">
                        @Html.TextBoxFor(x => x.CategoryName, new { @class = "form-control" })
                        @Html.ValidationMessageFor(model => model.CategoryName, "", new { @class = "text-danger" })
                        <span class="help-block">Fill in only if its a new category.</span>
                    </div>
                </div>
            </div>
        }            
        
        <div class="form-group">
            <label class="col-md-2 control-label" for="name">Item Name</label>
            <div class="col-sm-10">
                @Html.TextBoxFor(x => x.Name, new { @class = "form-control input-sm" })
                @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
            </div>
        </div>
            
        @if (Model.ShowToolBar)
        {
            <div class="form-group">
                <label class="col-md-2 control-label" for="name">Patient Toolbar</label>
                <div class="col-md-10">
                    <div class="btn-group btn-group-sm" role="group">
                        <button data-btn-value="{FirstName}" type="button" class="btn btn-default btn-patient-toolbar">FirstName</button>
                        <button data-btn-value="{LastName}" type="button" class="btn btn-default btn-patient-toolbar">LastName</button>
                        <button data-btn-value="{Age}" type="button" class="btn btn-default btn-patient-toolbar">Age</button>
                        <button data-btn-value="{Gender}" type="button" class="btn btn-default btn-patient-toolbar">Gender</button>
                        <button data-btn-value="{DOB}" type="button" class="btn btn-default btn-patient-toolbar">DOB</button>
                        <button data-btn-value="{Salutation}" type="button" class="btn btn-default btn-patient-toolbar">Salutation</button>
                    </div>
                </div>
            </div>
                
        }

        <div class="form-group">
            <label class="col-md-2 control-label" for="val">Item Value</label>
            <div class="col-sm-10">
                @Html.TextAreaFor(x => x.Value, new { @class = "form-control input-sm", @rows = "5", @cols = "50" })
                @Html.ValidationMessageFor(model => model.Value, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="row">&nbsp;</div>
    </div>
        
    <div style="margin-top:15px;" class="text-right">
        <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
    </div>             
      
}


