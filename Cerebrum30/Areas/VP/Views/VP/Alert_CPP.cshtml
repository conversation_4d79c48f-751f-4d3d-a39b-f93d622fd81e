@model  Cerebrum.ViewModels.VP.VMCPPAlertList
@{
    ViewBag.ModuleName = "CPP Alerts";
    //Layout = "~/Views/Shared/_LayoutFluidPopup.cshtml";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
@section scripts {    
    <script>
        function clear_form_elements(class_name) {
            jQuery(class_name).find(':input').each(function () {
                switch (this.type) {
                    case 'password':
                    case 'text':
                    case 'textarea':
                    case 'file':
                    case 'select-one':
                    case 'select-multiple':
                    case 'date':
                    case 'number':
                    case 'tel':
                    case 'email':
                        jQuery(this).val('');
                        break;
                    //case 'checkbox':
                    case 'radio':
                        this.checked = false;
                        break;
                }
            });
        }

        var LoadAlertHistory = function () {
           // $("#div-alerthistory").html('<img src="../../Content/fancybox_loading.gif" />');
            var url = $("#div-alerthistory").data("url");
            ajaxCall(url, { patientID:@Model.PatientID }, false, function (data) {
                //console.log(data);
                $("#div-alerthistory").html('');
                $("#div-alerthistory").html(data);
            });
        };


        var LoadPatientName = function () {
            //$("#div-pat-name").html('<img src="../../Content/fancybox_loading.gif" />');
            ajaxCall("Patients/GetPatientInfoVPPages", { patientId:@Model.PatientID }, false, function (data) {
                $("#div-pat-name").html(data);
            });
        }

        function alertSaveButtonClicked () {
            url = 'VP/VP/AddAlertCPP';           
            ajaxCall(url, $('#frm-alert-cpp').serialize(), false, function (data) {
                if(data.Errored == "1"){
                    $("#span-result-add").html('');
                    $("#span-result-add").html(data.Message);
                }
                else{
                    LoadCPP();
                    LoadAlertHistory();
                    $("#span-result-add").html('');
                    $("#span-result-add").html('Changes Saved');
                    clear_form_elements('.divAdd');
                }
            });

            return false;
        };

        $(document).ready(function () {
            LoadPatientName();
            LoadAlertHistory();

            //$(document).on('click', '#btn-alert-edit-save', function (e) {
            //    $('#ajax-loader').show();
            //    e.preventDefault();
            //    url = 'VP/VP/EditAlertCPP';
            //    $.ajax({
            //        type: "POST",
            //        url: url ,
            //        data : $('#frm-alert-cpp-edit').serialize() ,
            //        success: function (data) {
            //            if(data.Errored == "1"){
            //                $("#span-result-edit").html('');
            //                $("#span-result-edit").html(data.Message);
            //                //showNotificationMessage('error', 'Changes not saved');
            //            }
            //            else{
            //                LoadAlertHistory();
            //                LoadCPP();
            //                $("#span-result-edit").html('');
            //                $("#span-result-edit").html('Changes Saved');
            //            }
            //        },
            //        error: function (xhr, thrownError) {
            //            alert("Error while tryng to call  'VP/VP/EditAlertCPP'  " + xhr.status + " " + thrownError);
            //        },
            //        complete : function () {
            //            $('#ajax-loader').hide();
            //        }
            //    });
            //});

            $(document).on('click', '.btn-popover-close', function (e) {
                e.preventDefault();
                $(".popover-btn").popover('hide');
                $('[data-original-title]').popover('hide');
            });

            //$('.hl-delete')
            //    .popover({
            //        trigger: 'manual',
            //        html: true,
            //        placement: 'auto right'
            //    })

            //.click(function(e){

            //    $('#ajax-loader').show();

            //    e.preventDefault();

            //    var box       = $(this);
            //    var title       = "";

            //    var _patientid = $(this).data("patientid");
            //    var _cppType   = $(this).data("cpptype");
            //    var _rowid     = $(this).data("rowid");
            //    var _cntrlName = $(this).data("name");

            //    var data = {  patientID:_patientid,cppType:_cppType,rowid:_rowid, cntrlName:_cntrlName  };
            //    url = 'VP/VP/ReasonForDeletion';

            //    $.ajax({

            //        type:'POST',
            //        url: url ,
            //        data : data,
            //        success: function (data) {

            //            box.attr('data-content',data).popover('show');
            //        },

            //        error: function (xhr, thrownError) {

            //            alert("Error while tryng to call  'VP/VP/ReasonForDeletion'  " + xhr.status + " " + thrownError);
            //        },


            //        complete : function () {

            //            $('#ajax-loader').hide();
            //        }
            //    });

            //});
        });
    </script>
}

<div class="row">&nbsp;</div>
<div id="div-pat-name"></div>
<div class="row">&nbsp;</div>
<div class="row AddAlertCPP">
    <div class="col-sm-12">
        <h4 class="modal-sub-title" style="font-size:18px;">Alerts and Special Needs</h4>
        <div class=""><span style="background-color: #f2f2f2; color: #f28383; font-size: 18px;">Data will be visible in CPP summary page if checkbox is checked preset for all your patients&nbsp;&nbsp;</span></div>
    </div>
</div>

@using (Html.BeginForm("AddAlert", "VP", FormMethod.Post, new { @id = "frm-alert-cpp", model = @Model }))
{
    @Html.HiddenFor(x => x.PatientRecordId)
    @Html.HiddenFor(x => x.PatientID)
    @Html.HiddenFor(x => x.UserID)
    @Html.HiddenFor(x => x.CPPVisibleField.PracticeDoctorId)
    @Html.HiddenFor(x => x.CPPVisibleField.VP_CPP_Category_Id)
    @Html.HiddenFor(x => x.CPPVisibleField.Col5Visible)
    @Html.HiddenFor(x => x.CPPVisibleField.Col7Visible)
    @Html.HiddenFor(x => x.CPPVisibleField.Col8Visible)
    @Html.HiddenFor(x => x.CPPVisibleField.Col9Visible)
    @Html.HiddenFor(x => x.CPPVisibleField.Col10Visible)
    @Html.HiddenFor(x => x.CPPVisibleField.Col11Visible)
    @Html.HiddenFor(x => x.CPPVisibleField.Col12Visible)

    <div class="container topPadding">
        @*<div class="row">
            <div class="col-sm-12 text-center">
                <button id="btnAdd" class="btn btn-primary ">Add New Alert</button>
            </div>
        </div>*@
        <div class="row">&nbsp;</div>
        <div class="row divAdd">
            @*<div class="col-md-12 text-left"><span style="background-color: #f2f2f2; color: #f28383; font-size: 18px;">&nbsp;&nbsp;Data will be visible in CPP summary page if checkbox is checked preset for all your patients&nbsp;&nbsp;<br /><br /></span></div>*@
            <div class="col-sm-12 text-center">
                <div class="form-inline">
                    <div class="row">
                        <div class="spanCell col-sm-4 text-right">
                            @Html.CheckBoxFor(x => x.Visible)
                            Show
                        </div>
                        <div class="col-sm-8 text-left">
                        </div>
                    </div>
                    <div class="row">
                        <div class="spanCell col-sm-4 text-right">
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col1Visible, new { style = "vertical-align: top;" })
                            <span class="spanCell verticalAligned">   Description</span>
                        </div>
                        <div class="col-sm-8 text-left">
                            @Html.TextAreaFor(x => x.Description, new { @class = "txtArea" })
                        </div>
                    </div>
                    <div class="row">
                        <div class="spanCell col-sm-4 text-right">
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col2Visible, new { style = "vertical-align: top;" })
                            <span class="spanCell verticalAligned">  Notes</span>
                        </div>
                        <div class="col-sm-8 text-left">
                            @Html.TextAreaFor(x => x.Note, new { @class = "txtArea" })
                        </div>
                    </div>
                    <div class="row">
                        <div class="spanCell col-sm-4 text-right">
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col3Visible)
                            <span class="spanCell ">  Date Active</span>
                        </div>
                        <div class="col-sm-8 text-left">
                            <div class="col-sm-1 spanCell">Day</div>
                            <div class="col-sm-2">
                                @Html.DropDownListFor(x => x.DateActiveDay, Model.Days, "--Select--", new {})
                            </div>
                            <div class="col-sm-1 spanCell">Month</div>
                            <div class="col-sm-2">
                                @Html.DropDownListFor(x => x.DateActiveMonth, Model.Months, "--Select--", new {})
                            </div>
                            <div class="col-sm-1 spanCell">Year</div>
                            <div class="col-sm-2">
                                @Html.DropDownListFor(x => x.DateActiveYear, Model.Years, "--Select--", new {})
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="spanCell col-sm-4 text-right">
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col4Visible)
                            <span class="spanCell ">  End Date</span>
                        </div>
                        <div class="col-sm-8 text-left">
                            <div class="col-sm-1 spanCell">Day</div>
                            <div class="col-sm-2">
                                @Html.DropDownListFor(x => x.EndDateDay, Model.Days, "--Select--", new {})
                            </div>
                            <div class="col-sm-1 spanCell">Month</div>
                            <div class="col-sm-2">
                                @Html.DropDownListFor(x => x.EndDateMonth, Model.Months, "--Select--", new {})
                            </div>
                            <div class="col-sm-1 spanCell">Year</div>
                            <div class="col-sm-2">
                                @Html.DropDownListFor(x => x.EndDateYear, Model.Years, "--Select--", new {})
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="spanCell col-sm-4 text-right">
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col6Visible, new { style = "vertical-align: top;" })
                            <span class="spanCell verticalAligned">  Submit Date</span>
                        </div>
                        <div class="col-sm-8 text-left">
                        </div>
                    </div>
                </div>
                <div class="row">&nbsp;</div>
                <div class="row">
                    <div class="row text-center">
                        <b> <span style="color:red;" id="span-result-add"></span></b>
                    </div>
                </div>
                <div class="row">&nbsp;</div>
                <button class="btn btn-primary" onclick="return alertSaveButtonClicked();">Save</button>
            </div>
        </div>
    </div>
        <div id="div-alerthistory"  data-url="@Url.Action("Alert_CPP_Data", "VP", new { Area = "VP" })"></div>
        <div class="row text-center">
            <button class="btn btn-default btn-sm btn-cancel-model ">
                <i style="color:red" class="glyphicon glyphicon-remove"></i>Close
            </button>
        </div>
        <br />
        <br />
}


