@model Cerebrum.ViewModels.VP.VMCPPAlert
 
<script>
    function saveAlertData() {
        url = 'VP/VP/EditAlertCPP';
        ajaxCall(url, $('#frm-alert-cpp-edit').serialize(), false, function (data) {
            if (data.Errored == "1") {
                $("#span-result-add").html('');
                $("#span-result-add").html(data.Message);
                //showNotificationMessage('error', 'Changes not saved');
            }
            else {
                $("#span-result-add").html('');
                //showNotificationMessage('success', 'Changes saved');
                LoadAlertHistory();
                //LoadCPP();
                $("#button-alert-close").click();
            }
        });

        return false;
    }
</script>

@using (Html.BeginForm("EditAlert", "VP", FormMethod.Post, new { @id = "frm-alert-cpp-edit", model = @Model }))
{
    @Html.HiddenFor(x => x.Id)
    @Html.HiddenFor(x => x.PatientRecordId)
    @Html.HiddenFor(x => x.Status)
    <div class="col-sm-12 text-center">
        <div class="form-inline">
            <div class="row">
                <div class="spanCell col-sm-2 text-right">
                    <span class="spanCell verticalAligned">   Show</span>
                </div>
                <div class="col-sm-10 text-left">
                    @Html.CheckBoxFor(x => x.Visible)
                </div>
            </div>
            <div class="row">
                <div class="spanCell col-sm-2 text-right">
                    <span class="spanCell verticalAligned">   Description</span>
                </div>
                <div class="col-sm-10 text-left">
                    @Html.TextAreaFor(x => x.Description, new { @class = "txtArea" })
                </div>
            </div>
            <div class="row">
                <div class="spanCell col-sm-2 text-right">
                    <span class="spanCell verticalAligned">  Notes</span>
                </div>
                <div class="col-sm-10 text-left">
                    @Html.TextAreaFor(x => x.Notes, new { @class = "txtArea" })
                </div>
            </div>
            <div class="row">
                <div class="spanCell col-sm-2 text-right">
                    <span class="spanCell ">  Date Active</span>
                </div>
                <div class="col-sm-10 text-left">
                    <div class="col-sm-1 spanCell">Day</div>
                    <div class="col-sm-2">
                        @Html.DropDownListFor(x => x.DateActive_Day, Model.Days, "--Select--", new {})
                    </div>
                    <div class="col-sm-1 spanCell">Month</div>
                    <div class="col-sm-2">
                        @Html.DropDownListFor(x => x.DateActive_Month, Model.Months, "--Select--", new {})
                    </div>
                    <div class="col-sm-1 spanCell">Year</div>
                    <div class="col-sm-2">
                        @Html.DropDownListFor(x => x.DateActive_Year, Model.Years, "--Select--", new {})
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="spanCell col-sm-2 text-right">
                    <span class="spanCell ">  End Date</span>
                </div>
                <div class="col-sm-10 text-left">
                    <div class="col-sm-1 spanCell">Day</div>
                    <div class="col-sm-2">
                        @Html.DropDownListFor(x => x.EndDate_Day, Model.Days, "--Select--", new {})
                    </div>
                    <div class="col-sm-1 spanCell">Month</div>
                    <div class="col-sm-2">
                        @Html.DropDownListFor(x => x.EndDate_Month, Model.Months, "--Select--", new {})
                    </div>
                    <div class="col-sm-1 spanCell">Year</div>
                    <div class="col-sm-2">
                        @Html.DropDownListFor(x => x.EndDate_Year, Model.Years, "--Select--", new {})
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-12 text-center" style="margin-top: 32px;">
        <div class="col-sm-6">
            <button class="btn btn-default" onclick="return saveAlertData();">Save</button>
        </div>
        <div class="col-sm-6">
            <button class="btn btn-default" data-dismiss="modal" id="button-alert-close">Close</button>
        </div>
    </div>
    <br />
}