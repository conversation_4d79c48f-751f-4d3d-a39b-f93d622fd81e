@model Cerebrum.ViewModels.VP.CareElementHeaderGroup
<div>
    <table cellpadding="0" class="table table-bordered table-condensed tbl-item">
        @if (!string.IsNullOrWhiteSpace(Model.HeaderGroup))
            {
            <tr>
                <td colspan="4">@Html.Raw(Model.HeaderGroup)</td>
            </tr>

        }
        @foreach (var item in Model.Items)
        {
            if (item.ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.YesNo)
            {
                <tr>
                    <td colspan="2">
                        <div class="float_l" style="min-width:283px">

                            <span data-toggle="tooltip"
                                  title="@item.VPTemplateFieldName"
                                  data-placement="top"
                                  class="c-pointer">
                                <b>
                                    @Html.Raw((string.IsNullOrEmpty(item.ShortName) ? @item.VPTemplateFieldName : @item.ShortName))
                                </b>
                            </span><span @*style="color:green"*@ class="units" data-toggle="tooltip" data-placement="right" data-original-title="@item.Units">@item.Units</span> (low - high)
                        </div>
                    </td>
                    <td>
                        <div class="col-sm-12">
                            @Html.DropDownListFor(x => item.VPTemplateFieldName, EnumHelper.GetSelectList(typeof(AwareMD.Cerebrum.Shared.Enums.YesNo)), "...", new
                       {
                           VPTemplateField = @item.VPTemplateFieldId,
                           @class = "txtEntry"
                       })
                         
                        </div>
                    </td>
                </tr>
            }
            else if (item.ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.NumericInt || item.ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.NumericDecimal)
            {
                <tr style="background-color:#f2f2f2" class="txt-small">
                    <td colspan="2">
                        <div class="float_l" style="min-width:283px">

                            <span data-toggle="tooltip"
                                  title="@item.VPTemplateFieldName"
                                  data-placement="top"
                                  class="c-pointer">
                                <b>
                                    @Html.Raw((string.IsNullOrEmpty(item.ShortName) ? @item.VPTemplateFieldName : @item.ShortName))
                                </b>
                            </span><span @*style="color:green"*@ class="units" data-toggle="tooltip" data-placement="right" data-original-title="@item.Units">@item.Units</span> (low - high)
                        </div>
                    </td>
                    <td>
                        <div>
                            @Html.TextBox("Value", item.Value, new {
                                                         VPTemplateField = @item.VPTemplateFieldId,
                                                         @class = "txtEntry txtBox",
                                                         @placeholder = "Value"
                                                     })
                        </div>
                    </td>
                </tr>
                <tr class="txt-small">
                    <td style="width:33%">N: (@item.NL = @item.NH)</td>
                    <td style="width:33%">T: (@item.TL - @item.TH )  </td>
                    <td style="width:33%">Frequency: @item.Frequency </td>
                </tr>
            }
            else if (item.IsText || item.ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.Text)
            {
                <tr>
                    <td colspan="4">
                        <div class="col-sm-2">
                            <b>
                                <span data-toggle="tooltip"
                                      title="@item.VPTemplateFieldName"
                                      data-placement="top" class="c-pointer">
                                    @Html.Raw((string.IsNullOrEmpty(item.ShortName) ? @item.VPTemplateFieldName : @item.ShortName))
                                </span>
                            </b>
                            <span @*style="color:green"*@ class="units" data-toggle="tooltip" data-placement="right" data-original-title="@item.Units">@item.Units</span>

                        </div>
                        <div class="col-sm-2">
                            @Html.TextAreaFor(s => item.Value,
                            new
                            {

                                // @id = "abc-"+@item.ID,
                                VPTemplateField = @item.VPTemplateFieldId,
                                @class = "txtEntry",
                                @rows = 4,
                                @cols = 60
                            })
                        </div>
                    </td>
                </tr>
            }

        }
    </table>

</div>
