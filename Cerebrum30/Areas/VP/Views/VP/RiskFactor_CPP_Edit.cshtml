@model Cerebrum.ViewModels.VP.VMRiskFactor

<script>
    //$(document).on('click', '#btnSave-rf-edit', function (e) {
    function saveRiskFactorData() {
        url = 'VP/VP/Save_RiskFactor';
        ajaxCall(url, $('#frm-rf-edit').serialize(), false, function (data) {
            if (data.Errored == "1") {
                alert(data.Message);
                //showMessageModal("error", data.Message, false);
            }
            else {
                LoadHistory();
                //LoadCPP();
                $("#span-result-edit").html('');
                $("#span-result-edit").html('Changes Saved');
                $("#button-risk-factor-close").click();
            }
        });

        return false;
    };

    function OnsetAgeChanged() {
        $.ajax({
            type: "POST",
            url: 'VP/VP/GetLifeStage',
            data: { days: $("#VP_RiskFactor_Edit_OnsetAge").val(), option: $("#VP_RiskFactor_Edit_Unit").val() },
            success: function (data) {
                if (data.Result == "0") {
                    $("#VP_RiskFactor_Edit_LifeStage").val(data.Message);
                }
            },
            error: function (xhr, thrownError) {
                alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
            }
        });
    }
</script>

@using (Html.BeginForm("RiskFactor", "VP", FormMethod.Post, new { @id = "frm-rf-edit", model = @Model }))
{
    @Html.HiddenFor(x => x.VP_RiskFactor_Edit.Id)
    @Html.HiddenFor(x => x.VP_RiskFactor_Edit.PatientId)
    <div class="text-center">

        <div class="topPadding" id="divAdd">
            <div class="row">
                <div class="col-md-12 text-center">
                    <div class="rowContainer">

                        <div class="row">
                            <div class="spanCell col-sm-3 text-right">
                                Show :
                            </div>
                            <div class="col-sm-9 text-left">
                                @Html.CheckBoxFor(m => m.VP_RiskFactor_Edit.Visible)
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-3 text-right">
                                Risk Factors :
                            </div>
                            <div class="col-sm-9 text-left">
                                @Html.TextAreaFor(m => m.VP_RiskFactor_Edit.RiskFactor, new { @class = "txtArea" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-3 text-right">
                                Exposure Details :
                            </div>
                            <div class="col-sm-9 text-left">
                                @Html.TextAreaFor(m => m.VP_RiskFactor_Edit.ExposureDetails, new { @class = "txtArea" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-3 text-right">
                                Notes :
                            </div>
                            <div class="col-sm-9 text-left">
                                @Html.TextAreaFor(m => m.VP_RiskFactor_Edit.Notes, new { @class = "txtArea" })
                            </div>
                        </div>
                        <div class="row" style="margin-bottom: 4px;">
                            <div class="spanCell col-sm-3 text-right">
                                Start Date :
                            </div>
                            <div class="col-sm-9 text-left">
                                <div class="col-sm-1 spanCell">Day</div>
                                <div class="col-sm-3">
                                     @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.StartDateDay, Model.Days, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell" style="padding-right: 8px;">Month</div>
                                <div class="col-sm-3">
                                    @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.StartDateMonth, Model.Months, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell">Year</div>
                                <div class="col-sm-3">
                                    @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.StartDateYear, Model.Years, "--Select--", new {})
                                </div>
                            </div>
                        </div>

                        <div class="row" style="margin-bottom: 4px;">
                            <div class="spanCell col-sm-3 text-right">
                                End Date :
                            </div>
                            <div class="col-sm-9 text-left">
                                <div class="col-sm-1 spanCell">Day</div>
                                <div class="col-sm-3">
                                    @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.EndDateDay, Model.Days, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell" style="padding-right: 8px;">Month</div>
                                <div class="col-sm-3">
                                    @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.EndDateMonth, Model.Months, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell">Year</div>
                                <div class="col-sm-3">
                                    @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.EndDateYear, Model.Years, "--Select--", new {})
                                </div>
                            </div>
                        </div>

                        <div class="row" style="margin-bottom: 4px;">
                            <div class="spanCell col-sm-3 text-right">
                                Age of Onset :
                            </div>
                            <div class="col-sm-9 text-left">
                                @Html.TextBoxFor(m => m.VP_RiskFactor_Edit.OnsetAge, new { @class = "txtBox", onblur = "OnsetAgeChanged()" })
                                @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.Unit, Model.Units, "--Select--", new { onchange = "OnsetAgeChanged()" })
                                <span class="spanCell">Life Stage </span>
                                @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.LifeStage, Model.LifeStages, "--Select--", new {})
                                @*@Html.TextBoxFor(m => m.VP_RiskFactor_Edit.LifeStage, new { @class = "txtBox", @readonly = "readonly" })*@
                            </div>
                        </div>

                        <div class="row" style="margin-bottom: 4px;">
                            <div class="spanCell col-sm-3 text-right">
                                Position :
                            </div>
                            <div class="col-sm-9 text-left">
                                @Html.TextBoxFor(m => m.VP_RiskFactor_Edit.Position, new { @class = "largetxtBox" })
                            </div>
                        </div>

                        <div class="row" style="margin-bottom: 4px;">
                            <div class="spanCell col-sm-3 text-right">
                                Status :
                            </div>
                            <div class="col-sm-9 text-left">
                                @Html.DropDownListFor(m => m.VP_RiskFactor_Edit.Status, Model.ActiveList, "--Select--", new {})
                            </div>
                        </div>

                        <div class="row">
                            &nbsp;
                        </div>

                        <div class="row">
                            <div class="col-sm-6">
                                <button class="btn btn-default" onclick="return saveRiskFactorData();">Save</button>
                            </div>
                            <div class="col-sm-6">
                                <button class="btn btn-default" data-dismiss="modal" id="button-risk-factor-close">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="div-history" data-url="@Url.Action("RiskFactor_CPP_Data", "VP", new { Area = "VP" })"></div>
    </div>
}