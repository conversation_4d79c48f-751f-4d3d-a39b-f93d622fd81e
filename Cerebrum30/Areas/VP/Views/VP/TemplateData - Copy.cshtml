@model Cerebrum30.Areas.VP.Models.ViewModels.VP_Template_Patient_Data_VM
@{
    ViewBag.ModuleName = "Template Data";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section topscripts{
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-ui-timepicker-addon/1.6.3/jquery-ui-sliderAccess.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jquery-ui-timepicker-addon/1.6.3/jquery-ui-timepicker-addon.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-ui-timepicker-addon/1.6.3/jquery-ui-timepicker-addon.min.js"></script>

}
<script src="//cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="//cdn.datatables.net/1.10.13/css/jquery.dataTables.min.css" />

@Html.HiddenFor(x => x.PatientID)
@Html.HiddenFor(x => x.AppointmentID)
@Html.HiddenFor(x => x.AppointmentTestLogID)
@Html.HiddenFor(x => x.TemplateID)

@section scripts {
}
<style>
    .green-color {
        color: green;       
    }

    .red-color {
        color: red;
    }

    .table {
        margin-bottom: 0px;
    }

    .smalltextbox {
        padding-left: 5px;
        /*width: 80px;
        font-size: 11px;*/
            height: 25px !important;
            padding: 5px 5px !important;
            font-size: 11px !important;
            line-height: 1.25 !important;
    }

    /*.table-striped > tbody > tr:nth-child(2n+1) > td, .table-striped > tbody > tr:nth-child(2n+1) > th {
        background-color: #e8f2f8;
    }*/

    .tbl-item {
        margin-bottom: 3px !important;
        border: 2px solid #f2f2f2 !important;
       
    }

    .tbl-item td {
        border: 0px !important;          
    }

    .cnt-templateData {
        /*margin-top: 30px;*/
    }

    .txt-small {
        font-size: 11px;
    }

    .btn-popover-container .btn-popover-title,
    .btn-popover-container .btn-popover-content,
    .btn-modal-container .btn-modal-content {
        display: none;
    }

    .btn-popover-container {
    }

    /*.popover-content {
        height: 170px;
    }*/

    .top-legend {
        display: block;
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
        padding-top: 10px;
        font-size: 10px;
    }

    .top-legend li {
        float: left;
    }

    .top-legend .legend-label {
        font-weight: bold;
        padding-top: 3px;
    }

    .top-legend .li-color-desc {
        padding: 3px;
        border-right: 1px solid #999999;
        border-top: 1px solid #999999;
        border-bottom: 1px solid #999999;
    }

    .top-legend li:nth-child(2) {
        -webkit-border-top-left-radius: 3px;
        -webkit-border-bottom-left-radius: 3px;
        -moz-border-radius-topleft: 3px;
        -moz-border-radius-bottomleft: 3px;
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
        border-left: 1px solid #999999;
    }

    .top-legend li:last-child {
        -webkit-border-top-right-radius: 3px;
        -webkit-border-bottom-right-radius: 3px;
        -moz-border-radius-topright: 3px;
        -moz-border-radius-bottomright: 3px;
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
        border-left: none;
    }

    .legend-name {
        padding-right: 3px;
    }

    .legend-color-container {
        width: 15px;
        height: 15px;
        border-radius: 2px;
        margin-top: 1px;
        margin-right: 3px;
    }
</style>
<!-- ^^^^^^^^^^^^^^^^^ SIDE PANEL ^^^^^^^^^^^^^^^^^^^^ -->
<style>
    #zzz {
        top: 51px;
        right: -488px;
        width: 487px;
        position: fixed;
        z-index: 1022;
        background-color: #808080;
        overflow-x: hidden;
        padding-top: 50px;
        /* transition: 0.5s; */
        height: 100%;
    }

        #zzz .closebtn {
            position: absolute;
            top: 0;
            font-size: 36px;
            height: 20px;
        }

        #zzz a {
            padding: 8px 8px 8px 20px;
            text-decoration: none;
            display: block;
            transition: .1s;
            color: #f1f1f1;
        }

    .lbl-open-pnl {
        /*color: #fff;
        font-size: 14px;*/
    }

    #pnl_history #txtDate {
        height: 25px;
        font-size: 11px;
        padding-left: 5px;
    }


    #pnl_history #btnSave {
        margin-left: 3px;
        margin-top: 1px;
    }

    dt {
        font-weight: normal !important;
    }
</style>
<script>
    var menuVisible = false;
    function expandContractOnScreen1(arg) {
        if (arg) {
            $('#zzz').animate({ right: 0 }, { duration: 350, easing: 'easeInOutExpo', complete: function () { menuVisible = true } })
        } else {
            $('#zzz').animate({ right: -488 }, { duration: 350, easing: 'easeInOutExpo', complete: function () { menuVisible = false } })
        }
    }

    function openSidebar1() {
        expandContractOnScreen1(true);
    }

    function closeSidebar1() {
        expandContractOnScreen1(false);
    }

    function setHistoryHeight(){
        $('#pnl_history').css('height', $(document).height()-160);
    }

    $(document).ready(function () {
        setHistoryHeight();
    });

    $(window).resize(function () {
        setHistoryHeight();
    });
</script><!-- templateData -->
<div id="div-pat-name"></div>
<!-- templateData -->
<br /><br />
<div id="sidebar-right-open" class="_toClone17" style="display:none">
    <span class="c-pointer lbl-open-pnl" onclick="openSidebar1()"><span class="glyphicon glyphicon-list f25"></span> <span>Open Panel</span></span>
</div>

<div id="zzz">
    <div class="float_l" style="overflow: auto; width: 447px; margin-left: 12px" id="pnl_history">
        <!-- overflow:auto; -->
        <div class="float_l">
            <a href="javascript:void(0)" class="closebtn" onclick="closeSidebar1()">&times;</a>
        </div>

        <div class="float_l" style="position: absolute; top: 20px; left: 257px">
            <div class="float_l margin-left-15" style="width:250px;">
                <div class="float_l form-group form-group-sm"><span style="color: #dad8d6">Add Data</span>&nbsp;<input id="txtDate" class="" placeholder="Datetime" /></div>&nbsp;
                <button class="btn btn-default btn-xs btn-save-template-data float_l" id="btnSave">Save</button>
            </div>
        </div>

        @foreach (var item in Model.HeaderItems)
        {
            <table cellpadding="0" class="table table-bordered table-condensed tbl-item">
                @if (item.IsText)
                {
                    <tr>
                        <td colspan="4">
                            <div class="col-sm-2">
                                <b>
                                    <span data-toggle="tooltip"
                                          title="@item.VPTemplateFieldName"
                                          data-placement="top" class="c-pointer">
                                        @Html.Raw((string.IsNullOrEmpty(item.ShortName) ? @item.VPTemplateFieldName : @item.ShortName))
                                    </span>
                                </b>
                                <span @*style="color:green"*@ class="units" data-toggle="tooltip" data-placement="right" data-original-title="@item.Units">@item.Units</span>                              

                            </div>
                            <div class="col-sm-2">
                                @Html.TextAreaFor(s => item.Value,
                            new
                            {

                                // @id = "abc-"+@item.ID,
                                VPTemplateField = @item.VPTemplateFieldId,
                                @class = "txtEntry",
                                @rows = 10,
                                @cols = 80
                            })
                            </div>
                        </td>
                    </tr>
                }
                else
                {
                    <tr style="background-color:#f2f2f2" class="txt-small">
                        <td colspan="2"> 
                            <div class="float_l" style="min-width:283px">

                                <span data-toggle="tooltip"
                                      title="@item.VPTemplateFieldName"
                                      data-placement="top"
                                      class="c-pointer">
                                    <b>
                                        @Html.Raw((string.IsNullOrEmpty(item.ShortName) ? @item.VPTemplateFieldName : @item.ShortName))
                                    </b>
                                </span><span @*style="color:green"*@ class="units"  data-toggle="tooltip" data-placement="right" data-original-title="@item.Units">@item.Units</span> (low - high)
                            </div>
                        </td>
                        <td>
                            <div>
                                @Html.TextBox("Value", item.Value, new {
                                                         VPTemplateField = @item.VPTemplateFieldId,
                                                         @class = "txtEntry txtBox",
                                                         @placeholder = "Value"
                                                     })
                            </div>
                        </td>
                    </tr>
                    <tr class="txt-small">
                        <td style="width:33%">N: (@item.NL = @item.NH)</td>
                        <td style="width:33%">T: (@item.TL - @item.TH )  </td>
                        <td style="width:33%">Frequency: @item.Frequency </td>
                    </tr>
                }
            </table>
        }
    </div>
</div>
<!-- ^^^^^^^^^^^^^^^^^/SIDE PANEL ^^^^^^^^^^^^^^^^^^^^ -->
<div>
    <div id="print-modal-container" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document" style="width:auto">
            <div class="modal-content">
                <div class="modal-body ">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <div id="vp-modal-content"></div>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</div>

<style>
    .pad-right {
        margin-right:20px;
        display: inline-block;
    }
</style>

<div class="row __77863 cnt-templateData">
    <div class="col-sm-12">
        <div class="topPadding noPrint" @*style="padding:30px;"*@>
            <div id="div-comorbid">&nbsp;</div>
            @*<hr />*@
            <div class="row">
                @*<div class="">*@
                    <div class="col-sm-4 text-left" id="pnl_checkboxes">
                        @*@Html.CheckBoxFor(x => x.OutSideTarget, new { @class = "chk-search-mode ", @name="chk" })
                            <span class="small" for="chk-test-only-ecg ">
                                <label class="label label-default">Out of Target</label>
                            </span>

                            @Html.CheckBoxFor(x => x.OutSideNormal, new { @class = "chk-search-mode ", @name = "1" })
                            <span class="small" for="chk-exclude-ecg "><label class="label label-default">Out of Normal</label></span>

                            @Html.CheckBoxFor(x => x.OutSideInterval, new { @class = "chk-search-mode ", @name = "1" })
                            <span class="small" for="chk-exclude-ecg ">
                                <label class="label label-default">Out of Treatment Interval</label>
                            </span>*@

                        <input type="checkbox" id="_OutSideTarget" data-a="outSideTargetArrayOfIndexes">
                        <span class="small pad-right" for="chk-test-only-ecg">
                            
                            <label class="@*label label-default*@">Out of Target</label>
                            <i data-toggle="tooltip" title="Out of Target" style="color:red" class="glyphicon glyphicon glyphicon-remove-circle"></i>
                        </span>

                        <input type="checkbox" id="_OutSideNormal" data-a="outSideNormalArrayOfIndexes">
                        <span class="small pad-right" for="chk-exclude-ecg">
                            
                            <label class="@*label label-default*@">Out of Normal</label>
                            <i data-toggle="tooltip" title="Out of Normal" style="color:red" class="glyphicon glyphicon-exclamation-sign"></i>
                        </span>

                        <input type="checkbox" id="_OutSideInterval" data-a="outSideIntervalArrayOfIndexes">
                        <span class="small pad-right" for="chk-exclude-ecg">
                           
                            <label class="@*label label-default*@">Out of Treatment Interval</label>
                            <i data-toggle="tooltip" title="Out of Treatment Interval" style="color:red" class="glyphicon glyphicon-time"></i>
                        </span>

                        @*<input type="checkbox" id="_includeMedication" data-a="includeMedication">
                        <span class="small pad-right" for="chk-exclude-ecg">

                            <label class="label label-default">Include Medications</label>
                            @*<i data-toggle="tooltip" title="Out of Treatment Interval" style="color:red" class="glyphicon glyphicon-time"></i>
                        </span>
                        
                        *@
                    </div>

                    <div class="col-sm-4 @*text-center*@ form-group-sm">
                        <span class="small">From </span><input id="txtDateFrom" class="smalltextbox pad-right" />
                        <span class="small">To </span><input id="txtDateTo" class="smalltextbox" />
                        <a href="#" id="hlrefresh" class="btn btn-default btn-xs">Refresh</a>
                        <a href="#" id="btn-show-all" class="btn btn-default btn-xs">Show All</a>
                    </div>

                    <div class="col-sm-4 text-right">
                        <div class="form-inline">
                            @*<div class="form-group ">
                                <ul class="top-legend">
                                    <li>
                                        <div class="pull-left legend-label">
                                            <span class=""> &nbsp;</span>
                                        </div>
                                    </li>
                                    <li class="li-color-desc text-center">
                                        <div class="pull-left legend-name">Out of Target</div>
                                        <i data-toggle="tooltip" title="Outside Target" style="color:red" class="glyphicon glyphicon glyphicon-remove-circle"></i>
                                    </li>
                                    <li class="li-color-desc text-center">
                                        <div class="pull-left legend-name">Out of Normal 44</div>
                                        <i data-toggle="tooltip" title="Outside Normal" style="color:red" class="glyphicon glyphicon-exclamation-sign"></i>
                                    </li>
                                    <li class="li-color-desc text-center">
                                        <div class="pull-left legend-name">Out of Treatment Interval</div>
                                        <i data-toggle="tooltip" title="Outside Normal" style="color:red" class="glyphicon glyphicon-time"></i>
                                    </li>
                                </ul>
                            </div>*@

                            <div class="form-group">
                                <span class="c-pointer btn btn-default btn-xs @*btn btn-default btn-sm*@" id="btn_showHide">Hide/Show Headers</span>
                                <script>
                                    var LoadShowHide = function() {
                                        $("#dialogCDF").dialog({ @* set in __\Cerebrum30\Areas\VP\Views\VP\CDF_Headers.cshtml*@
                                            title: 'Hide/Show Headers'
                                        });
                                    }

                                    $(document).ready(function(){
                                        $('#btn_showHide').on('click',function(){
                                            LoadShowHide();
                                        });
                                    });
                                </script>
                                @*<div class="btn-popover-content">
                                            @(await Html.PartialAsync("CDF_Headers", Model.HeaderItems));
                                    </div>*@
                            </div>
                            @*<div class="form-group">
                                    <div class="popover-btn form-group">
                                        <div class="btn-popover-container">
                                            <span data-toggle="tooltip"
                                                  title="Hide/Show Headers"
                                                  data-placement="right"
                                                  class="text-danger popover-btn c-pointer btn btn-default btn-xs">Hide/Show Headers</span>
                                            <div class="btn-popover-title">Hide/Show Headers </div>
                                            <div class="btn-popover-content">
                                                @(await Html.PartialAsync("CDF_Headers", Model.HeaderItems));
                                            </div>
                                        </div>
                                    </div>
                                </div>*@
                            <div class="form-group ">
                                <a href="#" class="@*btn btn-default btn-sm*@ btn btn-default btn-xs pull-right" id="btn-print">Print</a>
                            </div>
                        </div>
                    </div>
                @*</div>*@
            </div>

            <div class="row">&nbsp;</div>
            <div id="divhistory"> </div>
            <div class="row">&nbsp;</div>
            <div class="row text-center" style="color:red">
                <span id="lblSpan"></span>
            </div>
        </div>
    </div>
</div>

<div id="dialogCDF" style="display: none; min-width: 315px" class="__000926734534">
    <div id="_AAA" class="__778765">
        @(await Html.PartialAsync("CDF_Headers", Model.HeaderItems))
    </div>
</div>

<script>
    $(document).ready(function () {
        $(".btn-popover-container").each(function () {
            var btn = $(this).find(".popover-btn").first();
            var titleContainer = $(this).find(".btn-popover-title").first();
            var contentContainer = $(this).find(".btn-popover-content").first();

            var title = $(titleContainer).html();
            var content = $(contentContainer).html();

            $(btn).popover({
                html: true,
                title: title,
                content: content,
                placement: 'auto top',
                trigger: 'manual' //,
                //container: 'body'
            })
            .on("click", function () {
                var _this = this;
                $(this).popover("show");
            }).on("mouseleave", function () {});
        });

        $(document).on('click','.btn-cancel-model', function(e){
            e.preventDefault();
            $('#print-modal-container').modal('hide');
        });

        $(document).on('click', '.btn-close-status-change', function (e) {
            e.preventDefault();
            $(".popover-btn").popover('hide');
            $('[data-original-title]').popover('hide');
        });

        //$('#txtDate').datetimepicker({
        //    timeFormat: "hh:mm:ss",
        //    changeMonth: true,
        //    changeYear: true,
        //    defaultTime:"00:01:00"
        //});

        //get current date and time
        var d = new Date();
        var month = d.getMonth();
        var day = d.getDate();
        var year = d.getFullYear();
        var hours = d.getHours(); // => 9
        var mins =d.getMinutes(); // =>  30
        var secs = d.getSeconds(); // => 51

        $("#txtDate").datetimepicker({
            ampm: true
        });

        var date = new Date(Date.parse(d ,"mm/dd/yyyy hh:MM tt"));
        $('#txtDate').datetimepicker('setDate', date);

        //$('#txtDate').datetimepicker({
        //    language: 'en',
        //    dateFormat: 'yyyy-MM-dd',
        //    timeFormat: 'hh:mm:ss'
        //    //format: 'yyyy-MM-dd hh:mm:ss'
        //});
        //$('#txtDate').data('datetimepicker').setLocalDate(new Date(year, month, day, hours, mins,secs));

        //$("#txtDate").datepicker();
        $('[data-toggle="tooltip"]').tooltip({ animation: false });



        var ConfirmDialog = function(message) {
            $('<div></div>').appendTo('body')
                            .html('<div><h6>'+message+'?</h6></div>')
                            .dialog({
                                modal: true,
                                title: 'Todays date will be saved as default date since no date is entered', zIndex: 10000, autoOpen: true,
                                width: '600px', resizable: false,
                                buttons: {
                                    Yes: function () {
                                        $(".btn-save-template-data").prop("disabled",true);

                                        var data = GetData();
                                        data = JSON.stringify(data);

                                        //console.log(data);
                                       var tempName= $("#templateName").val();
                                        $.ajax({
                                            type: "POST",
                                            url: "VP/VP/TemplatePatientDataPost",
                                            data: {
                                                json: data ,
                                                templateName:tempName,
                                                TemplateID:@Model.TemplateID,
                                                PatientID:@Model.PatientID,
                                                DateStr: $("#txtDate").val(),
                                                AppointmentID:@Model.AppointmentID
                                                },
                                            //contentType: 'application/json; charset=utf-8',
                                            //dataType: "json",
                                            success: function (data) {

                                                $(".btn-save-template-data").prop("disabled",false);
                                                if(data.Errored)
                                                {
                                                    $("#lblSpan").text(data.Message);
                                                }
                                                else
                                                {
                                                    $("#lblSpan").text('Changes Saved');

                                                    $(".txtEntry").val('');

                                                    LoadHistory();
                                                    opener.LoadMeasurements();                                                 
                                                    //window.location.href =  window.location.href;
                                                }
                                            },
                                            complete : function () {

                                            }
                                        });

                                        $(this).dialog("close");
                                    },
                                    No: function () {

                                        //$('body').append('<h1>Confirm Dialog Result: <i>No</i></h1>');
                                        $(this).dialog("close");
                                    }
                                },
                                close: function (event, ui) {
                                    $(this).remove();
                                }
                            });
        };

        var GetData = function() {
            var result = [];
            $(".txtEntry").each(function () {
                var VPTemplateField = $(this).attr('VPTemplateField');
                var txtBoxVal = $(this).val();
                var newItem = {
                    VPTemplateFieldId   : VPTemplateField ,
                    Value               : txtBoxVal
                };
                result.push(newItem);
            });
            return result;
        };

        var LoadPatientName = function() {
            $("#div-pat-name").html('<img src="../../Content/fancybox_loading.gif" />');
            $.ajax({
                type: 'GET',
                data: { patientId:@Model.PatientID },
                url: 'Patients/GetPatientInfoVPPages',
                cache:false,
                //success: function (data) {
                //    $('#div-pat-name').html(data);
                //}         
            }).done(function (data) {
                $('#div-pat-name').html(data);  
                $.when( $('#div-pat-name').html(data)).then(function( data, textStatus, jqXHR) {
                    var clone = $('._toClone17').clone();
                    clone.show().appendTo($('#_placeHolder17'));
                    //console.log('LoadPatientName');                
                });  
            }).fail(function (jqXHR, textStatus) {
                console.log('error processing patient info menu');
            });
        };     

        var LoadHistory = function(){         
            //alert($('#_IncludeMedication').is(':checked'));
            $("#divhistory").html('<img src="../../Content/fancybox_loading.gif" />');
            $.ajax({
                type: 'POST',
                data: {
                    PatientID:@Model.PatientID,
                    appointmentID:@Model.AppointmentID,
                    appointmentTestLogID:@Model.AppointmentTestLogID,
                    dateFrom: $("#txtDateFrom").val() ,
                    dateTo: $("#txtDateTo").val(),
                    OutSideTarget : $('#_OutSideTarget').is(':checked'),
                    OutSideNormal : $('#_OutSideNormal').is(':checked'),
                    OutSideInterval: $('#_OutSideInterval').is(':checked')
                    //includeMedication: $('#_includeMedication').is(':checked'),
                },
                url: "VP/VP/TemplateHistory",
                cache: false
            })
            .done(function (data) {
                $("#divhistory").html(data);

                var _chrc = _chrc || {};
                _chrc.arr = []; //main array

                _chrc.tblSelector = '#scrollable-table';
                _chrc.totalNumberOfColumns = $(_chrc.tblSelector+' .tbl-header-item > td');

                _chrc.uniqueArray = function (arr) {
                    var result = [];
                    $.each(arr, function (i, e) {
                        if ($.inArray(e, result) == -1) {
                            result.push(e)
                        };
                    });
                    return result;
                };

                _chrc.outSideTargetArrayOfIndexes = $('.outSideTarget').map(function () {
                    return $(this).index();
                }).get();
                $('#_OutSideTarget').attr('data-arr',JSON.stringify(_chrc.uniqueArray(_chrc.outSideTargetArrayOfIndexes));

                _chrc.outSideNormalArrayOfIndexes = $('.outSideNormal').map(function () {
                    return $(this).index();
                }).get();
                $('#_OutSideNormal').attr('data-arr',JSON.stringify(_chrc.uniqueArray(_chrc.outSideNormalArrayOfIndexes));

                _chrc.outSideIntervalArrayOfIndexes = $('.outSideInterval').map(function () {
                    return $(this).index();
                }).get();
                $('#_OutSideInterval').attr('data-arr',JSON.stringify(_chrc.uniqueArray(_chrc.outSideIntervalArrayOfIndexes));

                console.log(_chrc.outSideIntervalArrayOfIndexes);


                _chrc.showHideALLColumns = function (action, selectorLen, tbl) { // where action 'show' | 'hide'
                    //header
                    selectorLen.each(function (i) {
                        $(tbl + ' tr:first').find('td._mm:eq(' + i + ')').filter(function () {
                            return eval('$(this).' + action + '()');
                        })
                    });

                    selectorLen.each(function (i) {
                        $(tbl + ' tr').not(':first').find('td:eq(' + i + ')').filter(function () {
                            return eval('$(this).' + action + '()');
                        })
                    });
                };

                _chrc.showSelected = function (arr) {
                    _chrc.showHideALLColumns('hide', _chrc.totalNumberOfColumns, _chrc.tblSelector);
                    //header
                    $.each(arr, function (i, elm) {
                        $(_chrc.tblSelector + ' tr:first').find('td._mm:eq(' + elm + ')').filter(function () {
                            return $(this).show();
                        })
                    });

                    $.each(arr, function (i, elm) {
                        $(_chrc.tblSelector + ' tr').not(':first').find('td:eq(' + elm + ')').filter(function () {
                            return $(this).show();
                        })
                    });

                    //show Log Date
                    $(_chrc.tblSelector + ' tr').find('td:eq(2)').filter(function () {
                        return eval('$(this).show()');
                    })

                  



                };

                $(function(){
                    $('#_OutSideTarget, #_OutSideNormal, #_OutSideInterval').on('click', function () {
                        (_chrc.arr).length = 0; //reset array                      
                        
                        var ifAnypnl_checkboxesChecked = false;
                        if($('#pnl_checkboxes input[type=checkbox]').filter(':checked').length>0){
                            disableShowHideHeadersCheckboxesAndReset();
                            ifAnypnl_checkboxesChecked = true;
                        }
                        else{                           
                            enableShowHideHeadersCheckboxes();
                        }

                        $('#pnl_checkboxes input[type=checkbox]').each(function () {
                            if ($(this).is(':checked')) {
                                //_chrc.arr = (_chrc.arr).concat(eval('_chrc.'+$(this).attr('data-a'));
                                _chrc.arr = (_chrc.arr).concat(JSON.parse($(this).attr('data-arr'));
                            }
                        });

                        _chrc.arr = _chrc.uniqueArray(_chrc.arr);
                        

                        if ((_chrc.arr).length > 0) {
                            _chrc.showSelected((_chrc.arr));
                        }

                        else if ((_chrc.arr).length == 0 && ifAnypnl_checkboxesChecked) {//<<<<<<<<<<<<<<<<<<<<<<<<TEST
                            _chrc.showHideALLColumns('hide', _chrc.totalNumberOfColumns, _chrc.tblSelector);
                        }

                        else {
                            _chrc.showHideALLColumns('show', _chrc.totalNumberOfColumns, _chrc.tblSelector);
                        }
                    });
                });

            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                //console.log(textStatus);
            })
            .always(function() {
                //var oTable = $('#scrollable-table').DataTable({
                //    "fixedHeader": false,
                //    "sScrollY": "400px",
                //    "sScrollX": true,
                //    "bPaginate": false,
                //    "bSort": false,
                //    "bScrollCollapse": true,
                //    "bFilter": false,
                //    "bJQueryUI": true,
                //    "bScrollCollapse": true,
                //    "bAutoWidth": true,
                //    "sScrollX": "100%",
                //    "sScrollXInner": "100%",
                //    "responsive": true,
                //    "sDom": 'rt<"bottom"i flp>',
                //    "iDisplayLength": -1,
                //    "fnInitComplete": function () {
                //       // console.log("Compelete");
                //        this.fnAdjustColumnSizing(true);
                //    }
                //});



                // $('#scrollable-table').wrap('<div class="dataTables_scroll" />');

                //setTimeout(function () {
                //    $('#scrollable-table').DataTable().search('').draw();
                //}, 10);

                //#btn-select-cdf-all - all
                //.chk-cdf-header


            });
        };


        var LoadCoMorbid = function(){
            $("#div-comorbid").html('<img src="../../Content/fancybox_loading.gif" />');
            $.ajax({
                type: 'POST',
                cache:false,
                data: { PatientID:@Model.PatientID, appointmentID:@Model.AppointmentID },
                url: 'VP/VP/_Comorbid',
                success: function (data) {
                    $('#div-comorbid').html(data);
                }
            });
        };


        var GetCDF_ID_List = function () {
            var result = [];
            $('.chk-select:checkbox:checked').map(function () {
                var rowid = $(this).data('rowid');
                result.push(rowid);
            });   
            if(result.length==0)
            {
                $('.chk-select').map(function () {
                    var rowid = $(this).data('rowid');
                    result.push(rowid);
                });  
            }
            return result;
        };


        var GetCDF_Column_List = function () {    //get called on '#btn-print'    
            var result = [];
            $('.td-cdf-col').each(function () {
                if ($(this).is(':visible')){
                    var rowid = $(this).data('rowid');
                    result.push(rowid);
                }
            });
            return result;
        };






        LoadCoMorbid();
        LoadHistory();
        LoadPatientName();

        


        // events ///////////////////////////////////

        $(document).on('click',".btn-save-template-data", function (e) {
            //$('#ajax-loader').show();
            e.preventDefault();
            if( $("#txtDate").val() == '' ){
                ConfirmDialog('Are you sure');
            }
            else{
                $(".btn-save-template-data").prop("disabled",true);

                var data = GetData();
                data = JSON.stringify(data);
                //console.log(data);

                $.ajax({
                    cache: false,
                    type: "POST",
                    url: "VP/VP/TemplatePatientDataPost",
                    data: {
                        json: data ,
                        TemplateID:@Model.TemplateID,
                        PatientID:@Model.PatientID,
                        DateStr: $("#txtDate").val(),
                        AppointmentID:@Model.AppointmentID
                        },
                    //contentType: 'application/json; charset=utf-8',
                    //dataType: "json",
                    success: function (data) {

                        $(".btn-save-template-data").prop("disabled",false);
                        if(data.Errored)
                        {
                            $("#lblSpan").text(data.Message);
                        }
                        else
                        {
                            $("#lblSpan").text('Changes Saved');

                            $(".txtEntry").val('');

                            LoadHistory();
                            opener.LoadMeasurements();                           
                            //window.location.href =  window.location.href;
                        }
                    },
                    complete : function () {

                        $(".hasDatepicker").val('');
                    }
                });
            }
        });

        $(document).on('click', '#hlrefresh', function (e) {
            e.preventDefault();
            LoadHistory();
        });


        $(document).on('click', '.chk-search-mode', function (e) {
            //LoadHistory();
        });


        $(document).on('click', '.chk-select', function (e) { //left side visibility          
            var tr = $(this).closest('tr'); 

            if($(this).prop('checked')){
                tr.addClass('__keep__');
            }
            else{
                tr.removeClass('__keep__');
            }
            //tr.fadeOut(400, function () {
            //tr.remove();
            //alert($(this).attr('data-rowid'));
            //});
        });        

        $(document).on('click', '#ctrl_CDF_showAll', function (e) {        
            e.preventDefault();
            //LoadHistory();
            $('.tr-item').not('.__keep__').filter(function () {
                return $(this).show();
            })
        });

        $(document).on('click', '#ctrl_CDF_showSelected', function (e) {        
            e.preventDefault();            
            $('.tr-item').not('.__keep__').filter(function () {
                    return $(this).hide();
            })            
        });

        $(document).on('click', '#ctrl_CDF_showSelected1', function (e) {        
            if($(this).prop('checked')){            
                $('.tr-item').not('.__keep__').filter(function () {
                    return $(this).hide();
                })   
            }
            else{
                $('.tr-item').not('.__keep__').filter(function () {
                    return $(this).show();
                })
            }            
        });
                                


        


        $(document).on('click', '#ctrl_CDF_hideHdrDet', function (e) {        
            e.preventDefault();   
            $('.tbl-i').hide();
                      
        });

        $(document).on('click', '#ctrl_CDF_hideHdrDet1', function (e) {  
            if($(this).prop('checked')){
                $('.tbl-i').hide();
            }
            else{
                $('.tbl-i').show();
            }                      
        });

        $(document).on('click', '#ctrl_CDF_showHdrDet', function (e) {        
            e.preventDefault(); 
            $('.tbl-i').show();
                      
        });





        $(document).on('click', '#btn-show-all', function (e) {
            e.preventDefault();
            LoadHistory();
        });


        var disableShowHideHeadersCheckboxesAndReset = function(){
            $('#btn-select-cdf-all,.chk-cdf-header').prop('checked',true).prop('disabled', true);
        }
        var enableShowHideHeadersCheckboxes = function(){
            $('#btn-select-cdf-all,.chk-cdf-header').prop('disabled', false);
        }
           

        $(document).on('click', '#btn-select-cdf-all', function (e) {//checlboxes in show/hide panel
            var status = false;
            
            if($(this).prop('checked')){
                status = true;   
                $.when($('.chk-cdf-header').prop('checked', false) ).then(function( data, textStatus, jqXHR) {
                    $('.chk-cdf-header').trigger('click');
                });               
                //$('#hlrefresh').trigger('click'); //refresh - will loose selection criteria
            }
            else{
                $('.chk-cdf-header').trigger('click');
            }   
            
            //$('.chk-cdf-header').prop('checked', status);  
        });

        $(document).on('click', '.chk-cdf-header', function (e) {//checlboxes in show/hide panel
            //e.preventDefault();
            var tid = $(this).data('rowid');
            var checked = $(this).prop('checked');

            // legend ///////////////////
            // .td-cdf-33          column header format
            // .td-cdf-item-33     column col/item       

            var tdTargetHeaderID = '.td-cdf-'+tid;
            var tdTargetrecordColID = '.td-cdf-item-'+tid;

            if(!checked){
                $(tdTargetHeaderID).hide();
                $(tdTargetrecordColID).hide(); 
                $('#btn-select-cdf-all').prop('checked',false);
            }
            else{  
                $(tdTargetHeaderID).show();
                $(tdTargetrecordColID).show();   
                
                //check if all checked                
                if(parseInt($('#CDF_headersItemCount').val()) == $('.chk-cdf-header').filter(':checked').length ){                
                    $('#btn-select-cdf-all').prop('checked',true);
                }                
            }
        });

        $(document).on('click', '#btn-print', function (e) {
            e.preventDefault();
            $('#ajax-loader').show();
           
           
            var box       = $(this);
            var title       = "";
            url = 'VP/VP/Print_CDF';

            var data  =  { idLst:GetCDF_ID_List(), colLst : GetCDF_Column_List(), patientID:@Model.PatientID};
            $("#vp-modal-content").load(url, data, function () {
                $("#print-modal-container").modal({
                    keyboard: false,
                    backdrop: 'static'
                }, 'show');

                $('#ajax-loader').hide();
            });

            @*$.ajax({
                    type: "POST",
                    data: { idLst:GetCDF_ID_List(), patientID:@Model.PatientID  },
                    url: url ,
                    success: function (data) {
                        box.attr('data-content',data).popover('show');
                    },

                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/VP/Print_CDF'  " + xhr.status + " " + thrownError);
                    },

                    complete : function () {
                        $('#ajax-loader').hide();
                    }
                });*@
        });

        $(document).on('click', '.chk-col-vis', function (e) { //not in use
            //e.preventDefault();
            var tid = $(this).data("rowid");
            var tdTarget = '#td-cdf-' + tid;
            var checked = $(this).prop('checked');
            if(!checked)
            {
                //$(tdTarget).fadeOut(400, function () {
                $(tdTarget).hide();
                //});

                //var colnum = $(this).closest("td").prevAll("td").length;
                //var td = $(this).closest("table").find("tr").find("td:eq(" + colnum + ")")
                //td.css("background-color", "#FF3700");
                //td.fadeOut(400, function () {
                //    td.hide();
                //});
            }
            else{
                // $(tdTarget).fadeOut(400, function () {
                $(tdTarget).show();
                //  });

                //var colnum = $(this).closest("td").prevAll("td").length;
                //var td = $(this).closest("table").find("tr").find("td:eq(" + colnum + ")")
                //td.css("background-color", "#FF3700");
                //td.fadeOut(400, function () {
                //    td.show();
                //});
            }
        });


    });
</script>