@model Cerebrum.ViewModels.VP.VMFamilyHistoryControl
<script>
    $(document).ready(function () {
        $(document).on("ICD10CodeSelected", function (event, ICD10Selected) { ICD10CodeSelectedFamilyHistoryEdit(ICD10Selected); });
    });

    $('#FamilyHistory_Edit_Unit').on('change', function () {
    $.ajax({
            type: "POST",
            url: 'VP/VP/GetLifeStage',
            data: { days: $('#FamilyHistory_Edit_AgeOnset').val(), option: $('#FamilyHistory_Edit_Unit').val() },
            success: function (data) {

                if (data.Result == "0") {
                    $('#FamilyHistory_Edit_LifeStage').val(data.Message);
                }
            },
            error: function (xhr, thrownError) {

                alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
            }
        });
    });

    $('#FamilyHistory_Edit_AgeOnset').on('blur', function () {


        $.ajax({
            type: "POST",
            url: 'VP/VP/GetLifeStage',
            data: { days: $('#FamilyHistory_Edit_AgeOnset').val(), option: $('#FamilyHistory_Edit_Unit').val() },
            success: function (data) {

                if (data.Result == "0") {

                    $('#FamilyHistory_Edit_LifeStage').val(data.Message);
                }
            },
            error: function (xhr, thrownError) {

                alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
            }
        });

    });

    function saveFamilyHistoryData() {
        url = 'VP/VP/SaveFamilyHistory';
        ajaxCall(url, $('#frm-familyhistory-edt').serialize(), false, function (data) {
            if (data.Errored == "1") {
                $("#span-result-add").html('');
                $("#span-result-add").html(data.Message);
                //showNotificationMessage('error', 'Changes not saved');
            }
            else {
                $("#span-result-add").html('');
                //showNotificationMessage('success', 'Changes saved');
                LoadFamilyHistory();
                //LoadCPP();
                $("#button-family-history-close").click();
            }
        });

        return false;
    }

    function ICD10CodeSelectedFamilyHistoryEdit(ICD10Selected) {
        $("#CPPFamilyHistoryCodingSystem").val("ICD-10");
        $("#CPPFamilyHistoryDiagnosticCode").val(ICD10Selected.code);
        $("#CPPFamilyHistoryDiagnosticDescription").val(ICD10Selected.name);
    }
</script>

@using (Html.BeginForm("SaveFamilyHistory", "VP", FormMethod.Post, new { @id = "frm-familyhistory-edt", model = @Model }))
{
    string ageOnSet = Model.FamilyHistory_Edit.AgeOnset == 0 ? string.Empty : Model.FamilyHistory_Edit.AgeOnset.ToString();
    @Html.HiddenFor(x => x.FamilyHistory_Edit.Id)
    @Html.HiddenFor(x => x.FamilyHistory_Edit.PatientID)
    @Html.HiddenFor(x => x.FamilyHistory_Edit.Status)


    <div class="row">
        <div class="col-md-12 text-center">
            <table class="table">
                <tr>
                    <td>
                        <label>
                            Show   :
                        </label>
                    </td>
                    <td>
                        <label>
                            Diagnostic Code:
                        </label>
                    </td>
                    <td>
                        <label>
                            Diagnosis:
                        </label>
                    </td>
                    <td>
                        <label>
                            Problem Description:
                        </label>
                    </td>
                    <td>
                        <label>
                            Age of Onset :
                        </label>
                    </td>
                    <td>
                        <label>
                            Relationship :
                        </label>
                    </td>
                    <td>
                        <label>
                            Treatment :
                        </label>
                    </td>
                    <td>
                        <label>
                            Position :
                        </label>
                    </td>
                    @*<td>
                        <label>
                            Status :
                        </label>
                    </td>*@
                    <td>
                        <label>
                            Notes :
                        </label>
                    </td>
                    <td>
                        <label>
                            Start Date :
                        </label>
                    </td>
                </tr>
                <tr>
                    <td>@Html.CheckBoxFor(m => m.FamilyHistory_Edit.Visible)</td>
                    <td align="right">
                        <div>Code System: @Html.TextBoxFor(m => m.FamilyHistory_Edit.CodingSystem, new {@class = "txtBox", @id = "CPPFamilyHistoryCodingSystem" })</div>
                        <br />
                        <div style="margin-top: -12px;">Code: @Html.TextBoxFor(m => m.FamilyHistory_Edit.DiagnosticCode, new {@class = "txtBox", @id = "CPPFamilyHistoryDiagnosticCode" })</div>
                        <br />
                        <div>
                            <a id="hl-ic"
                               data-url="VP/VP/Search_ICD10?CntrlCode=&CntrlName=FamilyHistory_Add_ProblemDescription"
                               href="#" onclick="return ICD10Clicked(this)" class="btn btn-default btn-xs">ICD10</a>
                        </div>
                    </td>
                    <td>
                        @Html.TextAreaFor(m => m.FamilyHistory_Edit.DiagnosticDescription, new { @class = "txtArea form-control", @id = "CPPFamilyHistoryDiagnosticDescription", @cols = 20, @rows = 4 })
                    </td>
                    <td>
                        @Html.TextAreaFor(m => m.FamilyHistory_Edit.ProblemDescription, new { @class = "txtArea form-control", @cols = 20, @rows = 4 })
                    </td>
                    <td>
                        @Html.TextBoxFor(x => x.FamilyHistory_Edit.AgeOnset, new { @Value = ageOnSet, @class = "txtBox" })
                        @Html.DropDownListFor(x => x.FamilyHistory_Edit.Unit, Model.Units, "--Select--", new {})
                        @Html.DropDownListFor(x => x.FamilyHistory_Edit.LifeStage, Model.LifeStages, "--Select--", new {})
                        @*@Html.TextBoxFor(x => x.FamilyHistory_Edit.LifeStage, new { @class = "txtBox" })*@
                    </td>
                    <td>
                        @Html.DropDownListFor(x => x.FamilyHistory_Edit.RelationShip, Model.Relations, "--Select--", new {})
                    </td>
                    <td>
                        @Html.TextAreaFor(m => m.FamilyHistory_Edit.Treatment, new { @class = "txtArea form-control", @cols = 20, @rows = 4 })
                    </td>
                    <td>
                        @Html.TextBoxFor(x => x.FamilyHistory_Edit.Position, new { @class = "txtBox" })
                    </td>
                    @*<td>
                        @Html.DropDownListFor(x => x.FamilyHistory_Add.Status, Model.StatusList, "--Select--", new {})
                    </td>*@
                    <td>
                        @Html.TextAreaFor(m => m.FamilyHistory_Edit.Notes, new { @class = "txtArea form-control", @cols = 20, @rows = 4 })
                    </td>
                   
                    <td>
                        Day:@Html.DropDownListFor(x => x.FamilyHistory_Edit.StartDateDay, Model.Days, "--Select--", new {})
                        Month:@Html.DropDownListFor(x => x.FamilyHistory_Edit.StartDateMonth, Model.Months, "--Select--", new {})
                        Year:@Html.DropDownListFor(x => x.FamilyHistory_Edit.StartDateYear, Model.Years, "--Select--", new {})
                    </td>

                </tr>

            </table>
        </div>
    </div>


    <div class="row">&nbsp;</div>
    <div class="row">
        <div class="col-sm-12 ">
            <span class="red-error" id="span-result-add">
            </span>
        </div>
    </div>
    <div class="row">&nbsp;</div>
    <div class="row text-center">
        @*<div class="form-inline">
            <div class="form-group">
                <a id="btnedit-fh-save" href="#" class="btn btn-default btn-primary ">Save</a>
            </div>
            <div class="form-group">
                <button class="btn btn-default btn-sm btn-popover-close">Close</button>

            </div>
        </div>*@

        <div class="row">
            <div class="col-sm-6">
                <button class="btn btn-default" onclick="return saveFamilyHistoryData();">Save</button>
            </div>
            <div class="col-sm-6">
                <button class="btn btn-default" data-dismiss="modal" id="button-family-history-close">Close</button>
            </div>
        </div>
    </div>

}