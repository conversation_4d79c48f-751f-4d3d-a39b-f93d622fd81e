@model Cerebrum.ViewModels.VP.VMFamilyHistoryControl
@{
    ViewBag.ModuleName = "CPP Family History";
    //Layout = "~/Views/Shared/_LayoutFluidPopup.cshtml";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
@section scripts {
    <script>
        var LoadFamilyHistory = function () {
            $("#div-familyhistory").html('<img src="../../Content/fancybox_loading.gif" />');
            var url = $("#div-familyhistory").data("url");
            ajaxCall(url, { patientID:@Model.PatientID, practiceDoctorId: @Model.PracticeDoctorID }, false, function (data) {
                //console.log(data);
                $("#div-familyhistory").html('');
                $("#div-familyhistory").html(data);
            });
        };

        var LoadPatientName = function () {
            $("#div-pat-name").html('<img src="../../Content/fancybox_loading.gif" />');
            ajaxCall("Patients/GetPatientInfoVPPages", { patientId:@Model.PatientID }, false, function (data) {
                $("#div-pat-name").html(data);
            });
        };

        function clear_form_elements(class_name) {
            jQuery(class_name).find(':input').each(function() {
                switch(this.type) {
                    case 'password':
                    case 'text':
                    case 'textarea':
                    case 'file':
                    case 'select-one':
                    case 'select-multiple':
                    case 'date':
                    case 'number':
                    case 'tel':
                    case 'email':
                        jQuery(this).val('');
                        break;
                    //case 'checkbox':
                    case 'radio':
                        this.checked = false;
                        break;
                }
            });
        }

        $(document).ready(function () {
            $(document).on("ICD10CodeSelected", function (event, ICD10Selected) { ICD10CodeSelectedFamilyHistoryAdd(ICD10Selected); });

            LoadPatientName();
            LoadFamilyHistory();

            $(document).on('click', '.btn-popover-close', function (e) {
                e.preventDefault();
                $(".popover-btn").popover('hide');
                $('[data-original-title]').popover('hide');
            });

            $(document).on('click','.btn-save', function (e) {
                $('#ajax-loader').show();
                e.preventDefault();

                $("#CPPVisibleField.Col5Visible").prop("checked", $("#CPPVisibleField.Col4Visible").is(":checked"));
                $("#CPPVisibleField.Col7Visible").prop("checked", true);
                url = 'VP/VP/AddFamilyHistory';
                ajaxCall(url, $('#frm-familyhistory').serialize(), false, function (data) {
                    if(data.Errored == "1"){
                        $("#span-result-add").html('');
                        $("#span-result-add").html(data.Message);
                    }
                    else{
                        LoadCPP();
                        $("#span-result-add").html('');
                        $("#span-result-add").html('Changes Saved');
                        clear_form_elements('#divAdd');
                    }
                });
            });

            $(document).on('click','.btn-edit-save', function (e) {
                e.preventDefault();
                url = 'VP/VP/AddFamilyHistory';
                ajaxCall(url, $('#frm-familyhistory').serialize(), false, function (data) {
                    if(data.Errored == "1"){
                        $("#span-result-edit").html('');
                        $("#span-result-edit").html(data.Message);
                    }
                    else{
                        LoadCPP();
                        $("#span-result-edit").html('');
                        $("#span-result-edit").html('Changes Saved');
                        //clear_form_elements('#divEdit');
                    }
                });
            });

            $('#btnAdd').click(function () {
                $('#divGrd').hide();
                $('#divAdd').show();
                return false;
            });

            $('#FamilyHistory_Add_Unit').on('change', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#FamilyHistory_Add_AgeOnset').val() , option: $('#FamilyHistory_Add_Unit').val()},
                    success: function (data) {
                        if(data.Result == "0"){
                            $('#FamilyHistory_Add_LifeStage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $('#FamilyHistory_Add_AgeOnset').on('blur', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#FamilyHistory_Add_AgeOnset').val() , option: $('#FamilyHistory_Add_Unit').val()},
                    success: function (data) {
                        if(data.Result == "0" ){
                            $('#FamilyHistory_Add_LifeStage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            var show = @Model.Add_ErrorMessage.Length > 0 ? 1 : 0;
            //console.log(noError);
            if(show==1){
                $('#divGrd').hide();
                $('#divAdd').show();
                $('#divEdit').hide();
            }
            var show = @Model.Edit_ErrorMessage.Length > 0 ? 1 : 0;
            //console.log(noError);
            if(show==1){
                $('#divGrd').hide();
                $('#divAdd').hide();
                $('#divEdit').show();
            }
            var inEditMode = @Model.FamilyHistory_Edit.Id != 0 ? 1 : 0;
            if(inEditMode==1){
                $('#divGrd').hide();
                $('#divAdd').hide();
                $('#divEdit').show();
            }
        });

        function ICD10CodeSelectedFamilyHistoryAdd(ICD10Selected) {
            if (!CPPEditModalMode) {
                $("#FamilyHistory_Add_CodingSystem").val("ICD-10");
                $("#FamilyHistory_Add_DiagnosticCode").val(ICD10Selected.code);
                $("#FamilyHistory_Add_DiagnosticDescription").val(ICD10Selected.name);
            }
        }
    </script>
}
<style>
    .table-striped > tbody > tr:nth-of-type(odd) {
        background-color: #e8f2f8;
    }
    .popover {
        max-width: 100%; /* Max Width of the popover (depending on the container!) */
    }
</style>

<div class="row">&nbsp;</div>
<div id="div-pat-name"></div>
<div class="row">&nbsp;</div>
<div class="row FamilyHistory">
    <div class="col-sm-12">
        <h4 class="modal-sub-title" style="font-size:18px;">Family History </h4>
        <div class=""><span style="background-color: #f2f2f2; color: #f28383; font-size: 18px;">Data will be visible in CPP summary page if checkbox is checked preset for all your patients&nbsp;&nbsp;</span></div>
    </div>
</div>

<div class="text-center">
    @using (Html.BeginForm("FamilyHistory", "VP", FormMethod.Post, new { @id = "frm-familyhistory", model = @Model }))
    {
        @Html.HiddenFor(x => x.PatientID)
        @Html.HiddenFor(x => x.CPPVisibleField.PracticeDoctorId)
        @Html.HiddenFor(x => x.CPPVisibleField.VP_CPP_Category_Id)
        @Html.HiddenFor(x => x.CPPVisibleField.Col5Visible)
        @Html.HiddenFor(x => x.CPPVisibleField.Col7Visible)
        @Html.HiddenFor(x => x.CPPVisibleField.Col8Visible)
        @Html.HiddenFor(x => x.CPPVisibleField.Col10Visible)
        @Html.HiddenFor(x => x.CPPVisibleField.Col11Visible)
        @Html.HiddenFor(x => x.CPPVisibleField.Col12Visible)

        <div class="row">&nbsp;</div>

        <div class="row container-familyhistory">
            @*<div class="col-md-12 text-left"><span style="background-color: #f2f2f2; color: #f28383; font-size: 18px;">&nbsp;&nbsp;Data will be visible in CPP summary page if checkbox is checked preset for all your patients&nbsp;&nbsp;</span></div>*@
            <div class="col-md-12 text-center">
                <table class="table">
                    <tr>
                        <td>Show</td>
                        <td>
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col10Visible)
                            <label>
                                Diagnostic Code:
                            </label>                       
                        </td>
                        <td>
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col11Visible)
                            <label>
                                Diagnosis:
                            </label>
                        </td>
                        <td>
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col1Visible)
                            <label>
                                Problem Description:
                            </label>
                        </td>
                        <td>
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col4Visible)
                            <label>
                                Age of Onset :
                            </label>
                        </td>
                        <td>
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col3Visible)
                            <label>
                                Relationship :
                            </label>
                        </td>
                        <td>
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col2Visible)
                            <label>
                                Treatment :
                            </label>
                        </td>
                        <td>
                            <label>
                                Position :
                            </label>
                        </td>
                        @*<td>
                                <label>
                                    Status :
                                </label>
                            </td>*@
                        <td>
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col9Visible)
                            <label>
                                Notes :
                            </label>
                        </td>
                        <td>
                            @Html.CheckBoxFor(x => x.CPPVisibleField.Col6Visible)
                            <label>
                                Start Date :
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <td>@Html.CheckBoxFor(m => m.FamilyHistory_Add.Visible)</td>
                        <td align="right">
                            <div>Code System: @Html.TextBoxFor(x => x.FamilyHistory_Add.CodingSystem, new { @Value = "", @class = "txtBox" })</div>
                            <br />
                            <div style="margin-top: -12px;">Code: @Html.TextBoxFor(x => x.FamilyHistory_Add.DiagnosticCode, new { @Value = "", @class = "txtBox" })</div>
                            <br />
                            <div>
                                <a id="hl-ic"
                                   data-url="VP/VP/Search_ICD10?CntrlCode=&CntrlName=FamilyHistory_Add_ProblemDescription"
                                   href="#" onclick="return ICD10Clicked(this)" class="btn btn-default btn-xs">ICD10</a>
                            </div>
                        </td>
                        <td>
                            @Html.TextAreaFor(m => m.FamilyHistory_Add.DiagnosticDescription, new { @class = "txtArea form-control", @cols = 20, @rows = 4 })
                        </td>
                        <td>
                            @Html.TextAreaFor(m => m.FamilyHistory_Add.ProblemDescription, new { @class = "txtArea form-control", @cols = 20, @rows = 4 })
                        </td>
                        <td>
                            @Html.TextBoxFor(x => x.FamilyHistory_Add.AgeOnset, new { @Value = "", @class = "txtBox" })
                            @Html.DropDownListFor(x => x.FamilyHistory_Add.Unit, Model.Units, "--Select--", new {})
                            @Html.DropDownListFor(x => x.FamilyHistory_Add.LifeStage, Model.LifeStages, "--Select--", new {})
                            @*@Html.TextBoxFor(x => x.FamilyHistory_Add.LifeStage, new { @class = "txtBox" })*@
                        </td>
                        <td>
                            @Html.DropDownListFor(x => x.FamilyHistory_Add.RelationShip, Model.Relations, "--Select--", new {})
                        </td>
                        <td>
                            @Html.TextAreaFor(m => m.FamilyHistory_Add.Treatment, new { @class = "txtArea form-control", @cols = 20, @rows = 4 })
                        </td>
                        <td>
                            @Html.TextBoxFor(x => x.FamilyHistory_Add.Position, new { @class = "txtBox" })
                        </td>
                        @*<td>
                                @Html.DropDownListFor(x => x.FamilyHistory_Add.Status, Model.StatusList, "--Select--", new {})
                            </td>*@
                        <td>
                            @Html.TextAreaFor(m => m.FamilyHistory_Add.Notes, new { @class = "txtArea form-control", @cols = 20, @rows = 4 })
                        </td>
                        <td>
                            Day:@Html.DropDownListFor(x => x.FamilyHistory_Add.StartDateDay, Model.Days, "--Select--", new {})
                            Month:@Html.DropDownListFor(x => x.FamilyHistory_Add.StartDateMonth, Model.Months, "--Select--", new {})
                            Year:@Html.DropDownListFor(x => x.FamilyHistory_Add.StartDateYear, Model.Years, "--Select--", new {})
                        </td>

                    </tr>

                </table>
                <div class="row">&nbsp;</div>
                <div class="row text-center">
                    <b> <span style="color:red;" id="span-result-add"></span></b>
                </div>

                <div class="row text-center">
                    <a id="btn-add-new-fh" href="#" class="btn btn-default btn-primary ">Save</a>
                </div>
            </div>
        </div>

        <div class="row">&nbsp;</div>

        <div id="div-familyhistory" data-url="@Url.Action("FamilyHistoryData", "VP", new { Area = "VP" })"></div>

        <div class="row">&nbsp;</div>

        <button class="btn btn-default btn-sm btn-cancel-model">
            <i style="color:red" class="glyphicon glyphicon-remove"></i>Close
        </button>

        <br />
        <br />
    }
</div>

