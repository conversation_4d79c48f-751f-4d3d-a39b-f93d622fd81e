@model  Cerebrum.ViewModels.VP.VMCPPAlertList
@{
    ViewBag.ModuleName = "CPP Alerts";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<link href="~/Content/CPP_Styles.css" rel="stylesheet" />

@section scripts {

    <script>

        var LoadPatientName = function () {

            $("#div-pat-name").html('<img src="../../Content/fancybox_loading.gif" />');
            ajaxCall("Patients/GetPatientInfoVPPages", { patientId:@Model.PatientID }, false, function (data) {
                $("#div-pat-name").html(data);
            });
        }

        $(document).ready(function () {

            LoadPatientName();

            $(document).on('click', '.btn-popover-close', function (e) {

                e.preventDefault();
                $(".popover-btn").popover('hide');
                $('[data-original-title]').popover('hide');
            });

            $('.hl-delete')
                .popover({
                    trigger: 'manual',
                    html: true,
                    placement: 'auto right'
                })

            .click(function(e){

                $('#ajax-loader').show();

                e.preventDefault();

                var box       = $(this);
                var title       = "";

                var _patientid = $(this).data("patientid");
                var _cppType   = $(this).data("cpptype");
                var _rowid     = $(this).data("rowid");
                var _cntrlName = $(this).data("name");

                var data = {  patientID:_patientid,cppType:_cppType,rowid:_rowid, cntrlName:_cntrlName  };
                url = 'VP/VP/ReasonForDeletion';

                $.ajax({

                    type:'POST',
                    url: url ,
                    data : data,
                    success: function (data) {

                        box.attr('data-content',data).popover('show');
                    },

                    error: function (xhr, thrownError) {

                        alert("Error while tryng to call  'VP/VP/ReasonForDeletion'  " + xhr.status + " " + thrownError);
                    },


                    complete : function () {

                        $('#ajax-loader').hide();
                    }
                });

            });

            $('#btnAdd').click(function () {

                $('#divGrd').hide();
                $('#divAdd').show();
                $('#divEdit').hide();
                return false;
            });


            var show = @Model.Add_ErrorMessage.Length > 0 ? 1 : 0   ;
            //console.log(noError);
            if(show==1)
            {
                $('#divGrd').hide();
                $('#divAdd').show();
                $('#divEdit').hide();
            }


            var show = @Model.Edit_ErrorMessage.Length > 0 ? 1 : 0   ;
            //console.log(noError);
            if(show==1)
            {
                $('#divGrd').hide();
                $('#divAdd').hide();
                $('#divEdit').show();
            }

            var inEditMode = @Model.VP_CPP_Alert_Edit.Id != 0 ? 1 : 0   ;
            if(inEditMode==1)
            {
                $('#divGrd').hide();
                $('#divAdd').hide();
                $('#divEdit').show();
            }


            $("#DateActive").datepicker();
            $("#VP_CPP_Alert_Edit_EndDate").datepicker();

        });


        @Model.ScriptToExecute

    </script>

}

<div id="div-pat-name"></div>

<div class="row AddAlertCPP">
    @*<div class="col-sm-6">*@
        @*<div id="div-pat-name"></div>*@
    @*</div>*@
    <div class="col-sm-12">
        <h4 class="label label-primary pull-right" style="font-size:18px;">Alerts and Special Needs</h4>
    </div>
</div>

@using (Html.BeginForm("AddAlert", "VP", FormMethod.Post, new { model = @Model }))
{

        @Html.HiddenFor(x => x.PatientID)
        @Html.HiddenFor(x => x.UserID)

        <div class="container topPadding" id="divGrd">
            <div class="row">
                <div class="col-sm-12 text-center">
                    <button id="btnAdd" class="btn btn-primary ">Add New Alert</button>
                </div>
            </div>
            <br />
            <div class="row">
                <div class="col-sm-12">
                    <table class="spanCell table table table-striped table-bordered table-condensed">
                        <tr>
                            <th>
                                Description
                            </th>
                            <th>
                                Notes
                            </th>
                            <th>
                                DateActive
                            </th>
                            <th>
                                EndDate
                            </th>
                            <th>
                                Status
                            </th>
                            <th>
                                SubmitDate
                            </th>
                            <th>
                                Edit
                            </th>
                            <th>
                                Delete
                            </th>
                        </tr>
                        @for (int i = 0; i < @Model.Alerts.Count; i++)
                        {
                        <tr>
                            <th>
                                @Html.TextAreaFor(model => model.Alerts[i].Description, new { @cols=50,@rows=8})
                            </th>
                            <th>
                                @Html.TextAreaFor(model => model.Alerts[i].Notes , new { @cols = 50, @rows = 8 })
                            </th>
                            <th>
                                @(Model.Alerts[i].DateActive_Year == 0 ? "" : @Model.Alerts[i].DateActive_Year.ToString() + "/")@(Model.Alerts[i].DateActive_Month == 0 ? "" : @Model.Alerts[i].DateActive_Month.ToString() + "/")@(Model.Alerts[i].DateActive_Day == 0 ? "" : @Model.Alerts[i].DateActive_Day.ToString() )
                            </th>
                            <th>

                                @if (Model.Alerts[i].EndDate.HasValue)
                                    {
                                    <span>@Model.Alerts[i].EndDate</span>
                                    }
                                    else
                                    {
                                    <span>&nbsp;</span>
                                    }
                            </th>
                            <th>
                                @Html.CheckBoxFor(model => model.Alerts[i].Status)
                            </th>
                            <th>
                                @Html.DisplayFor(model => model.Alerts[i].eSubmitDate)
                            </th>
                            <th>
                                @Html.ActionLink("Edit", "Edit_Alert_CPP", new { entryID = Model.Alerts[i].Id, patientID = Model.PatientID })
                            </th>

                            <th class="spanCell">
                                <a href="#" id="<EMAIL>[i].Id" data-name="<EMAIL>[i].Id" class="hl-delete" data-patientid="@Model.PatientID" data-cpptype="6" data-rowid="@Model.Alerts[i].Id">Delete</a>
                            </th>

                        </tr>
                        }

                    </table>
                </div>
            </div>
        </div>

        <div class="container topPadding" id="divAdd" style="display:none;">
            <div class="row">
                <div class="col-sm-12 text-center">

                    <div class="row">
                        <div class="col-sm-6 text-right">
                            <span class="spanCell">   Description</span>
                        </div>
                        <div class="col-sm-6">

                        </div>
                        <div class="col-sm-6 text-left">
                            @Html.TextAreaFor(x => x.Description, new { @class = "txtArea" })
                        </div>
                    </div>

                    <div class="row">&nbsp;</div>
                    <div class="row">
                        <div class="col-sm-6 text-right">
                            <span class="spanCell">  Notes</span>
                        </div>
                        <div class="col-sm-6">

                        </div>
                        <div class="col-sm-6 text-left">
                            @Html.TextAreaFor(x => x.Note, new { @class = "txtArea" })
                        </div>
                    </div>

                    <div class="row">&nbsp;</div>

                    <div class="row">
                        <div class="col-sm-6 text-right">
                            <span class="spanCell">  Date Active</span>
                        </div>
                        <div class="col-sm-6">

                        </div>
                        <div class="col-sm-6 text-left">
                            @*@Html.TextBoxFor(x => x.DateActive, new { @class = "txtBox" })*@

                            <div class="col-sm-1 spanCell">Day</div>
                            <div class="col-sm-2">
                                @Html.DropDownListFor(x => x.DateActiveDay, Model.Days, "--Select--", new {})
                            </div>
                            <div class="col-sm-1 spanCell">Month</div>
                            <div class="col-sm-2">
                                @Html.DropDownListFor(x => x.DateActiveMonth, Model.Months, "--Select--", new {})
                            </div>
                            <div class="col-sm-1 spanCell">Year</div>
                            <div class="col-sm-2">
                                @Html.DropDownListFor(x => x.DateActiveYear, Model.Years, "--Select--", new {})
                            </div>


                        </div>
                    </div>

                    <div class="row">&nbsp;</div>

                    <div class="row">
                        <span class="redError">
                            @Html.Raw(@Model.Add_ErrorMessage)
                        </span>
                    </div>

                    <div class="row">&nbsp;</div>

                    <button id="btnSave" class="btn btn-primary">Save</button>
                    @Html.ActionLink("Cancel", "CancelAddAlertCPP", "VP", new { area = "VP", patientID = Model.PatientID }, new { @class = "btn btn-primary" })

                </div>

            </div>
        </div>

        <div class="container topPadding" id="divEdit" style="display:none;">
            <div class="row">
                <div class="col-sm-12 text-center">

                    @Html.HiddenFor(x => x.VP_CPP_Alert_Edit.Id)

                    <div class="row">&nbsp;</div>
                    <div class="row">
                        <div class="col-sm-6 text-right">
                            <span class="spanCell">Notes</span>
                        </div>
                        <div class="col-sm-6">

                        </div>
                        <div class="col-sm-6 text-left">
                            @Html.TextAreaFor(x => x.VP_CPP_Alert_Edit.Notes, new { @class = "txtArea" })
                        </div>
                    </div>

                    <div class="row">&nbsp;</div>

                    <div class="row">
                        <div class="col-sm-6 text-right">
                            <span class="spanCell">End Date</span>
                        </div>
                        <div class="col-sm-6">

                        </div>
                        <div class="col-sm-6 text-left">
                            @Html.TextBoxFor(x => x.VP_CPP_Alert_Edit.EndDate, new { @class = "txtBox" })

                        </div>
                    </div>

                    <div class="row">&nbsp;</div>

                    <div class="row">
                        <span class="redError">
                            @Html.Raw(@Model.Edit_ErrorMessage)
                        </span>
                    </div>

                    <div class="row">&nbsp;</div>

                    <button id="btnSave" class="btn btn-default">Save</button>
                    @Html.ActionLink("Cancel", "CancelAddAlertCPP", "VP", new { area = "VP", patientID = Model.PatientID }, new { @class = "btn btn-default" })

                </div>

            </div>
        </div>

        <div>&nbsp;</div>

}


