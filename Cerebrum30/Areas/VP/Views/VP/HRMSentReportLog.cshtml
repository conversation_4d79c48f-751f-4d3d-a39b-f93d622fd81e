@model IEnumerable<Cerebrum.ViewModels.VP.SendReport_VM>

@{
    ViewBag.Title = "HRMSentReportLog";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<script>
    $(document).ready(function () {
        $(".resendHRM").click(function (e) {
            e.preventDefault();
            var id = $(this).attr("data-id");
            var URL = $(this).attr('href');
            ResendHRM(URL, id);
        });
    });
    function ResendHRM(URL,id)
    {
        $.ajax({ url: URL }).done(function (up) {
            if (up.Errored == '0') {
                $("#" + id).remove();
                var rowcount = $("#row-count").html();
                rowcount = rowcount - 1;
                $("#row-count").html(rowcount);
            }
        });
    }
</script>
<h2>HRM Sent Report Log (<span id="row-count"> @(Model!=null? Model.Count():0) )</span></h2>


<table class="table">
    <tr>
        <th></th>
        <th>
            @Html.DisplayNameFor(model => model.DateEntered)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.DocName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.AppointmentId)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.TestId)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.PatientId)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Sent)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.EmailTo)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.FaxTo)
        </th>
       
       
        <th>
            @Html.DisplayNameFor(model => model.Amended)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.IsVP)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Location)
        </th>
      
        <th>
            @Html.DisplayNameFor(model => model.PhysicalPath)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.URL)
        </th>
       
        <th>
            @Html.DisplayNameFor(model => model.ErrorMessage)
        </th>
       
    </tr>
@if (Model != null && Model.Count()>0)
{
    foreach (var item in Model)
    {
    <tr id="@item.Id">
        <td>
            
            @{
                @Html.ActionLink("Send HRM", "ReSendHRM", new { id = item.Id, appointmentId = item.AppointmentId, patientId = item.PatientId, fileUrl = item.Location }, new { @class = "resendHRM", data_id = @item.Id })
            }
        </td>
        <td>
            @item.DateEntered
        </td>
        <td>
            @item.DocName
        </td>
        <td>
            @item.AppointmentId
        </td>
        <td>
            @item.TestId
        </td>
        <td>
            @item.PatientId
        </td>
        <td>
            @item.Sent
        </td>
        <td>
            @item.EmailTo
        </td>
        <td>
            @item.FaxTo
        </td>
      
        <td>
            @item.Amended
        </td>
        <td>
            @item.IsVP
        </td>
        <td>
            @item.Location
        </td>
       
        <td>
            @item.PhysicalPath
        </td>
        <td>
            @item.URL
        </td>
        
        <td>
            @item.ErrorMessage
        </td>
       
    </tr>
    }
}

</table>
