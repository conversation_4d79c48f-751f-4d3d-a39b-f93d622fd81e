@model  Cerebrum.ViewModels.VP.VMRiskFactor

@{
    ViewBag.ModuleName = "CPP Risk Factors";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<link href="~/Content/CPP_Styles.css" rel="stylesheet" />
@section scripts {

    <script>
        var LoadPatientName = function () {

            $("#div-pat-name").html('<img src="../../Content/fancybox_loading.gif" />');
            ajaxCall("Patients/GetPatientInfoVPPages", { patientId:@Model.PatientId }, false, function (data) {
                $("#div-pat-name").html(data);
            });
        };
        function clear_form_elements(class_name) {
            jQuery(class_name).find(':input').each(function() {
                switch(this.type) {
                    case 'password':
                    case 'text':
                    case 'textarea':
                    case 'file':
                    case 'select-one':
                    case 'select-multiple':
                    case 'date':
                    case 'number':
                    case 'tel':
                    case 'email':
                        jQuery(this).val('');
                        break;
                    case 'checkbox':
                    case 'radio':
                        this.checked = false;
                        break;
                }
            });
        }

        $(document).ready(function () {

            LoadPatientName();

            $(document).on('click', '.btn-popover-close', function (e) {

                e.preventDefault();
                $(".popover-btn").popover('hide');
                $('[data-original-title]').popover('hide');
            });


            // $('.hl-delete')
            //    .popover({
            //        trigger: 'manual',
            //        html: true,
            //        placement: 'auto right'
            //    })

            //.click(function(e){

            //    $('#ajax-loader').show();

            //    e.preventDefault();

            //    var box       = $(this);
            //    var title       = "";

            //    var _patientid = $(this).data("patientid");
            //    var _cppType   = $(this).data("cpptype");
            //    var _rowid     = $(this).data("rowid");
            //    var _cntrlName = $(this).data("name");

            //    var data = {  patientID:_patientid,cppType:_cppType,rowid:_rowid, cntrlName:_cntrlName  };
            //    url = 'VP/VP/ReasonForDeletion';

            //    $.ajax({

            //        type:'POST',
            //        url: url ,
            //        data : data,
            //        success: function (data) {

            //            box.attr('data-content',data).popover('show');
            //        },

            //        error: function (xhr, thrownError) {

            //            alert("Error while tryng to call  'VP/VP/ReasonForDeletion'  " + xhr.status + " " + thrownError);
            //        },


            //        complete : function () {

            //            $('#ajax-loader').hide();
            //        }
            //    });

            //});

            $('#btnAdd').click(function () {

                $('#divGrd').hide();
                $('#divAdd').show();
                return false;
            });

            $(document).on('click','#btnSave-rf-edit', function (e) {
                e.preventDefault();

                url = 'VP/VP/Save_RiskFactor';
                ajaxCall(url, $('#frm-rf').serialize(), false, function (data) {
                    if(data.Errored == "1"){

                        $("#span-result-edit").html('');
                        $("#span-result-edit").html(data.Message);
                    }
                    else{

                        $("#span-result-edit").html('');
                        $("#span-result-edit").html('Changes Saved');
                        //clear_form_elements('#divAdd');
                        opener.LoadCPP();
                    }
                });
            });

            $(document).on('click','#btnSave-rf', function (e) {
                e.preventDefault();
                url = 'VP/VP/Save_RiskFactor';
                ajaxCall(url, $('#frm-rf').serialize(), false, function (data) {
                    if(data.Errored == "1"){

                        $("#span-result-add").html('');
                        $("#span-result-add").html(data.Message);
                    }
                    else{

                        $("#span-result-add").html('');
                        $("#span-result-add").html('Changes Saved');
                        clear_form_elements('#divAdd');
                        opener.LoadCPP();
                    }
                });
            });

            $('#Unit').on('change', function () {


                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#OnsetAge').val() , option: $('#Unit').val()},
                    success: function (data) {

                        if(data.Result == "0" )
                        {
                            $('#LifeStage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {

                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $('#OnsetAge').on('blur', function () {


                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#OnsetAge').val() , option: $('#Unit').val()},
                    success: function (data) {

                        if(data.Result == "0" )
                        {

                            $('#LifeStage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {

                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });

            });

            $('#VP_RiskFactor_Edit_Unit').on('change', function () {


                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_RiskFactor_Edit_OnsetAge').val() , option: $('#VP_RiskFactor_Edit_Unit').val()},
                    success: function (data) {

                        if(data.Result == "0" )
                        {
                            $('#VP_RiskFactor_Edit_LifeStage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {

                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $('#VP_RiskFactor_Edit_OnsetAge').on('blur', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_RiskFactor_Edit_OnsetAge').val() , option: $('#VP_RiskFactor_Edit_Unit').val()},
                    success: function (data) {

                        if(data.Result == "0" )
                        {

                            $('#VP_RiskFactor_Edit_LifeStage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {

                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });

            });

            //$('#btnCancel').click(function () {

            //    $('#divGrd').show();
            //    $('#divAdd').hide();
            //    //window.opener.location.reload();
            //    return false;
            //});


            $("#StartDate").datepicker();
            $("#EndDate").datepicker();
            $("#VP_RiskFactor_Edit_EndDate").datepicker();

            var show = @Model.Add_ErrorMessage.Length > 0 ? 1 : 0   ;
            //console.log(noError);
            if(show==1)
            {
                $('#divGrd').hide();
                $('#divAdd').show();
                $('#divEdit').hide();
            }


            var show = @Model.Edit_ErrorMessage.Length > 0 ? 1 : 0   ;
            //console.log(noError);
            if(show==1)
            {
                $('#divGrd').hide();
                $('#divAdd').hide();
                $('#divEdit').show();
            }

            var inEditMode = @Model.VP_RiskFactor_Edit.Id != 0 ? 1 : 0   ;
            if(inEditMode==1)
            {
                $('#divGrd').hide();
                $('#divAdd').hide();
                $('#divEdit').show();
            }

        });

        @Model.ScriptToExecute


    </script>

}
<div id="div-pat-name"></div>

<div class="row">
    @*<div class="col-sm-6">
        <div id="div-pat-name"></div>
    </div>*@
    <div class="col-sm-12">
        <h4 class="label label-primary pull-right" style="font-size:18px;">Risk Factors</h4>
    </div>
</div>

@using (Html.BeginForm("RiskFactor", "VP", FormMethod.Post, new { @id = "frm-rf", model = @Model }))
{

    @Html.HiddenFor(x => x.PatientId)

    <div class="text-center">

        <div class="topPadding" id="divGrd">
            <div class="row">
                <div class="col-md-12">
                    <button id="btnAdd" class="btn btn-default btn-primary">Add New</button>
                </div>
            </div>
            <br />

            <div class="row">
                <div class="col-md-12">
                    <div class="pre-scrollable">
                        <table class="table table table-striped table-bordered table-condensed ">

                            @for (int i = 0; i < @Model.VP_RiskFactors.Count; i++)
                            {

                                if (i == 0)
                                {
                                    <tr>
                                        <td><span class="span"><b>ExposureDetails</b></span></td>
                                        <td><span class="span"><b>RiskFactor</b></span></td>
                                        <td><span class="span"><b>Notes</b></span></td>
                                        <td class="spanCell"><span class="span"><b>On Set Age</b></span></td>
                                        <td class="spanCell"><span class="span"><b>Start Date</b></span></td>
                                        <td class="spanCell"><span class="span"><b>End Date</b></span></td>
                                        <td class="spanCell"><span class="span"><b>Life Stage</b></span></td>

                                        <td class="spanCell"><span class="span"><b>Position</b></span></td>
                                        <td class="spanCell"><span class="span"><b>Status</b></span></td>
                                        <td class="spanCell"><span class="span"><b>SubmitDate</b></span></td>

                                        <td class="spanCell"> </td>
                                        <td class="spanCell"> </td>
                                    </tr>
                                }

                                <tr>

                                    <td class="spanCell">
                                        @Html.DisplayFor(model => model.VP_RiskFactors[i].ExposureDetails, new { @class = "txtBox" })
                                    </td>
                                    <td class="spanCell">
                                        @Html.DisplayFor(model => model.VP_RiskFactors[i].RiskFactor, new { @class = "txtBox" })
                                    </td>
                                    <td class="spanCell">
                                        @Html.DisplayFor(model => model.VP_RiskFactors[i].Notes, new { @class = "txtBox" })
                                    </td>
                                    <td class="spanCell">
                                        @Html.DisplayFor(model => model.VP_RiskFactors[i].OnsetAge, new { @class = "txtBox" })
                                    </td>

                                    <td class="spanCell">
                                        @Html.DisplayFor(model => model.VP_RiskFactors[i].StartDate, new { @class = "txtBox" })
                                    </td>
                                    <td>
                                        @Html.DisplayFor(model => model.VP_RiskFactors[i].EndDate, new { @class = "txtBox" })
                                    </td>
                                    <td class="spanCell">
                                        @Html.DisplayFor(model => model.VP_RiskFactors[i].LifeStage, new { @class = "txtBox" })
                                    </td>

                                    <td class="spanCell">
                                        @Html.DisplayFor(model => model.VP_RiskFactors[i].Position, new { @class = "txtBox" })
                                    </td>
                                    <td class="spanCell">
                                        @*@Html.TextBoxFor(model => model.VP_RiskFactors[i].Status, new { @class = "txtBox" })*@
                                        @Html.CheckBox("chkStatus", Model.VP_RiskFactors[i].Status == 1)
                                    </td>
                                    <td class="spanCell">
                                        @Html.DisplayFor(model => model.VP_RiskFactors[i].SubmitDate, new { @class = "txtBox" })
                                    </td>
                                    <td class="spanCell">
                                        @Html.ActionLink("Edit", "Edit_RiskFactor_CPP", new { entryID = Model.VP_RiskFactors[i].Id, patientID = Model.PatientId })
                                    </td>
                                    <td class="spanCell">
                                        <a href="#" id="hl-del-@Model.VP_RiskFactors[i].Id" data-name="hl-del-@Model.VP_RiskFactors[i].Id" class="hl-delete" data-patientid="@Model.PatientId" data-cpptype="4" data-rowid="@Model.VP_RiskFactors[i].Id">Delete</a>
                                    </td>
                                </tr>

                            }

                        </table>
                    </div>
                </div>
            </div>
        </div>


        <div class="container topPadding" id="divAdd" style='display:none'>
            <div class="row">
                <div class="col-md-12 text-center">
                    <div class="rowContainer">

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Risk Factors :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextAreaFor(m => m.RiskFactor, new { @class = "txtArea" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Exposure Details :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextAreaFor(m => m.ExposureDetails, new { @class = "txtArea" })
                            </div>
                        </div>


                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Notes :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextAreaFor(m => m.Notes, new { @class = "txtArea" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Start Date :
                            </div>
                            <div class="col-sm-8 text-left">
                                @*@Html.TextBoxFor(m => m.StartDate, new { @class = "largetxtBox", @readonly = "readonly" })*@

                                <div class="col-sm-1 spanCell">Day</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.StartDateDay, Model.Days, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell">Month</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.StartDateMonth, Model.Months, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell">Year</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.StartDateYear, Model.Years, "--Select--", new {})
                                </div>

                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                End Date :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextBoxFor(m => m.EndDate, new { @class = "txtBox", @readonly = "readonly" })
                                @*<div class="col-sm-1 spanCell">Day</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.EndDate , Model.Days, "--Select--", new {})
                                    </div>
                                    <div class="col-sm-1 spanCell">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.StartDateMonth, Model.Months, "--Select--", new {})
                                    </div>
                                    <div class="col-sm-1 spanCell">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.StartDateYear, Model.Years, "--Select--", new {})
                                    </div>*@

                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Age of Onset :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextBoxFor(m => m.OnsetAge, new { @class = "txtBox" })
                                @Html.DropDownListFor(x => x.Unit, Model.Units, "--Select--", new {})
                                <span class="spanCell">Life Stage </span>
                                @Html.TextBoxFor(m => m.LifeStage, new { @class = "txtBox", @readonly = "readonly" })
                            </div>
                        </div>


                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Position :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextBoxFor(m => m.Position, new { @class = "largetxtBox" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Status :
                            </div>
                            <div class="col-sm-8 text-left">
                                @*@Html.TextBoxFor(m => m.VP_RiskFactor_Edit.Status, new { @class = "largetxtBox"})*@
                                @Html.DropDownListFor(m => m.Status, Model.ActiveList, "--Select--", new {})
                            </div>
                        </div>


                        <div class="row">
                            &nbsp;
                        </div>

                        <div class="row">
                            <div class="col-sm-6 text-right">
                                <button id="btnSave-rf" class="btn btn-default">Save</button>
                            </div>
                            <div class="col-sm-6 text-left">
                                @Html.ActionLink("Cancel", "RiskFactor", "VP", new { area = "VP", patientID = Model.PatientId }, new { @class = "btn btn-default" })
                            </div>
                        </div>

                        <div class="row">
                            &nbsp;
                        </div>

                        <div class="row">
                            @Html.ActionLink("Done", "RiskFactor", "VP", new { area = "VP", patientID = Model.PatientId }, new { @class = "btn btn-primary" })
                        </div>

                        <div class="row">
                            <div class="col-sm-12 ">
                                <span class="redError" id="span-result-add">
                                    @Html.Raw(@Model.Add_ErrorMessage)
                                </span>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>

        <div class="container topPadding" id="divEdit" style='display:none'>
            <div class="row">
                <div class="col-md-12 text-center">

                    @Html.HiddenFor(x => x.VP_RiskFactor_Edit.Id)
                    @Html.HiddenFor(x => x.VP_RiskFactor_Edit.PatientId)

                    <div class="rowContainer">

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Risk Factors :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextAreaFor(m => m.VP_RiskFactor_Edit.RiskFactor, new { @class = "txtArea" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Exposure Details :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextAreaFor(m => m.VP_RiskFactor_Edit.ExposureDetails, new { @class = "txtArea" })
                            </div>
                        </div>


                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Notes :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextAreaFor(m => m.VP_RiskFactor_Edit.Notes, new { @class = "txtArea" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Start Date :
                            </div>
                            <div class="col-sm-8 text-left">
                                @*@Html.TextBoxFor(m => m.VP_RiskFactor_Edit.StartDate, new { @class = "largetxtBox", @readonly = "readonly" })*@

                                <div class="col-sm-1 spanCell">Day</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.StartDateDay, Model.Days, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell">Month</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.StartDateMonth, Model.Months, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell">Year</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.StartDateYear, Model.Years, "--Select--", new {})
                                </div>

                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                End Date :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextBoxFor(m => m.VP_RiskFactor_Edit.EndDate, new { @class = "txtBox", @readonly = "readonly" })
                                @*<div class="col-sm-1 spanCell">Day</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.EndDate , Model.Days, "--Select--", new {})
                                    </div>
                                    <div class="col-sm-1 spanCell">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.StartDateMonth, Model.Months, "--Select--", new {})
                                    </div>
                                    <div class="col-sm-1 spanCell">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.StartDateYear, Model.Years, "--Select--", new {})
                                    </div>*@

                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Age of Onset :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextBoxFor(m => m.VP_RiskFactor_Edit.OnsetAge, new { @class = "txtBox" })
                                @Html.DropDownListFor(x => x.VP_RiskFactor_Edit.Unit, Model.Units, "--Select--", new {})
                                <span class="spanCell">Life Stage </span>
                                @Html.TextBoxFor(m => m.VP_RiskFactor_Edit.LifeStage, new { @class = "txtBox", @readonly = "readonly" })
                            </div>
                        </div>


                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Position :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextBoxFor(m => m.VP_RiskFactor_Edit.Position, new { @class = "largetxtBox" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Status :
                            </div>
                            <div class="col-sm-8 text-left">
                                @*@Html.TextBoxFor(m => m.VP_RiskFactor_Edit.Status, new { @class = "largetxtBox"})*@
                                @Html.DropDownListFor(m => m.VP_RiskFactor_Edit.Status, Model.ActiveList, "--Select--", new {})
                            </div>
                        </div>


                        <div class="row">
                            &nbsp;
                        </div>

                        <div class="row">
                            <div class="col-sm-6 text-right">
                                <button id="btnSave-rf-edit" class="btn btn-default">Save</button>
                            </div>
                            <div class="col-sm-6 text-left">
                                @Html.ActionLink("Cancel", "RiskFactor", "VP", new { area = "VP", patientID = Model.PatientId }, new { @class = "btn btn-default" })
                            </div>
                        </div>

                        <div class="row">
                            &nbsp;
                        </div>

                        <div class="row">
                            @Html.ActionLink("Done", "RiskFactor", "VP", new { area = "VP", patientID = Model.PatientId }, new { @class = "btn btn-primary" })
                        </div>

                        <div class="row">
                            <div class="col-sm-12 ">
                                <span class="redError" id="span-result-edit">
                                    @Html.Raw(@Model.Edit_ErrorMessage)
                                </span>
                            </div>
                        </div>


                    </div>

                </div>
            </div>
        </div>
    </div>
}

