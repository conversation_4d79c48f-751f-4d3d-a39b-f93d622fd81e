@model Cerebrum.ViewModels.VP.VMCPPProblem
 
<script>
    $(document).ready(function () {
        $(document).on("ICD10CodeSelected", function (event, ICD10Selected) { ICD10CodeSelectedProblemListEdit(ICD10Selected); });
    });

    //$(document).on('click', '#btnSave-rf-edit', function (e) {
    function saveProblemListData() {
        url = 'VP/VP/Save_ProblemList';
        ajaxCall(url, $('#frm-problemlist-edit').serialize(), false, function (data) {
            if (data.Errored == "1") {
                //alert(data.Message);
                //showMessageModal("error", data.Message, false);
                $("#span-result-edit").html('');
                $("#span-result-edit").html(data.Message);
            }
            else {
                //LoadCPP();
                LoadHistory();
                $("#span-result-edit").html('');
                $("#span-result-edit").html('Changes Saved');
                $("#button-problem-list-close").click();
            }
        });

        return false;
    };

    function OnsetAgeChangedProblemList() {
        $.ajax({
            type: "POST",
            url: 'VP/VP/GetLifeStage',
            data: { days: $("#LifeStageYear").val(), option: $("#LifeStageUnit").val() },
            success: function (data) {
                if (data.Result == "0") {
                    $("#LifeStageText").val(data.Message);
                }
            },
            error: function (xhr, thrownError) {
                alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
            }
        });
    }

    function ICD10CodeSelectedProblemListEdit(ICD10Selected) {
        $("#CPPProblemDiagnosis").val(ICD10Selected.code);
        $("#CPPProblemDescription").val(ICD10Selected.name);
    }
</script>

@using (Html.BeginForm("ShowCPP_ProblemList", "VP", FormMethod.Post, new { @id = "frm-problemlist-edit", model = @Model }))
{
    <div class="topPadding ">
        <div class="">
            <div class="text-center">

                @Html.HiddenFor(x => x.VP_CPP_Problem_List_VM_Edit.PatientRecordId)
                @Html.HiddenFor(x => x.VP_CPP_Problem_List_VM_Edit.Id)

                <div>
                    <table width="95%">
                        <tr>
                            <td>
                                Diagnosis @*<a id="hl-ic"
                                             data-url="VP/VP/Search_ICD10?CntrlCode=VP_CPP_Problem_List_VM_Add_Diagnosis&CntrlName="
                                             href="VP/VP/Search_ICD10?CntrlCode=VP_CPP_Problem_List_VM_Add_Diagnosis&CntrlName=" class="btn btn-default btn-xs hl-ic">ICD10</a>*@

                                <a id="hl-ic"
                                   data-url="VP/VP/Search_ICD10?CntrlCode=VP_CPP_Problem_List_VM_Edit_Diagnosis&CntrlName=VP_CPP_Problem_List_VM_Edit_Problem_Description"
                                   href="#" onclick="return ICD10Clicked(this)" class="btn btn-default btn-xs">ICD10</a>
                            </td>
                            <td>@*@Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.Diagnosis, new { @class = "txtArea", @id = "CPPProblemDiagnosis", @readonly = true })*@</td>
                        </tr>
                        <tr>
                            <td>Problem Description</td>
                            <td>@Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.Problem_Description, new { @class = "txtArea", @id = "CPPProblemDescription" })</td>
                        </tr>
                        <tr>
                            <td>Life Stage</td>
                            <td align="left">
                                    @Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.Years, new { @class = "txtBox", @id= "LifeStageYear", onblur = "OnsetAgeChangedProblemList()" })
                                    @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.Units, Model.Units, "--Select--", new {@id = "LifeStageUnit", onchange = "OnsetAgeChangedProblemList()" })
                                    @Html.TextBoxFor(x => x.VP_CPP_Problem_List_VM_Edit.Life_Stage, new { @id = "LifeStageText" })
                            </td>
                        </tr>
                        <tr>
                            <td>Problem Status</td>
                            <td align="left">@Html.DropDownListFor(m => m.VP_CPP_Problem_List_VM_Edit.Problem_Status, Model.ActiveList, "--Select--", new {})</td>
                        </tr>
                        <tr>
                            <td>Position in Summary</td>
                            <td align="left">
                                @Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.Position, new { @class = "largetxtBox" })
                            </td>
                        </tr>
                        <tr>
                            <td>Procedure/Intervention</td>
                            <td>@Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.Proc_Interv, new { @class = "txtArea" })</td>
                        </tr>
                        <tr>
                            <td>Notes</td>
                            <td>@Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.Notes, new { @class = "txtArea" })</td>
                        </tr>
                        <tr>
                            <td>Date of Onset</td>
                            <td align="left">
                                Day @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Day, Model.Days, "--Select--", new {})
                                Month @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Month, Model.Months, "--Select--", new {})
                                Year @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Year, Model.Years, "--Select--", new {})
                            </td>
                        </tr>
                        <tr>
                            <td>Resolution Date</td>
                            <td align="left">
                                Day @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Day, Model.Days, "--Select--", new {})
                                Month @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Month, Model.Months, "--Select--", new {})
                                Year @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Year, Model.Years, "--Select--", new {})
                            </td>
                        </tr>
                        <tr>
                            <td>Procedure Date</td>
                            <td align="left">
                                Day @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ProcDate_Day, Model.Days, "--Select--", new {})
                                Month @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ProcDate_Month, Model.Months, "--Select--", new {})
                                Year @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ProcDate_Year, Model.Years, "--Select--", new {})
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12 ">
            <span class="redError" id="span-result-edit">
            </span>
        </div>
    </div>
    <br /><br />
    <div class="row  text-center">
        <div class="col-sm-6">
            <button class="btn btn-default" onclick="return saveProblemListData();">Save</button>
        </div>
        <div class="col-sm-6">
            <button class="btn btn-default" data-dismiss="modal" id="button-problem-list-close">Close</button>
        </div>
    </div>
    <br />
    <br />

}