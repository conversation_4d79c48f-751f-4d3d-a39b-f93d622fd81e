@model Cerebrum.ViewModels.VP.VMCPPProblem
@{
    ViewBag.ModuleName = "CPP Problem List/Past Health";
    //Layout = "~/Views/Shared/_LayoutFluidPopup.cshtml";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section scripts {
    <script>
        var LoadPatientName = function () {
            // $("#div-pat-name").html('<img src="../../Content/fancybox_loading.gif" />');
            ajaxCall("Patients/GetPatientInfoVPPages", { patientId:@Model.PatientID }, false, function (data) {
                $("#div-pat-name").html(data);
            });
        };

        var LoadHistory = function () {
            //$("#div-history").html('<img src="../../Content/fancybox_loading.gif" />');
            var url = $("#div-history").data("url");
            ajaxCall(url, "", false, function (data) {
                //console.log(data);
                $("#div-history").html('');
                $("#div-history").html(data);
            });
        };

        function clear_form_elements(class_name) {
            jQuery(class_name).find(':input').each(function () {
                switch (this.type) {
                    case 'password':
                    case 'text':
                    case 'textarea':
                    case 'file':
                    case 'select-one':
                    case 'select-multiple':
                    case 'date':
                    case 'number':
                    case 'tel':
                    case 'email':
                        jQuery(this).val('');
                        break;
                    case 'checkbox':
                    case 'radio':
                        this.checked = false;
                        break;
                }
            });
        }

        $(document).ready(function () {
            $(document).on("ICD10CodeSelected", function (event, ICD10Selected) { ICD10CodeSelectedProblemListAdd(ICD10Selected); });

            LoadHistory();

            LoadPatientName();
            $('#btnAdd').click(function () {
                $('#divGrd').hide();
                $('#divAdd').show();
                return false;
            });

            $(document).on('click', '.btn-popover-close', function (e) {
                e.preventDefault();
                $(".popover-btn").popover('hide');
                $('[data-original-title]').popover('hide');
            });

            $('.btn-save-add').click(function (e) {
            //$(document).on('click','.btn-save-add', function (e) {
                e.preventDefault();
                url = 'VP/VP/Save_ProblemList';
                ajaxCall(url, $('#frm-problemlist').serialize(), false, function (data) {
                    if(data.Errored == "1"){
                        $("#span-result-add").html('');
                        $("#span-result-add").html(data.Message);
                    }
                    else{
                        LoadCPP();
                        LoadHistory();
                        $("#span-result-add").html('');
                        $("#span-result-add").html('Changes Saved');
                        clear_form_elements('.divAdd');

                    }
                });
            });

            $('#VP_CPP_Problem_List_VM_Add_Units').on('change', function () {
                //console.log($('#FamilyHistory_Add_AgeOnset').val());
                //console.log('---------------------------');
                //console.log($('#FamilyHistory_Add_Unit').val());

                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_CPP_Problem_List_VM_Add_Years').val() , option: $('#VP_CPP_Problem_List_VM_Add_Units').val()},
                    success: function (data) {

                        if(data.Result == "0" )
                        {
                            $('#VP_CPP_Problem_List_VM_Add_Life_Stage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {

                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $('#VP_CPP_Problem_List_VM_Add_Years').on('blur', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_CPP_Problem_List_VM_Add_Years').val() , option: $('#VP_CPP_Problem_List_VM_Add_Units').val()},
                    success: function (data) {

                        if(data.Result == "0" )
                        {

                            $('#VP_CPP_Problem_List_VM_Add_Life_Stage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });

            });

            $('#VP_CPP_Problem_List_VM_Edit_Units').on('change', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_CPP_Problem_List_VM_Edit_Years').val() , option: $('#VP_CPP_Problem_List_VM_Edit_Units').val()},
                    success: function (data) {

                        if(data.Result == "0" )
                        {
                            $('#VP_CPP_Problem_List_VM_Edit_Life_Stage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {

                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $('#VP_CPP_Problem_List_VM_Edit_Years').on('blur', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_CPP_Problem_List_VM_Edit_Years').val() , option: $('#VP_CPP_Problem_List_VM_Edit_Units').val()},
                    success: function (data) {

                        if(data.Result == "0" )
                        {

                            $('#VP_CPP_Problem_List_VM_Edit_Life_Stage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {

                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });

            });

            var show = @Model.Add_ErrorMessage.Length > 0 ? 1 : 0   ;
            //console.log(noError);
            if(show==1)
            {
                $('#divGrd').hide();
                $('#divAdd').show();
                $('#divEdit').hide();
            }


            var show = @Model.Edit_ErrorMessage.Length > 0 ? 1 : 0   ;
            //console.log(noError);
            if(show==1)
            {
                $('#divGrd').hide();
                $('#divAdd').hide();
                $('#divEdit').show();
            }

            var inEditMode = @Model.VP_CPP_Problem_List_VM_Edit.Id != 0 ? 1 : 0   ;
            if(inEditMode==1)
            {
                $('#divGrd').hide();
                $('#divAdd').hide();
                $('#divEdit').show();
            }

            $("#VP_CPP_Problem_List_VM_Add_DateOfOnset").datepicker();
            $("#VP_CPP_Problem_List_VM_Add_ResolutionDate").datepicker();
            $("#VP_CPP_Problem_List_VM_Add_ProcDate").datepicker();

            $("#VP_CPP_Problem_List_VM_Edit_DateOfOnset").datepicker();
            $("#VP_CPP_Problem_List_VM_Edit_ResolutionDate").datepicker();
            $("#VP_CPP_Problem_List_VM_Edit_ProcDate").datepicker();



            $(document).on('click', '.btn-popover-close', function (e) {

                e.preventDefault();
                $(".popover-btn").popover('hide');
                $('[data-original-title]').popover('hide');
            });

        });

        function ICD10CodeSelectedProblemListAdd(ICD10Selected) {
            if (!CPPEditModalMode) {
                $("#VP_CPP_Problem_List_VM_Add_Diagnosis").val(ICD10Selected.code);
                $("#VP_CPP_Problem_List_VM_Add_Problem_Description").val(ICD10Selected.name);
            }
        }

        @Model.ScriptToExecute


    </script>

}


<div class="row">&nbsp;</div>
<div id="div-pat-name"></div>
<div class="row">&nbsp;</div>
<div class="row ">
    <div class="col-sm-12">
        @{ 
            var Title = Model.IsProblemList ? "PROBLEM LIST" : "PAST HEALTH";
        }
        <h4 class="modal-sub-title" style="font-size:18px;">@Title </h4> <!-- label label-primary pull-left-->
        <div class=""><span style="background-color: #f2f2f2; color: #f28383; font-size: 18px;">Data will be visible in CPP summary page if checkbox is checked preset for all your patients&nbsp;&nbsp;</span></div>
    </div>
</div>
<br />
<div class="text-center">
    @using (Html.BeginForm("ShowCPP_ProblemList", "VP", FormMethod.Post, new { @id = "frm-problemlist", model = @Model }))
    {
        @Html.HiddenFor(x => x.PatientID)
        @Html.HiddenFor(x => x.IsProblemList)
        @Html.HiddenFor(x => x.CPPVisibleField.PracticeDoctorId)
        @Html.HiddenFor(x => x.CPPVisibleField.VP_CPP_Category_Id)
        @Html.HiddenFor(x => x.CPPVisibleField.Col8Visible)
        @Html.HiddenFor(x => x.CPPVisibleField.Col9Visible)
        @Html.HiddenFor(x => x.CPPVisibleField.Col10Visible)
        @Html.HiddenFor(x => x.CPPVisibleField.Col11Visible)
        @Html.HiddenFor(x => x.CPPVisibleField.Col12Visible)

        <div id="div-history" data-url="@Url.Action("ProblemList_CPP_Data", "VP", new { Area = "VP", patientID=Model.PatientID,isProblemList=Model.IsProblemList,appointmentID=Model.AppointmentID,practiceDoctorId=Model.PracticeDoctorID})"><img src="../../Content/fancybox_loading.gif" /></div>
        <br />
        <div class="row text-center">
            <button class="btn btn-default btn-sm btn-cancel-model ">
                <i style="color:red" class="glyphicon glyphicon-remove"></i>Close
            </button>
        </div>
        <br />


        <div class="container topPadding" id="divEdit" style='display:none'>
                <div class="row">
                    <div class="col-md-12 text-center">
                        @Html.HiddenFor(x => x.VP_CPP_Problem_List_VM_Edit.PatientRecordId)
                        @Html.HiddenFor(x => x.VP_CPP_Problem_List_VM_Edit.Id)
                        <div class="rowContainer">
                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Diagnosis
                                    <a id="hl-ic"
                                       data-url="VP/VP/Search_ICD10?CntrlCode=VP_CPP_Problem_List_VM_Add_Diagnosis&CntrlName="
                                       href="VP/VP/Search_ICD10?CntrlCode=VP_CPP_Problem_List_VM_Add_Diagnosis&CntrlName=" class="btn btn-default btn-xs hl-ic">ICD10</a>
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.DiagnosticDescription, new { @class = "txtArea" })
                                </div>
                            </div>
                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Problem Description :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.Problem_Description, new { @class = "txtArea" })
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Life Stage :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.Years, new { @class = "largetxtBox" })
                                    @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.Units, Model.Units, "--Select--", new {})
                                    @Html.TextBoxFor(x => x.VP_CPP_Problem_List_VM_Edit.Life_Stage)
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Problem Status :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.DropDownListFor(m => m.VP_CPP_Problem_List_VM_Edit.Problem_Status, Model.ActiveList, "--Select--", new {})
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Position in Summary :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.Position, new { @class = "largetxtBox" })
                                </div>
                            </div>


                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Procedure /Intervention :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.Proc_Interv, new { @class = "txtArea" })
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Notes :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @Html.TextAreaFor(m => m.VP_CPP_Problem_List_VM_Edit.Notes, new { @class = "txtArea" })
                                </div>
                            </div>

                            <div class="row">&nbsp;</div>

                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Date of Onset :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @*@Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.DateOfOnset, new { @class = "largetxtBox", @readonly = "readonly" })*@

                                    <div class="col-sm-1 spanCell ">Day</div>
                                    <div class="col-sm-2 ">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Day, Model.Days, "--Select--", new {})
                                    </div>
                                    <div class="col-sm-1 spanCell">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Month, Model.Months, "--Select--", new {})
                                    </div>
                                    <div class="col-sm-1 spanCell">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.DateOfOnset_Year, Model.Years, "--Select--", new {})
                                    </div>

                                </div>
                            </div>



                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Resolution Date :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @*@Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.ResolutionDate, new { @class = "largetxtBox", @readonly = "readonly" })*@
                                    <div class="col-sm-1 spanCell ">Day</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Day, Model.Days, "--Select--", new {})
                                    </div>
                                    <div class="col-sm-1 spanCell ">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Month, Model.Months, "--Select--", new {})
                                    </div>
                                    <div class="col-sm-1 spanCell ">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ResolutionDate_Year, Model.Years, "--Select--", new {})
                                    </div>

                                </div>
                            </div>


                            <div class="row">
                                <div class="spanCell col-sm-4 text-right">
                                    Procedure Date :
                                </div>
                                <div class="col-sm-8 text-left">
                                    @*@Html.TextBoxFor(m => m.VP_CPP_Problem_List_VM_Edit.ProcDate, new { @class = "largetxtBox", @readonly = "readonly" })*@

                                    <div class="col-sm-1 spanCell ">Day</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ProcDate_Day, Model.Days, "--Select--", new {})
                                    </div>
                                    <div class="col-sm-1 spanCell ">Month</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ProcDate_Month, Model.Months, "--Select--", new {})
                                    </div>
                                    <div class="col-sm-1 spanCell ">Year</div>
                                    <div class="col-sm-2">
                                        @Html.DropDownListFor(x => x.VP_CPP_Problem_List_VM_Edit.ProcDate_Year, Model.Years, "--Select--", new {})
                                    </div>

                                </div>
                            </div>

                            <div class="row">&nbsp;</div>

                            <div class="row">
                                <div class="col-sm-12 ">
                                    <span class="redError" id="span-result-edit">
                                        @Html.Raw(@Model.Add_ErrorMessage.Replace("\n", "<br>"))
                                    </span>
                                </div>
                            </div>

                            <div class="row">
                                <div class="spanCell col-sm-6 text-right">
                                    <input type="submit" value="Save" class="btn btn-default btn-save-edit" />

                                </div>
                                <div class="col-sm-6 text-left">
                                    @Html.ActionLink("Cancel", "Cancel_CPP_ProblemList", "VP", new { area = "VP", patientID = Model.PatientID, IsProblemList = Model.IsProblemList }, new { @class = "btn btn-default" })
                                </div>
                            </div>

                            <div class="row">&nbsp;</div>

                            <div class="row">
                                <div class="col-sm-12 ">
                                    @Html.ActionLink("Done", "Cancel_CPP_ProblemList", "VP", new { area = "VP", patientID = Model.PatientID, IsProblemList = Model.IsProblemList }, new { @class = "btn btn-primary" })
                                </div>
                            </div>

                        </div>

                        <br />
                    </div>
                </div>
            </div>

           

    }
</div>

