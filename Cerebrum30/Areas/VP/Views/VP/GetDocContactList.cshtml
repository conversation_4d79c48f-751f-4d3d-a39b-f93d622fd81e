@model Cerebrum.ViewModels.VP.VMPatientAppointement
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>Doc Contact List</title>
</head>
<body>

    <script>


        $(document).ready(function () {

            LoadHistoryData();

        });

    </script>
    @using (Html.BeginForm("SendFile", "VP", FormMethod.Post, new { @id = "frm-send-rp-vp", model = @Model }))
    {


        @Html.HiddenFor(X => X.AppointmentID)
        @Html.HiddenFor(X => X.PatientID)
        @Html.HiddenFor(X => X.OfficeID)


        <div class="text-center">

            <h4>Contacts</h4>
            <br />
            <div>
                <table class="table table-bordered table-condensed">
                    <tr>
                        <td><b>Name</b></td>
                        <td><b>DocType</b></td>
                        <td><b>HRM</b></td>
                        <td><b>Fax</b></td>
                        <td><b>Mail</b></td>
                        <td><b>Email</b></td>
                        <td>&nbsp;</td>
                    </tr>

                    @for (int i = 0; i < Model.Doctors.Count; i++)
                {
                        <tr>
                            <td>@Model.Doctors[i].Name</td>
                            <td>@Model.Doctors[i].DocType</td>
                            <td>
                                @Html.CheckBox("Doctors[" + i + "].HRM", Model.Doctors[i].HRM)
                            </td>
                            <td>
                                @Html.CheckBox("Doctors[" + i + "].Fax", Model.Doctors[i].Fax)
                            </td>
                            <td>
                                @Html.CheckBox("Doctors[" + i + "].Mail", Model.Doctors[i].Mail)
                            </td>
                            <td>
                                @Html.CheckBox("Doctors[" + i + "].Email", Model.Doctors[i].Email)
                                @Html.TextBox("Doctors[" + i + "].EmailAddress", Model.Doctors[i].EmailAddress)
                            </td>

                        </tr>
                    }
                </table>

                <button data-url='@Url.Action("SendFile")'
                        id="btn-send-vp" class="btn btn-default btn-sm">
                    Send
                </button>

            </div>
            <label class="has-error">
                <h5>
                    <p style="padding:15px;" id="div-result-frm-send-rp-vp"></p>
                </h5>
            </label>
            <div id="div-data">
                <img src="~/Content/fancybox_loading.gif" />
            </div>
        </div>

    }
</body>
</html>
