@model Cerebrum.ViewModels.VP.VP_VM
<script>
    $(document).ready(function () {
        $('[data-toggle="tooltip"]').tooltip({ animation: false });

        //$(".btn-popover-container").each(function () {

        //    var btn = $(this).find(".popover-btn").first();
        //    var titleContainer = $(this).find(".btn-popover-title").first();
        //    var contentContainer = $(this).find(".btn-popover-content").first();

        //    var title = $(titleContainer).html();
        //    var content = $(contentContainer).html();

        //    $(btn).popover({
        //        html: true,
        //        title: title,
        //        content: content,
        //        placement: 'auto top',
        //        trigger: 'manual'//,
        //        //container: 'body'
        //    })
        //    .on("click", function () {

        //        var _this = this;
        //        $(this).popover("show");
        //        $(".txtDateService").datepicker();

        //    }).on("mouseleave", function () {
        //        //  $(".popover-btn").popover('hide');
        //    });
        //});

        //$(document).on('click', '.btn-close-status-change', function (e) {

        //    e.preventDefault();
        //    $(".popover-btn").popover('hide');
        //    $('[data-original-title]').popover('hide');
        //});

    });
</script>
<style>
    /*.btn-popover-container {
        display: inline-block;
    }

    .btn-popover-content {
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 0;
        margin-bottom: 0;
    }

    .btn-popover-container .btn-popover-title, .btn-popover-container .btn-popover-content {
        display: none;
    }

    .popover-pointer {
        cursor: pointer;
    }

    .popover {
        width: 500px;
    }*/
</style>
<div id="div-vp-rp" class="__00985 AA panel panel-info">
    <div class="panel-heading">VP Phrases</div>
    <div class="panel-body">
        <div>
            <!-- class="row" style="margin-top:5px; padding-left:15px;"-->
            <div class="form-group">
                <div>
                    @foreach (var phrase in Model.OpeningStatementPhrases)
                    {
                        <div class="dropdown">
                            @*style="margin-bottom:3px"*@
                            <button class="btn btn-default btn-sm dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                <span class="menuText">@phrase.Name </span>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                @foreach (var subphrase in phrase.Phrases)
                                {
                                    <li class="dropdown-submenu">
                                        <a href="#"
                                           class="opening-link dropdown-toggle @(subphrase.Phrases.Count > 0 ? "spanRed" : "spanGreen" )"
                                           data-toggle="dropdown"
                                           data-loading-text="@(string.IsNullOrEmpty(subphrase.CustomText) ?  subphrase.Value : subphrase.CustomText)"
                                           data-cntrlname="OpeningStatement">
                                            <span>@subphrase.Name </span>
                                        </a>
                                        @if (subphrase.Phrases.Count > 0)
                                        {
                                            <ul class="dropdown-menu">
                                                @foreach (var innersubphrase in subphrase.Phrases)
                                                {
                                                    <li class="dropdown-submenu">
                                                        @if (!string.IsNullOrEmpty(innersubphrase.Name))
                                                        {
                                                            <a href="#"
                                                               class="opening-link dropdown-toggle @(innersubphrase.Phrases.Count > 0 ? "spanRed" : "spanGreen" )"
                                                               data-toggle="dropdown"
                                                               data-loading-text="@(string.IsNullOrEmpty(innersubphrase.CustomText) ? innersubphrase.Value : innersubphrase.CustomText)"
                                                               data-cntrlname="OpeningStatement">
                                                                <span>@innersubphrase.Name</span>
                                                            </a>
                                                        }

                                                        @if (innersubphrase.Phrases.Count == 0)
                                                        {
                                                            <ul class="dropdown-menu">
                                                                <li>

                                                                    <a href="#" class="btn-vp-rp-customize" data-url='@Url.Action("ReportPhraseCustomize", "VP",
                                                                 new
                                                                 {
                                                                     reportPhraseID = @innersubphrase.Id,
                                                                     userID = Model.DoctorID
                                                                 })'>Customize Value</a>
                                                                <li />
                                                            </ul>
                                                        }
                                                    </li>
                                                }
                                                <li>
                                                    <a data-url='@Url.Action("AddOpeningStatementPhraseSubItem", "VP", new { RootPhraseID = @phrase.Id, ParentID = @subphrase.Id, DocID = Model.DoctorID })'
                                                       class="btn-create-subitem"
                                                       href="#">Create sub-item for '@subphrase.Name'</a>
                                                </li>
                                            </ul>
                                        }
                                        else
                                        {
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a href="#" class="btn-vp-rp-customize" data-url='@Url.Action("ReportPhraseCustomize", "VP",
                                                                                                            new
                                                                                                            {
                                                                                                                reportPhraseID =@subphrase.Id,
                                                                                                                userID = Model.DoctorID
                                                                                                            })'>Customize Value</a>
                                                <li />
                                            </ul>

                                        }
                                    </li>
                                }
                                <li>
                                    <a data-url='@Url.Action("AddOpeningStatementPhraseSubItem", "VP", new { RootPhraseID = @phrase.Id, ParentID = @phrase.Id, DocID=Model.DoctorID})'
                                       class="btn-create-subitem"
                                       href="#">Create sub-item for '@phrase.Name'</a>
                                </li>
                            </ul>
                        </div>
                    }
                </div>
            </div>

            <div class="form-group-sm nnn">
                @*style="clear:both; display:block; margin-top:5px;"*@
                @Html.TextAreaFor(x => x.OpeningStatement, new { @class = "opening-box" })
            </div>
        </div>
        <div style="margin-top:5px;" class="row">

            <ol class="vp-list">
                <!-- already used 'panel-body' || cpp-list -->
                @for (int i = 0; i < @Model.ReportPhrases.Count; i++)
                {
                    @Html.Hidden("ReportPhrases[" + i + "].Id", Model.ReportPhrases[" + i + "].Id)
                    @Html.Hidden("ReportPhrases[" + i + "].Name", Model.ReportPhrases[i].Name)
                    var attrb = Model.ReportPhrases[i].Skipped ? "checked" : "";
                    var size = 3;
                    switch (Model.ReportPhrases.Count)
                    {
                        case 1:
                            size = 12;
                            break;
                        case 2:
                            size = 6;
                            break;
                        case 3:
                            size = 4;
                            break;
                        case 4:
                            size = 3;
                            break;
                        default:
                            break;
                    }
                    <li class="col-sm-@size">
                        <div class="form-inline  ">
                            <div class="form-group form-group-sm">
                                <ul class="nav">
                                    <li class="dropdown">
                                        <div class="form-inline">
                                            <div class="form-group form-group-sm">
                                                <a class="dropdown-toggle" href="#" data-toggle="dropdown">
                                                    <span class="rep-phrase-header">@Model.ReportPhrases[i].Name</span>
                                                </a>
                                            </div>
                                        </div>
                                        <ul class="dropdown-menu ">
                                            @for (int j = 0; j < @Model.ReportPhrases[i].Phrases.Count; j++)
                                            {
                                                //var classNameExtensions = @Model.ReportPhrases[i].Phrases[j].Phrases.Count > 0 ? "dropdown-submenu" : "";
                                                var classNameExtensions = "dropdown-submenu";
                                                var functionName = @Model.ReportPhrases[i].Phrases[j].Phrases.Count > 0 ? "return false;" : "AddReportPhraseText";
                                                var hasPhrases = @Model.ReportPhrases[i].Phrases[j].Phrases.Count > 0 ? "1" : "0";
                                                var txtCntrlName = "txtReportPhrase_" + @Model.ReportPhrases[i].Id;
                                                var txtStr = string.IsNullOrEmpty(@Model.ReportPhrases[i].Phrases[j].CustomText) ?
                                                    (string.IsNullOrEmpty(@Model.ReportPhrases[i].Phrases[j].Value) ? @Model.ReportPhrases[i].Phrases[j].Name : @Model.ReportPhrases[i].Phrases[j].Value) :
                                                    @Model.ReportPhrases[i].Phrases[j].CustomText;
                                                var jsCall = functionName + "(this,'" + @txtCntrlName + "','" + txtStr + "'," + @Model.ReportPhrases[i].Phrases[j].Id + "," + @Model.ReportPhrases[i].Id + " ); return false;";
                                                var selectedClass = "spanGreen";

                                                if (Model.ReportPhrases[i].Phrases[j].Phrases.Count > 0)
                                                    selectedClass = "spanRed";


                                                if (!string.IsNullOrEmpty(Model.ReportPhrases[i].Value) &&
                                                    ((!string.IsNullOrEmpty(Model.ReportPhrases[i].Phrases[j].Name) &&
                                                    Model.ReportPhrases[i].Value.Contains(Model.ReportPhrases[i].Phrases[j].Name))

                                                    ||

                                                      (!string.IsNullOrEmpty(Model.ReportPhrases[i].Phrases[j].Value) &&
                                                      Model.ReportPhrases[i].Value.Contains(Model.ReportPhrases[i].Phrases[j].Value))

                                                      ||

                                                     (!string.IsNullOrEmpty(Model.ReportPhrases[i].Phrases[j].CustomText) &&
                                                     Model.ReportPhrases[i].Value.Contains(Model.ReportPhrases[i].Phrases[j].CustomText))

                                                    )

                                                    selectedClass = "spanBlue";

                                                <li class="@classNameExtensions ">
                                                    @if (!string.IsNullOrEmpty(@Model.ReportPhrases[i].Phrases[j].Name))
                                                    {
                                                        <a tabindex="-1" href="#"
                                                           class="vp-rp-item"
                                                           data-hasphrases="@hasPhrases"
                                                           data-cntrlname="@txtCntrlName"
                                                           data-str="@txtStr"
                                                           data-phraseid="@Model.ReportPhrases[i].Phrases[j].Id"
                                                           data-topphraseid="@Model.ReportPhrases[i].Id">
                                                            <span class="@selectedClass">
                                                                <span>@Model.ReportPhrases[i].Phrases[j].Name </span>
                                                            </span>
                                                        </a>
                                                    }
                                                    @if (Model.ReportPhrases[i].Phrases[j].Phrases.Count > 0)
                                                    {
                                                        <ul class="dropdown-menu">
                                                            @for (int z = 0; z < @Model.ReportPhrases[i].Phrases[j].Phrases.Count; z++)
                                                            {
                                                                //var className = @Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases.Count > 0 ? "dropdown-submenu" : "";
                                                                var className = "dropdown-submenu";
                                                                functionName = @Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases.Count > 0 ? "return false;" : "AddReportPhraseText";
                                                                hasPhrases = @Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases.Count > 0 ? "1" : "0";
                                                                txtCntrlName = "txtReportPhrase_" + @Model.ReportPhrases[i].Id;
                                                                txtStr = string.IsNullOrEmpty(@Model.ReportPhrases[i].Phrases[j].Phrases[z].CustomText) ?
                                                                     (string.IsNullOrEmpty(@Model.ReportPhrases[i].Phrases[j].Phrases[z].Value) ? @Model.ReportPhrases[i].Phrases[j].Phrases[z].Name : @Model.ReportPhrases[i].Phrases[j].Phrases[z].Value)
                                                                    : @Model.ReportPhrases[i].Phrases[j].Phrases[z].CustomText;
                                                                jsCall = functionName + "(this,'" + @txtCntrlName + "','" + txtStr + "'," + @Model.ReportPhrases[i].Phrases[j].Phrases[z].Id + "," + @Model.ReportPhrases[i].Id + " ); return false;";
                                                                selectedClass = "spanGreen";

                                                                if (Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases.Count > 0)
                                                                    selectedClass = "spanRed";

                                                                if (!string.IsNullOrEmpty(Model.ReportPhrases[i].Value) &&

                                                                      ((!string.IsNullOrEmpty(Model.ReportPhrases[i].Phrases[j].Phrases[z].Name) &&
                                                                    Model.ReportPhrases[i].Value.Contains(Model.ReportPhrases[i].Phrases[j].Phrases[z].Name))

                                                                    ||

                                                                      (!string.IsNullOrEmpty(Model.ReportPhrases[i].Phrases[j].Phrases[z].Value) &&
                                                                      Model.ReportPhrases[i].Value.Contains(Model.ReportPhrases[i].Phrases[j].Phrases[z].Value))

                                                                     ||

                                                                     (!string.IsNullOrEmpty(Model.ReportPhrases[i].Phrases[j].Phrases[z].CustomText) &&
                                                                     Model.ReportPhrases[i].Value.Contains(Model.ReportPhrases[i].Phrases[j].Phrases[z].CustomText)))

                                                                    selectedClass = "spanBlue";

                                                            <li class="@className">
                                                                @if (!string.IsNullOrEmpty(@Model.ReportPhrases[i].Phrases[j].Phrases[z].Name))
                                                                    {
                                                                    <a tabindex="-1" href="#"
                                                                       class="vp-rp-item"
                                                                       data-hasphrases='@hasPhrases'
                                                                       data-cntrlname="@txtCntrlName"
                                                                       data-str="@txtStr"
                                                                       data-phraseid="@Model.ReportPhrases[i].Phrases[j].Phrases[z].Id"
                                                                       data-topphraseid="@Model.ReportPhrases[i].Id">
                                                                        <span class="@selectedClass">
                                                                            <span>@Model.ReportPhrases[i].Phrases[j].Phrases[z].Name  </span>
                                                                        </span>
                                                                    </a>
                                                                    }
                                                                @if (Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases.Count > 0)
                                                                    {
                                                                    <ul class="dropdown-menu">
                                                                        @for (int a = 0; a < @Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases.Count; a++)
                                                                            {
                                                                                functionName = "AddReportPhraseText";
                                                                                txtCntrlName = "txtReportPhrase_" + @Model.ReportPhrases[i].Id;
                                                                                txtStr = string.IsNullOrEmpty(@Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].CustomText) ?
                                                                                                                                (string.IsNullOrEmpty(@Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Value) ? @Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Name
                                                                                                                                : @Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Value) :
                                                                                                                                @Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].CustomText;
                                                                                jsCall = functionName + "(this,'" + @txtCntrlName + "','" + txtStr + "'," + @Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Id + "," + @Model.ReportPhrases[i].Id + " ); return false;";
                                                                                selectedClass = "spanGreen";
                                                                                if (!string.IsNullOrEmpty(Model.ReportPhrases[i].Value) &&

                                                                                      ((!string.IsNullOrEmpty(Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Name) &&
                                                                                      Model.ReportPhrases[i].Value.Contains(Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Name))

                                                                                        ||

                                                                                      (!string.IsNullOrEmpty(Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Value) &&
                                                                                      Model.ReportPhrases[i].Value.Contains(Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Value))

                                                                                        ||

                                                                                         (!string.IsNullOrEmpty(Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].CustomText) &&
                                                                                         Model.ReportPhrases[i].Value.Contains(Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].CustomText)))

                                                                                    selectedClass = "spanBlue";
                                                                            <li class="dropdown-submenu">
                                                                                @if (!string.IsNullOrEmpty(@Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Name))
                                                                                    {
                                                                                    <a tabindex="-1" href="#"
                                                                                       class="vp-rp-item"
                                                                                       data-hasphrases="0"
                                                                                       data-cntrlname="@txtCntrlName"
                                                                                       data-str="@txtStr"
                                                                                       data-phraseid="@Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Id"
                                                                                       data-topphraseid="@Model.ReportPhrases[i].Id">
                                                                                        <span class="@selectedClass">
                                                                                            <span>@Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Name </span>
                                                                                        </span>
                                                                                    </a>
                                                                                    }
                                                                                    @*@if (a + 1 == @Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases.Count)
                                                                                        {
                                                                                        <ul class="dropdown-menu">
                                                                                            <li>

                                                                                                <a href="#" class="btn-vp-rp-customize" data-url='@Url.Action("ReportPhraseCustomize", "VP",
                                                                                                   new
                                                                                                   {
                                                                                                       reportPhraseID = Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases[a].Id,
                                                                                                       userID = Model.DoctorID
                                                                                                   })'>Customize Value</a>
                                                                                            <li />
                                                                                        </ul>
                                                                                        }*@
                                                                                </li>
                                                                                if (a + 1 == @Model.ReportPhrases[i].Phrases[j].Phrases[z].Phrases.Count)
                                                                                {
                                                                            <li>
                                                                                <a data-url='@Url.Action("AddPhraseSubItem", "VP", new { RootPhraseID = @Model.ReportPhrases[i].Id, ParentID = @Model.ReportPhrases[i].Phrases[j].Phrases[z].Id, DocID=@Model.DoctorID})'
                                                                                   class="btn-create-subitem"
                                                                                   href="#">Create sub-item for '@Model.ReportPhrases[i].Phrases[j].Phrases[z].Name'</a>
                                                                            </li>
                                                                                }
                                                                            }
                                                                    </ul>
                                                                    }
                                                                    else
                                                                    {
                                                                    <ul class="dropdown-menu">
                                                                        <li>

                                                                            <a href="#" class="btn-vp-rp-customize" data-url='@Url.Action("ReportPhraseCustomize", "VP",
                                                                                                            new
                                                                                                            {
                                                                                                                reportPhraseID = Model.ReportPhrases[i].Phrases[j].Phrases[z].Id,
                                                                                                                userID = Model.DoctorID
                                                                                                            })'>Customize Value</a>
                                                                        <li />
                                                                    </ul>
                                                                    }
                                                            </li>
                                                                if (z + 1 == @Model.ReportPhrases[i].Phrases[j].Phrases.Count)
                                                                {
                                                            <li>
                                                                <a data-url='@Url.Action("AddPhraseSubItem", "VP", new { RootPhraseID = @Model.ReportPhrases[i].Id, ParentID = @Model.ReportPhrases[i].Phrases[j].Id , DocID=@Model.DoctorID })'
                                                                   class="btn-create-subitem"
                                                                   href="#">Create sub-item for '@Model.ReportPhrases[i].Phrases[j].Name'</a>
                                                            </li>
                                                                }
                                                            }
                                                        </ul>
                                                    }
                                                    else
                                                    {
                                                        <ul class="dropdown-menu ">
                                                            <li>

                                                                <a href="#" class="btn-vp-rp-customize" data-url='@Url.Action("ReportPhraseCustomize", "VP",
                                                                                                            new
                                                                                                            {
                                                                                                                reportPhraseID = Model.ReportPhrases[i].Phrases[j].Id,
                                                                                                                userID = Model.DoctorID
                                                                                                            })'>Customize Value</a>
                                                            <li />
                                                        </ul>
                                                    }

                                                </li>

                                                if (j + 1 == @Model.ReportPhrases[i].Phrases.Count)
                                                {
                                                    <li>
                                                        <a data-url='@Url.Action("AddPhraseSubItem", "VP", new { RootPhraseID = @Model.ReportPhrases[i].Id, ParentID = @Model.ReportPhrases[i].Id, DocID=@Model.DoctorID })'
                                                           class="btn-create-subitem"
                                                           href="#">Create sub-item for '@Model.ReportPhrases[i].Name'</a>
                                                    </li>
                                                }
                                            }
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                            <div class="form-group form-group-sm "> @*pull-right*@
                                <div class="form-inline">
                                    
                                    <div class="form-group ">
                                        @*pull-right*@
                                        <a 
                                           style="padding-left:10px"
                                           data-toggle="tooltip"
                                           class="icon-color"
                                           title="History"
                                           @*data-placement="right"*@
                                           id="btn-phrase-history" target="_blank" href=# data-url='@Url.Action("ReportPhraseHistory", "VP", new { reportPhraseID = Model.ReportPhrases[i].Id, appointmentID = Model.AppointmentID,patientID=Model.PatientID , IsAccumulative = Model.ReportPhrases[i].Accumulative })'>
                                            <i style="margin-top: -7px;" class="vert-center glyphicon glyphicon-pencil btn-apptest-status c-pointer"></i>
                                        </a>
                                    </div>
                                    
                                    <div class="form-group form-group-sm ">@*pull-right*@
                                        @*<div data-toggle="tooltip"
                                                 title="Skip in Letter"
                                                 data-placement="right">
                                                &nbsp;<input id="chk-skip" data-value="@Model.ReportPhrases[i].Id" type="checkbox" class="chk-skip-phrases"
                                                             @attrb />
                                            </div>*@
                                        <i data-selected=@(Model.ReportPhrases[i].Skipped ?  "1":"0")
                                           data-toggle="tooltip"
                                           title="Show&nbsp;in&nbsp;Letter"
                                           data-placement="right"
                                           id="<EMAIL>[i].Id"
                                           data-value="@Model.ReportPhrases[i].Id"
                                           style="margin-top: -7px;" 
                                           class="vert-center glyphicon chk-skip-reportphrases
                                               @(Model.ReportPhrases[i].Skipped?"glyphicon-remove color-red":"glyphicon-ok color-green")
                                               btn-apptest-status
                                               c-pointer"></i>
                                    </div>
                                    
                                    @*<div class="form-group pull-right">
                                        <div class="btn-popover-container">
                                            <span class="popover-btn popover-pointer cb-text16 text-primary">
                                                <i style="color:gray" class="vert-center glyphicon glyphicon-zoom-in btn-apptest-status c-pointer"> </i>
                                            </span>
                                            <div class="btn-popover-title">
                                                <span class="default-text-color">@Model.ReportPhrases[i].Name</span>
                                            </div>
                                            <div class="btn-popover-content">
                                                <div class="pre-scrollable">
                                                    @Model.ReportPhrases[i].Value
                                                </div>
                                                <div class="row text-center">
                                                    <a href="#" class="btn btn-default btn-xs btn-close-status-change">
                                                        <i style="color:red" class="glyphicon glyphicon-remove"></i>Close
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <a class="popover-btn popover-pointer"
                                           data-placement="right"
                                           id="btn-phrase-zoom" target="_blank" href=# data-url='@Url.Action("ReportPhraseHistory", "VP", new { reportPhraseID = Model.ReportPhrases[i].Id, appointmentID = Model.AppointmentID,patientID=Model.PatientID , IsAccumulative = Model.ReportPhrases[i].Accumulative })'>

                                        </a>
                                    </div>*@
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                @{
                                    var mostRecent = "";
                                    if ((Model.ReportPhrases[i].Name).ToLower() == "most recent investigations") {
                                        mostRecent = "mostRecent";
                                    }
                                }                                

                                @Html.TextAreaFor(x => x.ReportPhrases[i].Value,                                   
                                    new
                                   {
                                       @id = "txtReportPhrase_" + @Model.ReportPhrases[i].Id,
                                       @class = "txtArea txtReportPhrases form-control " + mostRecent,
                                       //@rows = 9,
                                       @cols = 50,
                                       autocmplete = "off"
                                   })
                            </div>
                        </div>
                    </li>
                }
            </ol>

        </div>
    </div>
</div>

<script>
    $(document).ready(function(){
        var cnt = @Model.ReportPhrases.Count;
        
        //if (cnt <= 4){
        // console.log('adjust txtReportPhrases class height if  <=4');
        //$('.txtReportPhrases').css('height','125px');
        //}
         
        $('.txtReportPhrases').each(function (i, e) {
            if ($(this).text().length == 0) {
              $(this).css('height', '100px');
              // $(this).css('background-color', '#ff8800');
            }
        });

        var selectorsToExtend = '.txtReportPhrases';
        $(selectorsToExtend).on('keyup paste', function () {          
            var _this = $(this),
                offset = _this.innerHeight() - _this.height();
            if (_this.innerHeight < this.scrollHeight) {
                _this.height(this.scrollHeight - offset);
            }
            else {
                _this.height(0);
                _this.height(this.scrollHeight - offset);
            }
        }).keyup();

    });
</script>   