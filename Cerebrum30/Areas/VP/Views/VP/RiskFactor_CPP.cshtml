@model  Cerebrum.ViewModels.VP.VMRiskFactor
@{
    ViewBag.ModuleName = "CPP Risk Factors";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
@*<link href="~/Content/CPP_Styles.css" rel="stylesheet" />*@
<style>
    #frm-rf .row {
    margin-bottom:3px;
    }

</style>
@section scripts {
    <script>
        var LoadHistory = function () {
            var url = $("#div-history").data("url");
            ajaxCall(url, { patientID:@Model.PatientId }, false, function (data) {
                //console.log(data);
                $("#div-history").html('');
                $("#div-history").html(data);
            });
        };

        var LoadPatientName = function () {
            // $("#div-pat-name").html('<img src="../../Content/fancybox_loading.gif" />');
            ajaxCall("Patients/GetPatientInfoVPPages", { patientId:@Model.PatientId }, false, function (data) {
                $("#div-pat-name").html(data);
            });
        };

        function clear_form_elements(class_name) {
            jQuery(class_name).find(':input').each(function() {
                switch(this.type) {
                    case 'password':
                    case 'text':
                    case 'textarea':
                    case 'file':
                    case 'select-one':
                    case 'select-multiple':
                    case 'date':
                    case 'number':
                    case 'tel':
                    case 'email':
                        jQuery(this).val('');
                        break;
                    //case 'checkbox':
                    case 'radio':
                        this.checked = false;
                        break;
                }
            });
        }

        function Save_RiskFactor() {
            url = 'VP/VP/Save_RiskFactor';
            ajaxCall(url, $('#frm-rf').serialize(), false, function (data) {
                if(data.Errored == "1"){
                    $("#span-result-add").html('');
                    $("#span-result-add").html(data.Message);
                }
                else{
                    LoadHistory();
                    LoadCPP();
                    $("#span-result-add").html('');
                    $("#span-result-add").html('Changes Saved');
                    clear_form_elements('#divAdd');
                }
            });

            return false;
        }

        $(document).ready(function () {
            LoadPatientName();
            LoadHistory();
            $(document).on('click', '.btn-popover-close', function (e) {
                e.preventDefault();
                $(".popover-btn").popover('hide');
                $('[data-original-title]').popover('hide');
            });


            $('.hl-delete')
               .popover({
                   trigger: 'manual',
                   html: true,
                   placement: 'auto right'
               })

           .click(function(e){
               $('#ajax-loader').show();
               e.preventDefault();
               var box       = $(this);
               var title       = "";
               var _patientid = $(this).data("patientid");
               var _cppType   = $(this).data("cpptype");
               var _rowid     = $(this).data("rowid");
               var _cntrlName = $(this).data("name");
               var data = {  patientID:_patientid,cppType:_cppType,rowid:_rowid, cntrlName:_cntrlName  };
               url = 'VP/VP/ReasonForDeletion';
               ajaxCall(url, data, false, function (data) {
                   box.attr('data-content', data).popover('show');
               });
           });

            $('#btnAdd').click(function () {
                $('#divGrd').hide();
                $('#divAdd').show();
                return false;
            });

            $('#Unit').on('change', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#OnsetAge').val() , option: $('#Unit').val()},
                    success: function (data) {
                        if(data.Result == "0" ){
                            $('#LifeStage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $('#OnsetAge').on('blur', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#OnsetAge').val() , option: $('#Unit').val()},
                    success: function (data) {
                        if(data.Result == "0" ){
                            $('#LifeStage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $('#VP_RiskFactor_Edit_Unit').on('change', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_RiskFactor_Edit_OnsetAge').val() , option: $('#VP_RiskFactor_Edit_Unit').val()},
                    success: function (data) {
                        if(data.Result == "0" )
                        {
                            $('#VP_RiskFactor_Edit_LifeStage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {

                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });
            });

            $('#VP_RiskFactor_Edit_OnsetAge').on('blur', function () {
                $.ajax({
                    type: "POST",
                    url: 'VP/VP/GetLifeStage' ,
                    data :{  days : $('#VP_RiskFactor_Edit_OnsetAge').val() , option: $('#VP_RiskFactor_Edit_Unit').val()},
                    success: function (data) {
                        if(data.Result == "0" ){
                            $('#VP_RiskFactor_Edit_LifeStage').val(data.Message);
                        }
                    },
                    error: function (xhr, thrownError) {
                        alert("Error while tryng to call  'VP/VP/GetLifeStage'  " + xhr.status + " " + thrownError);
                    }
                });

            });

            //$('#btnCancel').click(function () {

            //    $('#divGrd').show();
            //    $('#divAdd').hide();
            //    //window.opener.location.reload();
            //    return false;
            //});

            $("#StartDate").datepicker();
            $("#EndDate").datepicker();
            $("#VP_RiskFactor_Edit_EndDate").datepicker();
        });

        $(document).on('click', '.btn-popover-close', function (e) {
            e.preventDefault();
            $(".popover-btn").popover('hide');
            $('[data-original-title]').popover('hide');
        });
    </script>
}

<div class="row">&nbsp;</div>
<div id="div-pat-name"></div>
<div class="row">&nbsp;</div>
<div class="row ">
    <div class="col-sm-12">
        <h4 class="modal-sub-title" style="font-size:18px;">Risk Factors</h4> <!-- label label-primary pull-left-->
        <div class=""><span style="background-color: #f2f2f2; color: #f28383; font-size: 18px;">Data will be visible in CPP summary page if checkbox is checked preset for all your patients&nbsp;&nbsp;</span></div>
    </div>
</div>

@using (Html.BeginForm("RiskFactor", "VP", FormMethod.Post, new { @id = "frm-rf", model = @Model }))
{
    @Html.HiddenFor(x => x.PatientId)
    @Html.HiddenFor(x => x.CPPVisibleField.PracticeDoctorId)
    @Html.HiddenFor(x => x.CPPVisibleField.VP_CPP_Category_Id)
    @Html.HiddenFor(x => x.CPPVisibleField.Col7Visible)
    @Html.HiddenFor(x => x.CPPVisibleField.Col8Visible)
    @Html.HiddenFor(x => x.CPPVisibleField.Col10Visible)
    @Html.HiddenFor(x => x.CPPVisibleField.Col11Visible)
    @Html.HiddenFor(x => x.CPPVisibleField.Col12Visible)

    <div class="text-center">
        <div class="container topPadding" id="divAdd" >
            <div class="row">
                @*<div class="col-md-12 text-left"><span style="background-color: #f2f2f2; color: #f28383; font-size: 18px;">&nbsp;&nbsp;Data will be visible in CPP summary page if checkbox is checked preset for all your patients&nbsp;&nbsp;</span></div>*@
                <div class="col-md-12 text-center">
                    <div class="rowContainer">

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                @Html.CheckBoxFor(m => m.Visible)
                                Show :
                            </div>
                            <div class="col-sm-8 text-left">
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                @Html.CheckBoxFor(x => x.CPPVisibleField.Col2Visible)
                                Risk Factors :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextAreaFor(m => m.RiskFactor, new { @class = "txtArea" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                @Html.CheckBoxFor(x => x.CPPVisibleField.Col1Visible)
                                Exposure Details :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextAreaFor(m => m.ExposureDetails, new { @class = "txtArea" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                @Html.CheckBoxFor(x => x.CPPVisibleField.Col3Visible)
                                Notes :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextAreaFor(m => m.Notes, new { @class = "txtArea" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                @Html.CheckBoxFor(x => x.CPPVisibleField.Col5Visible)
                                Start Date :
                            </div>
                            <div class="col-sm-8 text-left">
                                @*@Html.TextBoxFor(m => m.StartDate, new { @class = "largetxtBox", @readonly = "readonly" })*@

                                <div class="col-sm-1 spanCell">Day</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.StartDateDay, Model.Days, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell">Month</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.StartDateMonth, Model.Months, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell">Year</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.StartDateYear, Model.Years, "--Select--", new {})
                                </div>

                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                @Html.CheckBoxFor(x => x.CPPVisibleField.Col6Visible)
                                End Date :
                            </div>
                            <div class="col-sm-8 text-left">
                                @*@Html.TextBoxFor(m => m.EndDate, new { @class = "txtBox", @readonly = "readonly" })*@
                                <div class="col-sm-1 spanCell">Day</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.EndDateDay, Model.Days, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell">Month</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.EndDateMonth, Model.Months, "--Select--", new {})
                                </div>
                                <div class="col-sm-1 spanCell">Year</div>
                                <div class="col-sm-2">
                                    @Html.DropDownListFor(x => x.EndDateYear, Model.Years, "--Select--", new {})
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                @Html.CheckBoxFor(x => x.CPPVisibleField.Col4Visible)
                                Age of Onset :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextBoxFor(m => m.OnsetAge, new { @class = "txtBox" })
                                @Html.DropDownListFor(x => x.Unit, Model.Units, "--Select--", new {})
                                <span style="padding-left: 32px;"></span>
                                <span class="spanCell">Life Stage </span>
                                @Html.DropDownListFor(m => m.LifeStage, Model.LifeStages, "--Select--", new {})
                                @*@Html.TextBoxFor(m => m.LifeStage, new { @class = "txtBox", @readonly = "readonly" })*@
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                Position :
                            </div>
                            <div class="col-sm-8 text-left">
                                @Html.TextBoxFor(m => m.Position, new { @class = "largetxtBox" })
                            </div>
                        </div>

                        <div class="row">
                            <div class="spanCell col-sm-4 text-right">
                                @Html.CheckBoxFor(x => x.CPPVisibleField.Col9Visible)
                                Status :
                            </div>
                            <div class="col-sm-8 text-left">
                                @*@Html.TextBoxFor(m => m.VP_RiskFactor_Edit.Status, new { @class = "largetxtBox"})*@
                                @Html.DropDownListFor(m => m.Status, Model.ActiveList, "--Select--", new {})
                            </div>
                        </div>

                        <div class="row">
                            &nbsp;
                        </div>

                        <div class="row">
                            <div class="col-sm-12">
                                <button class="btn btn-default" onclick="return Save_RiskFactor();">Save</button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12 ">
                                <span class="redError" id="span-result-add">
                                    
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
         
        <div id="div-history" data-url="@Url.Action("RiskFactor_CPP_Data", "VP", new { Area = "VP" })"></div>

        <button class="btn btn-default btn-sm btn-cancel-model">
            <i style="color:red" class="glyphicon glyphicon-remove"></i>Close
        </button>

    </div>
}

