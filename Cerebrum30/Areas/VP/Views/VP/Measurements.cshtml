@model Cerebrum.ViewModels.VP.VP_VM

<script>
    $(document).ready(function () {
        $('[data-toggle="tooltip"]').tooltip({ animation: false });
        //LoadVSHistory();
    });
</script>

@*//vital signs*@
@for (int i = 0; i < Model.VitalSignCategories.Count; i++)
{
    @Html.HiddenFor(x => x.VitalSignCategories[i].id)
    @*@Html.HiddenFor(x => x.VitalSignCategories[i].Name)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Order)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Status)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Dt)
        @Html.HiddenFor(x => x.VitalSignCategories[i].DrID)*@

for (int j = 0; j < @Model.VitalSignCategories[i].Measurements.Count; j++)
{

    @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].Id)
    @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].Name)
    @*@Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].Order)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].Normal)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].Range1)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].Range2)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].Spec)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].Status)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].Type)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].Units)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].VPCategoryID)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].DrID)
        @Html.HiddenFor(x => x.VitalSignCategories[i].Measurements[j].ErrorMessage)*@
    }
}

@*//Lab Results*@
@for (int i = 0; i < Model.LabResultCategories.Count; i++)
{

    @Html.HiddenFor(x => x.LabResultCategories[i].id)
    @*@Html.HiddenFor(x => x.LabResultCategories[i].Name)
        @Html.HiddenFor(x => x.LabResultCategories[i].Order)
        @Html.HiddenFor(x => x.LabResultCategories[i].Status)
        @Html.HiddenFor(x => x.LabResultCategories[i].DrID)
        @Html.HiddenFor(x => x.LabResultCategories[i].Dt)*@

for (int j = 0; j < @Model.LabResultCategories[i].Measurements.Count; j++)
{
    @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Id)
    @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Name)
    @*@Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Order)
        @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Normal)
        @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Range1)
        @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Range2)
        @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Spec)
        @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Status)
        @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Type)
        @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].Units)
        @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].VPCategoryID)
        @Html.HiddenFor(x => x.LabResultCategories[i].Measurements[j].DrID)*@
    }
}


@*<div class="row">&nbsp;</div>*@
<div id="div-vp-ms" class="__009857 panel panel-info">
    <div class="panel-heading">Measurements</div>
    <div class="panel-body">


        <div class="row">
            <div class="col-sm-12">
                <div class="col-sm-3">
                    <span class="small">Source: </span>
                    <span class="small green">@Model.Source</span>
                </div>
                <div class="col-sm-3">
                    <span class="small">CDF Date: </span>
                    <span class="small">@Model.CDF_CollectionDate</span>
                </div>
                <div class="col-sm-3">
                    <span class="small smallertext">HL7 Collection Date: </span>
                    <span class="small">@Model.HL7_CollectionDate</span>
                </div>
                <div class="col-sm-3 pull-right">
                    <span class="small smallertext">DB Date: </span>
                    <span class="small">@Model.DB_CollectionDate</span>
                </div>
            </div>
        </div>

        <div class="row">&nbsp;</div>

        @for (int i = 0; i < Model.vm_cdf.TemplateDetails.Count; i++)
        {
            @Html.Hidden("vm_cdf.TemplateDetails[" + i + "].TemplateItemName", Model.vm_cdf.TemplateDetails[" + i + "].TemplateItemName)
            @Html.Hidden("vm_cdf.TemplateDetails[" + i + "].TestCode", Model.vm_cdf.TemplateDetails[i].TestCode)
            @Html.Hidden("vm_cdf.TemplateDetails[" + i + "].VPTemplateField", Model.vm_cdf.TemplateDetails[" + i + "].VPTemplateField)
        }

        <div>

            <div class="form-inline">
                <div class="form-group">
                    <span class="custom-label">
                        Vitals
                    </span>
                </div>
                <div class="form-group form-group-sm">
                    <div id="div-meas-History">
                        @(await Html.PartialAsync("VP_Menu", Model.VsHistory))
                    </div>
                </div>
            </div>

            <table cellpadding="0" class="table custom-table table-condensed table-bordered _0098723 spacer-btm-7">
                @for (int i = 0; i < Model.VitalSignCategories.Count; i++)
                {
                    <tr>
                        <td>

                            <div>
                                <ol>
                                    @for (int j = 0; j < @Model.VitalSignCategories[i].Measurements.Count; j++)
                                    {
                                        <li class="col-sm-2">
                                            <div>
                                                <div>

                                                    <span class="hideOverflowMeas small "
                                                          data-toggle="tooltip"
                                                          data-placement="top"
                                                          tooltip="@Model.VitalSignCategories[i].Measurements[j].Name"
                                                          title="@Model.VitalSignCategories[i].Measurements[j].Name">
                                                        @Model.VitalSignCategories[i].Measurements[j].Name
                                                    </span>


                                                    @Html.TextBoxFor(x => x.VitalSignCategories[i].Measurements[j].Value, new { @class = " txtBox vp-meas-box" })


                                                    <a data-toggle="tooltip"
                                                       data-placement="right"
                                                       class="hideOverflowUnits" title="@Model.VitalSignCategories[i].Measurements[j].Units">
                                                        (@Model.VitalSignCategories[i].Measurements[j].Units)
                                                    </a>

                                                    @{
                                                        var history = Model.VitalSignCategories[i].Measurements[j].HistoricalValues.Select(s => "Value :" + s.Value + " ( " + s.UserName + ")").ToArray();
                                                        var str = string.Join(",", history);
                                                    }
                                                    @if (!string.IsNullOrWhiteSpace(str))
                                                    {
                                                        <span data-toggle="tooltip"
                                                              data-placement="right"
                                                              class="glyphicon glyphicon-time" title="@str">
                                                        </span>
                                                    }
                                                    @if (!string.IsNullOrEmpty(@Model.VitalSignCategories[i].Measurements[j].ErrorMessage))
                                                    {
                                                        <br />
                                                        <span class="label label-danger">@Model.VitalSignCategories[i].Measurements[j].ErrorMessage </span>
                                                    }
                                                </div>
                                            </div>
                                        </li>
                                                        }
                                </ol>
                            </div>

                        </td>
                    </tr>
                                                        }
            </table>
        </div>

        <div class="">
            <div>
                <span class="custom-label">
                    Labs
                </span>
                <table cellpadding="0" class="table custom-table table-condensed table-bordered __0098432 spacer-btm-7">
                    @for (int i = 0; i < Model.LabResultCategories.Count; i++)
                    {
                        <tr>
                            <td>
                                @*<p>
                                        <span class="label label-primary label-app-type 456456">
                                            @Model.LabResultCategories[i].Name
                                        </span>
                                    </p>*@
                                <div>
                                    <ol>
                                        @for (int j = 0; j < @Model.LabResultCategories[i].Measurements.Count; j++)
                                        {
                                            <li class="col-sm-2">
                                                <div class="">


                                                    <span class="hideOverflowMeas small "
                                                          data-toggle="tooltip"
                                                          data-placement="top"
                                                          tooltip="@Model.LabResultCategories[i].Measurements[j].Name"
                                                          title="@Model.LabResultCategories[i].Measurements[j].Name">
                                                        @Model.LabResultCategories[i].Measurements[j].Name
                                                    </span>

                                                    @Html.TextBoxFor(x => x.LabResultCategories[i].Measurements[j].Value, new { @class = " txtBox vp-meas-box" })

                                                    <a data-toggle="tooltip"
                                                       data-placement="right"
                                                       class="hideOverflowUnits" title="@Model.LabResultCategories[i].Measurements[j].Units">
                                                        (@Model.LabResultCategories[i].Measurements[j].Units)
                                                    </a>

                                                    @if (!string.IsNullOrEmpty(@Model.LabResultCategories[i].Measurements[j].ErrorMessage))
                                                    {
                                                        <br />
                                                        <span class="label label-danger">@Model.LabResultCategories[i].Measurements[j].ErrorMessage</span>
                                                    }
                                                    @if (Model.LabResultCategories[i].Measurements[j].OverTarget)
                                                    {
                                                        var target = "Out of Normal: " + @Model.LabResultCategories[i].Measurements[j].Normal;

                                                        <i data-toggle="tooltip"
                                                           title="@target"
                                                           style="color:red" class="glyphicon glyphicon-exclamation-sign"></i>
                                                    }
                                                </div>
                                            </li>
                                        }
                                    </ol>
                                </div>
                            </td>
                        </tr>
                    }
                </table>
            </div>
        </div>

        @if (Model.vm_cdf.TemplateDetails.Count > 0)
        {
            <div>
                <span class="custom-label">
                    CDF
                </span>
                <table cellpadding="0" class="table custom-table table-condensed __77654 spacer-btm-7">
                    <tr>
                        <td>
                            @*<p>
                                    <span class="label label-primary label-app-type">
                                        CDF
                                    </span>
                                </p>*@
                            <div>
                                <ol>
                                    @for (int j = 0; j < Model.vm_cdf.TemplateDetails.Count(); j++)
                                    {
                                        <li class="col-sm-2">
                                            <div>
                                                <div style="">

                                                    <span class="hideOverflowMeas small "
                                                          style="display:inline-block; float: left"
                                                          data-toggle="tooltip"
                                                          data-placement="top"
                                                          tooltip="@Model.vm_cdf.TemplateDetails[j].TemplateItemName"
                                                          title="@Model.vm_cdf.TemplateDetails[j].TemplateItemName">
                                                        @Model.vm_cdf.TemplateDetails[j].TemplateItemName
                                                    </span>

                                                    @*//TextBoxFor*@

                                                    @{
                                                        var nmvt = $"vm_cdf.TemplateDetails[{j}].ValueType";
                                                        bool isUnit = false;//mutli-line
                                                        if (!string.IsNullOrEmpty(Model.vm_cdf.TemplateDetails[j].Units))
                                                        {
                                                            isUnit = true;
                                                        }
                                                    }
                                                   
                                                    @if (Model.vm_cdf.TemplateDetails[j].ValueType == AwareMD.Cerebrum.Shared.Enums.ValueType.YesNo)
                                                    {
                                                        var v = $"vm_cdf_TemplateDetails_{j}__Value";
                                                        var nm = $"vm_cdf.TemplateDetails[{j}].Value";
                                                       
                                                        AwareMD.Cerebrum.Shared.Enums.YesNo myval;
                                                        List<SelectListItem> items = new List<SelectListItem>();
                                                        if(Enum.TryParse(Model.vm_cdf.TemplateDetails[j].Value, out myval))
                                                        {
                                                            items = EnumHelper.GetSelectList(typeof(AwareMD.Cerebrum.Shared.Enums.YesNo), myval).ToList();
                                                        }
                                                        else
                                                        {
                                                            items = EnumHelper.GetSelectList(typeof(AwareMD.Cerebrum.Shared.Enums.YesNo)).ToList();
                                                        }

                                                        @Html.DropDownListFor(x => Model.vm_cdf.TemplateDetails[j].Value,items, "...", Model.vm_cdf.TemplateDetails[j].Value)


                                                        @Html.Hidden(nmvt, Model.vm_cdf.TemplateDetails[j].ValueType)
                                                    }
                                                    else
                                                    {

                                                        @Html.TextAreaFor(x => @Model.vm_cdf.TemplateDetails[j].Value, new
                                                   {
                                                       @class = " txtBox vp-meas-box meas-cdf-item",
                                                       @data = @Model.vm_cdf.TemplateDetails[j].TemplateItemName,
                                                       @readonly = "readonly",
                                                       @data_toggle = "tooltip",
                                                       @data_placement = "right",
                                                       @title = Model.vm_cdf.TemplateDetails[j].Value,
                                                       @style = "white-space:pre; resize: none; overflow:hidden;padding:2px",
                                                       @isunit = @isUnit,
                                                       @rows = 1
                                                   })
                                                        @Html.Hidden(nmvt, Model.vm_cdf.TemplateDetails[j].ValueType)
                                                    }
                                                    @*@if (!string.IsNullOrEmpty(Model.vm_cdf.TemplateDetails[j].Units))*@
                                                    <div style="display: inline-block; position: absolute; margin-left: 3px;">
                                                        @if (isUnit)
                                                        {
                                                            <a style="margin-top: -2px"
                                                               data-toggle="tooltip"
                                                               data-placement="right"
                                                               class="hideOverflowUnits"
                                                               title="@Model.vm_cdf.TemplateDetails[j].Units">
                                                                (@Model.vm_cdf.TemplateDetails[j].Units)
                                                            </a>
                                                        }

                                                        @if (Model.vm_cdf.TemplateDetails[j].OverDue)
                                                        {
                                                            <i data-toggle="tooltip"
                                                               title="Out of Treatment Interval" @*OverDue*@
                                                               style="color:red" class="glyphicon glyphicon-time"></i>
                                                        }
                                                    </div>

                                                    @*@if (!string.IsNullOrEmpty(@Model[j].ErrorMessage))
                                                            {
                                                                <br />
                                                                <span class="label label-danger">@Model[j].ErrorMessage </span>
                                                        }*@
                                                </div>
                                            </div>
                                        </li>
                                                        }
                                </ol>
                            </div>
                    </tr>
                </table>

            </div>
                                                        }

    </div>
</div>



<!--------------------- ----->
<div class="container">
    <!-- Modal -->
    <div class="modal fade" id="mdlCDF" role="dialog">

        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Modal Header</h4>

                </div>
                <div class="modal-body">

                    <div id="__placeHolder__"></div>

                    <input type="hidden" id="mdlCDF_ctrlIDToUpdate" />
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default btn-sm btn-primary" data-dismiss="modal" id="btnModal-CDF-update">Update</button>

                    <button type="button" class="btn btn-default btn-sm" data-dismiss="modal" id="btnModal-CDF-close">Close</button>
                </div>
            </div>

        </div>
    </div>
</div>
<script>

    $(document).ready(function () {
        var isUnit;
        var selectedCtrlIDToUpdate;



        $('.meas-cdf-item').on('click', function () {

            selectedCtrlIDToUpdate = $(this).attr('id');
            var selectedCtrlCurrentVal = $(this).val();

            var selectedCtrl_isUnit = $.parseJSON($(this).attr('isunit').toLowerCase());


            var updateCtrl = $('<textarea/>', {
            });

            if (selectedCtrl_isUnit) {
                updateCtrl = $('<input/>', {
                    'type': 'text'
                });
            }


            $.when(updateCtrl.appendTo('#__placeHolder__')).done(function () {
                updateCtrl.attr('id', 'mdlCDF_ctrlNewContent').addClass('cls').val(selectedCtrlCurrentVal);
            });


            $('#mdlCDF .modal-title').html('CDF: ' + $(this).prev('span').html());

            $('#mdlCDF').modal();
        });

        $('#btnModal-CDF-update').on('click', function () {
            var newVal = $('#mdlCDF_ctrlNewContent').val()
                   .replace(/</g, '&lt;')
                   .replace(/>/g, '&gt;')
                   .replace(/\n/g, '<br/>');

            eval(" $('#" + selectedCtrlIDToUpdate + "').val('" + newVal.replace(/<br\s*\/?>/ig, '\\r') + "') ").css('background-color', '#ffe498');
        });


        $('#mdlCDF').on('shown.bs.modal', function (e) { // show: about to be shown
            $('.cls:first').focus();
        });

        $('#mdlCDF').on('hidden.bs.modal', function () {
            $('#__placeHolder__').empty(); //cleanup
        });
    });
</script>