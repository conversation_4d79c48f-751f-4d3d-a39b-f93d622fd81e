@model IEnumerable<Cerebrum.ViewModels.Documents.VMReferralDocument>

@{
    var totalDocs = Model.Count();
    var docNumber = 1;
}

<script type="text/javascript" src="~/Scripts/tiff.min.js"></script>     @*https://raw.githubusercontent.com/seikichi/tiff.js/master/tiff.min.js*@
<script src="~/Areas/ExternalDocument/Scripts/ExternalDocument.js"></script>
<script>
    function ShowReferralDocument(element) {
        var appointmentId = $(element).data("appointment-id");
        var dataUrl = $(element).data("file-path");
        var clinicServerUrl = $(element).data("clinic-server-url");
        var token = $(element).data("token");
        var mainServerUrl = "Measurements/Measurement/RetrieveFile?path=" + escape(dataUrl) + "&appointmentID=" + appointmentId;
        readExternalDocument(clinicServerUrl, dataUrl, token, appointmentId, 0, "", mainServerUrl, 1, 0);

        return false;
    }
</script>

<div class="panel panel-info content-height300">
    <div class="panel-heading">
        <h3 class="panel-title">Referral Documents <span id="spantotalreferdocnumber" class="badge cbadge">@totalDocs</span></h3>
    </div>
    <table id="table-ref-documents" class="table">
        <thead>
            <tr>
                <th style="width:25%;">
                    @Html.DisplayNameFor(model => model.FileName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.FileDescription)
                </th>
                <th style="width:10%;">

                </th>
                <th style="width:10%;">

                </th>
            </tr>
        </thead>
        <tbody>

            @foreach (var item in Model)
            {
                <tr>
                    <td>
                        @item.FileName
                    </td>
                    <td>
                        @item.FileDescription
                    </td>
                    @*<td>
                            <button data-document-total="@totalDocs" data-document-num="@docNumber" data-document-id="@item.Id" data-document-path="@item.FilePath" type="button" class="btn btn-xs btn-primary btn-view-document">View</button>
                        </td>*@
                    <td>
                        <a data-appointment-id="@item.ApppintmentID" data-file-path="@item.FilePath.Replace("\\" ,"|").Replace(":", "||")" data-clinic-server-url="@item.ClinicServerUrl" data-token="@item.Token" onclick="ShowReferralDocument(this)">@item.FilePath</a>
                        @*<a target="_blank" href="/Measurements/Measurement/RetrieveFile?path=@item.FilePath.Replace("\\" ,"|").Replace(":", "||")&appointmentID=@item.ApppintmentID">@item.FilePath</a>*@
                        @*@Html.ActionLink(@item.FilePath, "RetrieveFile", "Measurement", new { @Area="Measurements", path=item.FilePath, token=item.Token, appointmentID=item.ApppintmentID   })*@
                    </td>
                    <td>
                        <button data-document-total="@totalDocs" data-document-num="@docNumber" data-document-id="@item.Id" data-document-path="@item.FilePath" type="button" class="btn btn-xs btn-info btn-inactive-document">Inactivate</button>
                    </td>
                </tr>
                docNumber++;
            }
        </tbody>
    </table>
</div>