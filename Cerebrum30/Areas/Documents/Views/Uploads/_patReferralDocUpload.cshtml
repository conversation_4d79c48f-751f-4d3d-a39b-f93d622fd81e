@model Cerebrum.ViewModels.Documents.VMRefDocumentUpload


@using (Html.BeginForm("UploadReferralDocument", "uploads", new { area = "documents" }, FormMethod.Post, true, new { enctype = "multipart/form-data", @id = "frm-ref-doc-upload" }))
{
    var allowedTypes = string.Join(",",((List<string>)ViewBag.AllowedFileTypes));
    @Html.ModalHeader("Upload Referral Documents for " + Model.PatientFullName)
    <div class="modal-body">
        @(await Html.PartialAsync("_validationSummary"))       
        <div class="form-horizontal">
            @Html.AntiForgeryToken()            
            @Html.HiddenFor(model => model.AppointmentId)
            @Html.HiddenFor(model => model.OfficeId)
            @Html.HiddenFor(model => model.PatientId)
            @Html.HiddenFor(model => model.PatientFullName)
            @*@Html.HiddenFor(model => model.FileDescription)*@
            <div class="form-group form-group-sm">
                @Html.Label("Allowed types", new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <p class="form-control-static">@allowedTypes</p>
                </div>
            </div> 
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.FileDescription, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10">
                   @Html.EditorFor(model => model.FileDescription, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.FileDescription, "", new { @class = "text-danger" })
                </div>
            </div> 
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.File, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10">
                    <input type="file" id="File" name="File" style="width:280px;" />
                    @Html.ValidationMessageFor(model => model.File, "", new { @class = "text-danger" })
                </div>                
            </div> 
            @*<div class="form-group form-group-sm">
                @Html.Label("Selected Files", new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div id="selected-files">
                        <ul style="list-style:none;padding:0px;margin:0px;font-size:11px;">
                            @foreach(var file in Model.FilesToUpload)
                            {
                                <li>@file.FileName <span> size: @file.ContentLength</span></li>
                            }
                        </ul>
                    </div>      
                </div>               
            </div>*@ 
                 
        </div>
    </div>
    @Html.ModalFooter("Upload File", "blue")
}




    




