@model IEnumerable<Cerebrum.ViewModels.Documents.VMSendReportItem>

@{
    var totalDocs = Model.Count();
}

<link href="~/Areas/Documents/Content/style.css" rel="stylesheet" />
<script src="~/Areas/Documents/Scripts/_sendHistoryList.js"></script>

<div class="panel panel-info content-height300">
    <div class="panel-heading">
        <h3 class="panel-title">Send History</h3>
        <div class="checkbox-container">
            <input type="checkbox" id="show-errors">
            <label for="show-errors">Show only errors</label>
        </div>
    </div>
    <table id="history-table" class="table table-condensed">
        <tr>
            <td width="5%">
                <b>Doctor</b>
            </td>
            <td width="5%">
                <b>Email</b>
            </td>
            <td width="10%">
                <b>Fax</b>
            </td>
            <td width="9%">
                <b>Date</b>
            </td>
            <td width="5%">
                <b>Sent</b>
            </td>
            <td width="9%">
                <b>Sent Method</b>
            </td>
            <td width="5%">
                <b>URL</b>
            </td>
            <td width="52%"><b>Error</b></td>
        </tr>
        @foreach (var item in Model)
        {
            if (!String.IsNullOrWhiteSpace(item.URL))
            {
                var trClass = !item.Sent ? "danger" : "";
                var errorClass = item.Sent ? "sent-row" : "error-row";
                <tr class="@trClass @errorClass">
                    <td>
                        @item.DocName
                    </td>
                    <td>
                        @item.EmailTo
                    </td>
                    <td>
                        @item.FaxTo
                    </td>
                    <td>
                        @item.DateEntered
                    </td>
                    <td>
                        @item.Sent
                    </td>

                    <td>
                        @item.SendType
                    </td>
                    <td>
                        @if (item.SendType != AwareMD.Cerebrum.Shared.Enums.SendType.HRM)
                        {
                            @Html.ActionLink(item.URL, "ViewReportUrl", "Reports", new { area = "Documents", url = item.URL, appointmentId = item.AppointmentId }, new { @target = "_blank" })
                        }
                    </td>
                    <td>
                        @Html.TextAreaFor(modelItem => item.ErrorMessage, new { @class = "form-control textarea-small-font" })
                    </td>
                </tr>
            }
        }
    </table>

</div>