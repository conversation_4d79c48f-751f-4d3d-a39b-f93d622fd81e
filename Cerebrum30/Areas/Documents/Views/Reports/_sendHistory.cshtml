@model Cerebrum.ViewModels.Documents.VMFileResendToDoctors

@if (Model.ErrorMessage != null && Model.ErrorMessage.Any())
{
    <div class="row">
        <div class="col-md-12" style="font-size: 18px; color: red; padding-bottom: 16px;">
            The report cannot be sent through HRM, if urgent, please send it through <b>fax</b> or <b>email</b>, if not, resolve the errors displayed below and send it.
        </div>
    </div>
}

@using (Html.BeginForm("ResendFile", "reports", new { area = "documents" }, FormMethod.Post, true, new { @id = "frm-resend-file" }))
{

    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.AppointmentTestId)
    @Html.HiddenFor(model => model.AppointmentId)
    @Html.HiddenFor(model => model.TestId)
    @Html.HiddenFor(model => model.PatientId)
    @Html.HiddenFor(model => model.HasSameMainAndResponsible)
    @Html.HiddenFor(model => model.AppointmentTestLogId)
    @Html.HiddenFor(model => model.IsAmended)
    @Html.HiddenFor(model => model.ChangeStatus)

    <div class="panel panel-primary">
        <div class="panel-heading">
            <div>
                <h3 style="padding-top:5px;" class="panel-title">Contacts</h3>
            </div>
        </div>
        <div class="panel-body">
            <div>
                <table class="table  table-condensed">
                    <tr>
                        <td><b>Names</b></td>
                        <td><b>Type</b></td>
                        <td><b>HRM</b></td>
                        <td @(Model.ErrorMessage != null ? "width=20%" : "")><b>Fax</b></td>
                        <td><b>Mail</b></td>
                        <td><b>Email</b></td>
                    </tr>

                    @for (int i = 0; i < Model.Doctors.Count; i++)
                    {
                        var attributes = new Dictionary<string, object>();

                        if (Model.ErrorMessage != null && Model.ErrorMessage.Any() && !Model.SendHistoryList.Any())
                        {
                            attributes.Add("disabled", "disabled");
                        }

                        <tr>
                            <td>
                                @Model.Doctors[i].Name
                                @Html.HiddenFor(x => x.Doctors[i].AppointmentId)
                                @Html.HiddenFor(x => x.Doctors[i].ExternalDoctorId)
                                @Html.HiddenFor(x => x.Doctors[i].ReportResourceType)
                                @Html.HiddenFor(x => x.Doctors[i].DocType)
                                @Html.HiddenFor(x => x.Doctors[i].FirstName)
                                @Html.HiddenFor(x => x.Doctors[i].LastName)
                                @Html.HiddenFor(x => x.Doctors[i].Name)
                                @Html.HiddenFor(x => x.Doctors[i].Address)
                                @Html.HiddenFor(x => x.Doctors[i].City)
                                @Html.HiddenFor(x => x.Doctors[i].Province)
                                @Html.HiddenFor(x => x.Doctors[i].Degrees)
                                @Html.HiddenFor(x => x.Doctors[i].LetterHead)
                                @Html.HiddenFor(x => x.Doctors[i].CPSO)
                                @Html.HiddenFor(x => x.Doctors[i].OHIPId)
                                @Html.HiddenFor(x => x.Doctors[i].IsMainReportDoctor)
                                @Html.HiddenFor(x => x.Doctors[i].IsMainResponsibleDoctor)
                                @Html.HiddenFor(x => x.Doctors[i].IsTrainee)
                                @Html.HiddenFor(x => x.Doctors[i].AppointmentDateTime)
                            </td>
                            <td>@Model.Doctors[i].DocType</td>
                            <td>
                                @Html.CheckBox("Doctors[" + i + "].HRM, attributes", Model.Doctors[i].HRM, attributes)
                            </td>
                            <td>
                                @Html.CheckBox("Doctors[" + i + "].Fax", Model.Doctors[i].Fax)
                                @if (Model.ErrorMessage != null)
                                {
                                    @Html.TextBox("Doctors[" + i + "].FaxNumber", Model.Doctors[i].FaxNumber)
                                }
                                else
                                {
                                    @Html.HiddenFor(x => x.Doctors[i].FaxNumber)
                                }
                            </td>
                            <td>
                                @Html.CheckBox("Doctors[" + i + "].Mail", Model.Doctors[i].Mail)
                            </td>
                            <td>
                                @Html.CheckBox("Doctors[" + i + "].Email", Model.Doctors[i].Email)
                                @Html.TextBox("Doctors[" + i + "].EmailAddress", Model.Doctors[i].EmailAddress)
                            </td>

                        </tr>
                    }
                </table>

            </div>
        </div>
    </div>


    <div class="row">
        <div class="col-md-12">
            <div style="margin-bottom:15px;" class="text-right">
                @if (Model.SendHistoryList.Any())
                {
                    <button type="submit" id="btn-frm-resend-report" class="btn custom-btn-primary" data-url='@Url.Action("ResendFile", "reports", new { area = "documents" })'>Resend</button>
                }
                @if (Model.ErrorMessage != null && Model.ErrorMessage.Any() && !Model.SendHistoryList.Any())
                {
                    <button type="button" id="btn-frm-save-report-sending-options-send" class="btn custom-btn-primary" data-url='@Url.Action("SaveReportSendingOptions", "Measurement" , new {area="Measurements" })'>Send</button>
                }
                @if (Model.ErrorMessage != null && Model.ErrorMessage.Any())
                {
                    <button type="button" id="btn-frm-save-report-sending-options-create-task" class="btn custom-btn-primary" onclick="document.getElementById('frm-hrm-new-task').submit();">Create Task</button>
                }
                <button type="button" data-dismiss="modal" id="btn-frm-resend-file-close" class="hidden"></button>
            </div>
        </div>
    </div>



    if (Model.SendHistoryList != null)
    {
        for (int i = 0; i < Model.SendHistoryList.Count; i++)
        {
            @Html.HiddenFor(model => model.SendHistoryList[i].Id)
            @Html.HiddenFor(model => model.SendHistoryList[i].EmailTo)
            @Html.HiddenFor(model => model.SendHistoryList[i].FaxTo)
            @Html.HiddenFor(model => model.SendHistoryList[i].Location)
            @Html.HiddenFor(model => model.SendHistoryList[i].DateEntered)
            @Html.HiddenFor(model => model.SendHistoryList[i].Sent)
            @Html.HiddenFor(model => model.SendHistoryList[i].Amended)
            @Html.HiddenFor(model => model.SendHistoryList[i].IsVP)
            @Html.HiddenFor(model => model.SendHistoryList[i].AppointmentId)
            @Html.HiddenFor(model => model.SendHistoryList[i].TestId)
            @Html.HiddenFor(model => model.SendHistoryList[i].PatientId)
            @Html.HiddenFor(model => model.SendHistoryList[i].SendType)
            @Html.HiddenFor(model => model.SendHistoryList[i].PhysicalPath)
            @Html.HiddenFor(model => model.SendHistoryList[i].URL)
            @Html.HiddenFor(model => model.SendHistoryList[i].DocName)
            @Html.HiddenFor(model => model.SendHistoryList[i].ErrorMessage)
            @Html.HiddenFor(model => model.SendHistoryList[i].MessageId)
        }
    }
}

@if (Model.ErrorMessage != null && Model.ErrorMessage.Any())
{
    <div class="panel panel-primary">
        <div class="panel-heading" style="background-color: #f7f7f7;">
            <div>
                <h3 style="padding-top:5px;" class="panel-title">HRM Error(s)</h3>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12" style="font-size: 14px; padding-top: 12px; padding-bottom: 16px; padding-left: 20px;">
                @Html.Raw(Model.ErrorMessage)
            </div>
        </div>
    </div>
}

<div id="send-history-list-wrapper">
    @if (Model.SendHistoryList.Any())
    {
        await Html.RenderPartialAsync("_sendHistoryList", Model.SendHistoryList);
    }
</div>

@if (Model.ErrorMessage != null && Model.ErrorMessage.Any())
{
    using (Html.BeginForm("HrmNewTask", "ContactManagers", new { area = "ContactManagers" }, FormMethod.Post, true, new { @id = "frm-hrm-new-task", @target = "_blank" }))
    {
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => model.AppointmentTestId)
        @Html.HiddenFor(model => model.AppointmentTestLogId)
        @Html.HiddenFor(model => model.AppointmentId)
        @Html.HiddenFor(model => model.TestId)
        @Html.HiddenFor(model => model.PatientId)
        @Html.HiddenFor(model => model.ErrorMessage, new { @Value = Model.ErrorMessage.Replace("<br />", Environment.NewLine) })
    }
}