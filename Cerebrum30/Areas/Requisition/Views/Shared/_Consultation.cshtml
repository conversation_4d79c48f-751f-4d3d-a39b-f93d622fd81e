@model Cerebrum.ViewModels.Consultation.ConsultationForm
@*@{
        ViewBag.Title = "ConsulForm";
        Layout = "~/Views/Shared/_Layout.cshtml";
    }*@

@*<link href="~/Content/jquery.timepicker.css" rel="stylesheet" />*@
<style type="text/css">
    .ui-dialog, .ui-widget, .ui-widget-content {
        background: white !important;
    }

    .ui-dialog-titlebar {
        background-color: steelblue;
        color: yellow;
    }
</style>
@*<script src="~/Scripts/jquery-ui-1.12.1.min.js"></script>*@
<script src="~/Scripts/jquery.timepicker.js"></script>
<script src="~/Scripts/cf_form.js"></script>
<link href="~/Content/cf_form.css" rel="stylesheet" />
<head id="Head1">

</head>
<body>
    @*@using (Html.BeginForm("ConsultForm", "ConsultationForm", FormMethod.Post, new { consultFormModel = Model }))*@
    @using (Html.BeginForm("ConsultForm", "ConsultationForm", FormMethod.Post, new { consultFormModel = Model }))
    {
        @*<div id="ajax-loader">
                <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
            </div>*@
        <div id="mainFrame">
            <div id="header">
                <div id="header-1">
                    <div id="headerLeft">
                        <div id="forLogo">
                            <div id="reqDateDiv">
                                <div id="reqDateStr">
                                    <span>Request date :</span>
                                    @Html.HiddenFor(x => x.dateOfRequestHid)
                                    @*@Html.ActionLink("Requisitin Pdf", "FormRequisition", "ConsultationForm", new { requisitionId = "42" }, new { target = "blank_" })*@
                                </div>
                                <div id="reqDateDate">
                                    <span>
                                        @Html.LabelFor(m => m.dateOfRequest, Model.dateOfRequest, new { id = "dateOfRequest" })
                                        @Html.HiddenFor(x => x.officeId)
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="letterHead">
                            <div class="refDoctor_">
                                <div class="labelRefDoc">
                                    @Html.LabelFor(m => m.refDoctorName, Model.refDoctorName, new { id = "refDoctorName" })
                                    @Html.HiddenFor(x => x.refDoctorNameHid)
                                </div>
                                <span>Referring Doctor:</span>
                            </div>
                            <div class="refDoctor">
                                <div class="labelRefDoc">
                                    <span>
                                        @Html.LabelFor(m => m.refDoctorAddress, Model.refDoctorAddress, new { id = "refDoctorAddress" })
                                        @Html.HiddenFor(x => x.refDoctorAddressHid)
                                    </span>
                                </div>
                                <span>Address:</span>
                            </div>
                            <div class="refDoctor">
                                <div class="labelRefDoc">
                                    @Html.LabelFor(m => m.refDoctorBill, Model.refDoctorBill, new { id = "refDoctorBill" })
                                    @Html.HiddenFor(x => x.refDoctorBillHid)
                                </div>
                                <span>Bill # | CPSO</span>
                            </div>
                            <div id="consultantDoc">
                                <div class="consultantDoc-name">
                                    <div id="consDocNameID" class="headerLabel">
                                        <span id="consDocSpan">
                                            @Html.LabelFor(m => m.consDocNameLabel, Model.consDocNameLabel, new { id = "consDocNameLabel" })
                                        </span>
                                    </div>
                                    <span>Consulting Doctor:</span>
                                </div>
                                <div class="consultantDoc-name">
                                    <div id="consDocAddressID" class="headerLabel">
                                        <span>
                                            @Html.LabelFor(m => m.consDocAddress, Model.consDocAddress, new { id = "consDocAddress" })
                                        </span>
                                    </div>
                                    <span class="headerSpan">Address:</span>
                                </div>
                                <div class="consultantDoc-name">
                                    <div class="headerLabel">
                                        @Html.HiddenFor(x => x.consDocNameHidden)
                                        @Html.HiddenFor(x => x.consDocAddressHidden)
                                        @Html.HiddenFor(x => x.phone_faxHidden)
                                        @Html.HiddenFor(x => x.consDocId)
                                        @Html.HiddenFor(x => x.consDocBillN)
                                        @Html.HiddenFor(x => x.consDocFax)
                                        <span>
                                            @Html.LabelFor(m => m.phone_fax, Model.phone_fax, new { id = "phone_fax" })
                                        </span>
                                    </div>
                                    <span class="headerSpan_">Phone | Fax:</span>
                                </div>
                                <div class="refDoctor">
                                    <div class="labelRefDoc">
                                        @Html.LabelFor(m => m.ccDocNameLabel, Model.ccDocNameLabel, new { id = "ccDocNameLabel" })
                                        @Html.HiddenFor(x => x.ccDocNameHidden)
                                        @Html.HiddenFor(x => x.ccDocFax)
                                        @Html.HiddenFor(x => x.ccDocBillN)
                                    </div>
                                    <span>CC :</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="headerRight">
                        <div id="headerRight_">
                            <div class="headerRight-name">
                                <div class="headerRightLabel">
                                    <span>
                                        @Html.LabelFor(m => m.patientName, Model.patientName, new { id = "patientName" })
                                    </span>
                                </div>
                                <span class="headerRightSpan">Patient:</span>
                            </div>
                            <div class="headerRight-name">
                                <div id="patientAddressID" class="headerRightLabel">
                                    <span>
                                        @Html.LabelFor(m => m.patientAddress, Model.patientAddress, new { id = "patientAddress" })
                                    </span>
                                </div>
                                <span class="headerRightSpan" style="height: 40px;">Address:</span>
                            </div>
                            <div class="headerRight-name">
                                <div class="headerRightLabel">
                                    <span>
                                        @Html.LabelFor(m => m.patientHelthCard, Model.patientHelthCard, new { id = "patientHelthCard" })
                                        &nbsp;&nbsp;
                                        @Html.LabelFor(m => m.patientHelthCardVersionCode, Model.patientHelthCardVersionCode, new { id = "patientHelthCardVersionCode" })
                                    </span>
                                </div>
                                <span class="headerRightSpan  dobClass">Health Card No:</span>
                            </div>
                            <div class="headerRight-name">
                                <div class="headerRightLabel ">
                                    <span>
                                        @Html.LabelFor(m => m.patientDOB, Model.patientDOB, new { id = "patientDOB" })
                                    </span>
                                </div>
                                <span class="headerRightSpan dobClass">DOB:</span>
                            </div>
                            <div class="headerRight-name">
                                <div class="headerRightLabel">
                                    <span>
                                        @Html.LabelFor(m => m.patientPhone, Model.patientPhone, new { id = "patientPhone" })
                                    </span>
                                </div>
                                <span class="headerRightSpan  dobClass">Phone No:</span>
                            </div>
                            <div class="headerRight-name">
                                <div class="headerRightLabel">
                                    <span>
                                        @Html.LabelFor(m => m.patientSex, Model.patientSex, new { id = "patientSex" })
                                    </span>
                                </div>
                                <span class="headerRightSpan">Sex:</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="header-2">
                    <div id="docTypeDiv" class="clearRight">
                        @Html.DropDownListFor(model => model.SelectedDocType, Model.docTypeList, htmlAttributes: new { @class = "form-control selec_", tabindex = 2 })
                    </div>
                </div>
            </div>
            <div id="mainContent">
                <div id="menuBar">
                    <div id="servicesDiv">
                        @Html.DropDownListFor(model => model.SelectedService, Model.servicesList, htmlAttributes: new { @class = "form-control selec_", tabindex = 2 })
                    </div>
                    <div class="docDiv">
                        @Html.EditorFor(model => model.docList, new { htmlAttributes = new { @class = "form-control docListClass", tabindex = 9, placeholder = "Doctors", autocomplete = "off" } })
                    </div>
                    <div class="docDiv_">
                        @Html.EditorFor(model => model.ccDocList, new { htmlAttributes = new { @class = "form-control", tabindex = 9, placeholder = "CC:", autocomplete = "off" } })
                    </div>
                    <div id="reasonDiv" class="clearRight">
                        @Html.DropDownListFor(model => model.SelectedReason, Model.reasonList, htmlAttributes: new { @class = "form-control selec_ SelectedReasonClass", tabindex = 2 })
                    </div>
                </div>
                <div id="content">
                    <div id="reasonFor">
                        <div id="reasonHead">
                            <div id="reasonMenuBar">
                                @*<asp:Menu ID="Menu2" CssClass="menuStyle_" runat="server"></asp:Menu>*@
                            </div>
                            @*<span>Reasons For Referral</span>*@
                            <span>Reasons</span>
                        </div>
                        <div id="reasonText">
                            @Html.TextAreaFor(x => x.reasonsTextBox, 20, 15, new { maxlength = 3900, @class = "form-control text-box multy-line txtHeight reasonsTextBoxClass", tabindex = 44 })
                        </div>
                    </div>
                    <div id="medications" class="medClass">
                        <div id="medicationsLabel" class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            @Html.EditorFor(x => x.medicationsChB, new { htmlAttributes = new { } })
                            <label ID="Label55">Medications</label>
                        </div>
                    </div>
                    <div id="medicationsDiv" class="medDivClass showXScroll">
                        @for (int i = 0; i < Model.medications.Count; i++)
                        {
                            <div class="row" style="margin:0px;">
                                <div class="docDate0_c">
                                    -
                                </div>
                                <div class="mdedate1_c lblElipssis">
                                    @Html.LabelFor(m => m.medications[i], Model.medications[i], new { id = @Model.medications[i] })
                                </div>
                            </div>
                        }
                    </div>
                </div>
                <div id="letter" class="medClass">
                    <div id="Div4" class="checkLabel">

                    </div>
                    <div class="checkBoxDiv">
                        @Html.EditorFor(x => x.letterChB, new { htmlAttributes = new { } })
                        <label ID="Label49">Letters</label>
                    </div>
                </div>
                <div id="letterDiv" class="medDivClass showXScroll">
                    @if (Model.patientLetters != null)
                    {
                        for (int i = 0; i < Model.patientLetters.Count; i++)
                        {
                            <div class="row" style="margin:0px;">
                                <div class="docDate0_c">
                                    @Html.CheckBoxFor(m => m.patientLetters[i].isChecked, new { id = @Model.patientLetters[i].lettetTblId })
                                </div>
                                <div class="docDate1_c lblElipssis">
                                    @Html.LabelFor(m => m.patientLetters[i].dateStr, Model.patientLetters[i].dateStr, new { id = "l_" + @Model.patientLetters[i].lettetTblId })
                                </div>
                            </div>
                        }
                    }
                </div>
                <div id="Div3" class="medClass">
                    <div id="Div4" class="checkLabel">
                    </div>
                    <div class="checkBoxDiv">
                        @Html.EditorFor(x => x.internalTestChB, new { htmlAttributes = new { } })
                        <label ID="Label49">Internal Tests</label>
                    </div>
                </div>
                <div id="internalTestDiv" class="medDivClass showXScroll">
                    <div class="repItemIntTest_h">
                        <div class="appDate1_it">
                            <label>Visit Date</label>
                        </div>
                        <div class="appDate2_it">
                            <label>Report Date</label>
                        </div>
                        <div class="appDate3_it">
                            <label>Test Name</label>
                        </div>
                    </div>
                    @if (Model.internalTests != null)
                    {
                        for (int i = 0; i < Model.internalTests.Count; i++)
                        {
                            <div class="row" style="margin:0px;">
                                <div class="docDate0_c">
                                    @Html.CheckBoxFor(m => m.internalTests[i].isChecked, new { id = @Model.internalTests[i].appointmentId, @alt = ("" + Model.internalTests[i].testID + "|" + Model.internalTests[i].appointmentId) })
                                </div>
                                <div class="docDate2_c">
                                    @Html.LabelFor(m => m.internalTests[i].testDate, Model.internalTests[i].testDate, new { id = "v_" + @Model.internalTests[i].appointmentId })
                                </div>
                                <div class="docDate3_c">
                                    @Html.LabelFor(m => m.internalTests[i].requisitionDate, Model.internalTests[i].requisitionDate, new { id = "rep_" + @Model.internalTests[i].appointmentId })
                                </div>
                                <div class="docDate1_c lblElipssis">
                                    @Html.LabelFor(m => m.internalTests[i].testName, Model.internalTests[i].testName, new { id = "Name_" + @Model.internalTests[i].appointmentId })
                                </div>
                            </div>
                        }
                    }
                </div>
                <div id="Div3" class="medClass">
                    <div id="Div4" class="checkLabel">
                    </div>
                    <div class="checkBoxDiv">
                        @Html.EditorFor(x => x.alergiesChB, new { htmlAttributes = new { } })
                        <label ID="Label49">Allergies</label>
                    </div>
                </div>
                <div id="alergiesDiv" class="medDivClass showXScroll">
                    <table class="table alergiesTbl" id="alergiesId" name="alergiesList" style="width: 100%">
                        <thead>
                            <tr class="vertical-center headerColer">
                                <th width="2%"></th>
                                <th width="20%">ReactionType</th>
                                <th width="20%">Severity</th>
                                <th width="60%">Allergen</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && Model.alergies != null && Model.alergies.Count > 0)
                            {
                                for (int i = 0; i < Model.alergies.Count; i++)
                                {
                                    <tr class="p_editRow_r">
                                        <td class="pList">
                                            @Html.CheckBoxFor(m => m.alergies[i].isChecked, new { id = @Model.alergies[i].Id })
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.alergies[i].reactionType)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.alergies[i].severity)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.alergies[i].allergen)
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>





                    @*<div class="repItemIntTest_h">
                            <div class="docDate0_c">
                            </div>
                            <div class="appDate1_it">
                                <label>ReactionType</label>
                            </div>
                            <div class="appDate2_it">
                                <label>Severity</label>
                            </div>
                            <div class="appDate3_it">
                                <label>Allergen</label>
                            </div>
                        </div>
                        @if (Model.alergies != null)
                        {
                            for (int i = 0; i < Model.alergies.Count; i++)
                            {
                                <div class="row" style="margin:0px;">
                                    <div class="docDate0_c">
                                        @Html.CheckBoxFor(m => m.alergies[i].isChecked, new { id = @Model.alergies[i].Id })
                                    </div>
                                    <div class="docDate2_c">
                                        @Html.LabelFor(m => m.alergies[i].reactionType, Model.alergies[i].reactionType, new { id = "v_" + @Model.alergies[i].reactionType })
                                    </div>
                                    <div class="docDate3_c">
                                        @Html.LabelFor(m => m.alergies[i].severity, Model.alergies[i].severity, new { id = "rep_" + @Model.alergies[i].severity })
                                    </div>
                                    <div class="docDate1_c lblElipssis">
                                        @Html.LabelFor(m => m.alergies[i].allergen, Model.alergies[i].allergen, new { id = "Name_" + @Model.alergies[i].allergen })
                                    </div>
                                </div>
                            }
                        }*@
                </div>
                <div id="Div1" class="medClass">
                    <div id="Div2" class="checkLabel">
                    </div>
                    <div id="extDocPlug">
                        @Html.EditorFor(x => x.problemLChB, new { htmlAttributes = new { } })
                        <label ID="Label65">Problem List</label>
                    </div>
                </div>
                <div id="problemLDiv" class="medDivClass  showXScroll">
                    <table class="table consultTbl" id="problemLId" name="problemL" style="width: 100%">
                        <thead>
                            <tr class="vertical-center headerColer">
                                <th width="2%"></th>
                                <th width="15%">Onset Date</th>
                                <th width="10%">Res.Date</th>
                                <th width="10%">Sub.Date</th>
                                <th width="10%">Life Stage</th>
                                <th width="20%">Description</th>
                                <th width="8%">Diagnosis</th>
                                <th width="15%">Notes</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && Model.problemLists != null && Model.problemLists.Count > 0)
                            {
                                for (int i = 0; i < Model.problemLists.Count; i++)
                                {
                                    <tr class="p_editRow_r">
                                        <td class="pList">
                                            @Html.CheckBoxFor(m => m.problemLists[i].isChecked, new { id = @Model.problemLists[i].id })
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.problemLists[i].onsetDate)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.problemLists[i].resDate)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.problemLists[i].submitDateStr)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.problemLists[i].lifeStage)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.problemLists[i].description)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.problemLists[i].diagnosis)
                                        </td>
                                        <td class="pList is-active">
                                            @Html.DisplayFor(model => model.problemLists[i].notes)
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
                <div id="Div1" class="medClass">
                    <div id="Div2" class="checkLabel">
                    </div>
                    <div id="extDocPlug">
                        @Html.EditorFor(x => x.famHistChB, new { htmlAttributes = new { } })
                        <label ID="Label65">Family History</label>
                    </div>
                </div>
                <div id="famHistDiv" class="medDivClass  showXScroll">
                    <table class="table consultTbl" id="famHistId" name="famHist" style="width: 100%">
                        <thead>
                            <tr class="vertical-center headerColer">
                                <th width="2%"></th>
                                <th width="10%">Relationship</th>
                                <th width="10%">Age</th>
                                <th width="10%">Start Date</th>
                                <th width="10%">Life Stage</th>
                                <th width="15%">Description</th>
                                <th width="18%">Treatment</th>
                                <th width="15%">Notes</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && Model.familyHistorys != null && Model.familyHistorys.Count > 0)
                            {
                                for (int i = 0; i < Model.familyHistorys.Count; i++)
                                {
                                    <tr class="p_editRow_r">
                                        <td class="pList">
                                            @Html.CheckBoxFor(m => m.familyHistorys[i].isChecked, new { id = @Model.familyHistorys[i].id })
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.familyHistorys[i].Relationship)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.familyHistorys[i].Age)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.familyHistorys[i].startDate)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.familyHistorys[i].lifeStage)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.familyHistorys[i].description)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.familyHistorys[i].treatment)
                                        </td>
                                        <td class="pList is-active">
                                            @Html.DisplayFor(model => model.familyHistorys[i].notes)
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
                <div id="Div1" class="medClass">
                    <div id="Div2" class="checkLabel">
                    </div>
                    <div id="extDocPlug">
                        @Html.EditorFor(x => x.pastMedChB, new { htmlAttributes = new { } })
                        <label ID="Label65">Past Medical History</label>
                    </div>
                </div>
                <div id="pastMedDiv" class="medDivClass  showXScroll">
                    <table class="table consultTbl" id="pastMedId" name="pastMedList" style="width: 100%">
                        <thead>
                            <tr class="vertical-center headerColer">
                                <th width="2%"></th>
                                <th width="10%">Onset Date</th>
                                <th width="10%">Res.Date</th>
                                <th width="10%">Sub.Date</th>
                                <th width="10%">Life Stage</th>
                                <th width="15%">Description</th>
                                <th width="18%">Diagnosis</th>
                                <th width="15%">Notes</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && Model.pastHealths != null && Model.pastHealths.Count > 0)
                            {
                                for (int i = 0; i < Model.pastHealths.Count; i++)
                                {
                                    <tr class="p_editRow_r">
                                        <td class="pList">
                                            @Html.CheckBoxFor(m => m.pastHealths[i].isChecked, new { id = @Model.pastHealths[i].id })
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.pastHealths[i].onsetDate)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.pastHealths[i].resDate)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.pastHealths[i].submitDateStr)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.pastHealths[i].lifeStage)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.pastHealths[i].description)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.pastHealths[i].diagnosis)
                                        </td>
                                        <td class="pList is-active">
                                            @Html.DisplayFor(model => model.pastHealths[i].notes)
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
                <div id="Div1" class="medClass">
                    <div id="Div2" class="checkLabel">
                    </div>
                    <div id="extDocPlug">
                        @Html.EditorFor(x => x.alertChB, new { htmlAttributes = new { } })
                        <label ID="Label65">Alerts And Needs</label>
                    </div>
                </div>
                <div id="alertsDiv" class="medDivClass  showXScroll">
                    <table class="table consultTbl" id="alertsId" name="alertsList" style="width: 100%">
                        <thead>
                            <tr class="vertical-center headerColer">
                                <th width="2%"></th>
                                <th width="10%">Date Active</th>
                                <th width="10%">End Date</th>
                                <th width="38%">Description</th>
                                <th width="40%">Notes</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && Model.alertNeeds != null && Model.alertNeeds.Count > 0)
                            {
                                for (int i = 0; i < Model.alertNeeds.Count; i++)
                                {
                                    <tr class="p_editRow_r">
                                        <td class="pList">
                                            @Html.CheckBoxFor(m => m.alertNeeds[i].isChecked, new { id = @Model.alertNeeds[i].id })
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.alertNeeds[i].activDate)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.alertNeeds[i].endtDateStr)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.alertNeeds[i].description)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.alertNeeds[i].notes)
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
                <div id="Div1" class="medClass">
                    <div id="Div2" class="checkLabel">
                    </div>
                    <div id="extDocPlug">
                        @Html.EditorFor(x => x.immunChB, new { htmlAttributes = new { } })
                        <label ID="Label65">Immunization Summary</label>
                    </div>
                </div>
                <div id="immunDiv" class="medDivClass  showXScroll">
                    <table class="table consultTbl" id="immunId" name="immunList" style="width: 100%">
                        <thead>
                            <tr class="vertical-center headerColer">
                                <th width="2%"></th>
                                <th width="40%">Name</th>
                                <th width="20%">Status</th>
                                <th width="20%">Date</th>
                                <th width="40%">Administer Doctor</th>
                                @*<th width="2%"></th>
                                    <th width="20%">Name</th>
                                    <th width="10%">Indicator</th>
                                    <th width="13%">Date</th>
                                    <th width="13%">Refused Date</th>
                                    <th width="14%">Next Imm. Date</th>
                                    <th width="28%">Doctor</th>*@
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && Model.ImmunizationInfos != null && Model.ImmunizationInfos.Count > 0)
                            {
                                for (int i = 0; i < Model.ImmunizationInfos.Count; i++)
                                {
                                    <tr class="p_editRow_r">
                                        <td class="pList">
                                            @Html.CheckBoxFor(m => m.ImmunizationInfos[i].isChecked, new { id = @Model.ImmunizationInfos[i].immunizationId })
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.ImmunizationInfos[i].Name)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.ImmunizationInfos[i].ststus)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.ImmunizationInfos[i].ImmunizationStatusDate_str)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.ImmunizationInfos[i].Doctor)
                                        </td>
                                        @*<td class="pList">
                                                @Html.CheckBoxFor(m => m.ImmunizationInfos[i].isChecked, new { id = @Model.ImmunizationInfos[i].immunizationId })
                                            </td>
                                            <td class="pList">
                                                @Html.DisplayFor(model => model.ImmunizationInfos[i].Name)
                                            </td>
                                            <td class="pList">
                                                @Html.DisplayFor(model => model.ImmunizationInfos[i].Indicator)
                                            </td>
                                            <td class="pList">
                                                @Html.DisplayFor(model => model.ImmunizationInfos[i].ImmunizationDate_str)
                                            </td>
                                            <td class="pList">
                                                @Html.DisplayFor(model => model.ImmunizationInfos[i].RefusedDate_str)
                                            </td>
                                            <td class="pList">
                                                @Html.DisplayFor(model => model.ImmunizationInfos[i].NextImmunDate_str)
                                            </td>
                                            <td class="pList">
                                                @Html.DisplayFor(model => model.ImmunizationInfos[i].Doctor)
                                            </td>*@
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
                <div id="Div1" class="medClass">
                    <div id="Div2" class="checkLabel">
                    </div>
                    <div id="extDocPlug">
                        @Html.EditorFor(x => x.riskChB, new { htmlAttributes = new { } })
                        <label ID="Label65">Risk Factors</label>
                    </div>
                </div>
                <div id="riskFactorsDiv" class="medDivClass  showXScroll">
                    <table class="table consultTbl" id="riskList" name="riskList" style="width: 100%">
                        <thead>
                            <tr class="vertical-center headerColer">
                                <th width="2%"></th>
                                <th width="15%">Risk Factor</th>
                                <th width="10%">Age</th>
                                <th width="10%">St.Date</th>
                                <th width="10%">Life Stage</th>
                                <th width="10%">Sub.Date</th>
                                <th width="18%">Exposure Details</th>
                                <th width="15%">Notes</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && Model.riskFactors != null && Model.riskFactors.Count > 0)
                            {
                                for (int i = 0; i < Model.riskFactors.Count; i++)
                                {
                                    <tr class="p_editRow_r">
                                        <td class="pList">
                                            @Html.CheckBoxFor(m => m.riskFactors[i].isChecked, new { id = @Model.riskFactors[i].id })
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.riskFactors[i].riskfactor)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.riskFactors[i].onsetAge)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.riskFactors[i].startDate)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.riskFactors[i].lifeStage)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.riskFactors[i].submitDateStr)
                                        </td>
                                        <td class="pList">
                                            @Html.DisplayFor(model => model.riskFactors[i].exposureDetails)
                                        </td>
                                        <td class="pList is-active">
                                            @Html.DisplayFor(model => model.riskFactors[i].notes)
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
                <div id="Div1" class="medClass">
                    <div id="Div2" class="checkLabel">
                    </div>
                    <div id="extDocPlug">
                        @Html.EditorFor(x => x.extDocChB, new { htmlAttributes = new { } })
                        <label ID="Label65">External Documents</label>
                    </div>
                </div>
                <div id="extDocDiv" class="medDivClass  showXScroll">
                    <div class="docDiv_h">
                        <div class="docDate0_c">
                        </div>
                        <div class="docDate1_c">
                            <label>Document Name</label>
                        </div>
                        <div class="docDate2_c">
                            <label>Test Date</label>
                        </div>
                        <div class="docDate3_c">
                            <label>Document Type</label>
                        </div>
                    </div>
                    @if (Model.externalDocuments != null)
                    {
                        for (int i = 0; i < Model.externalDocuments.Count; i++)
                        {
                            <div class="row" style="margin:0px;">
                                <div class="docDate0_c">
                                    @Html.CheckBoxFor(m => m.externalDocuments[i].isChecked, new { id = @Model.externalDocuments[i].id + "|" + Model.externalDocuments[i].extDocType })
                                </div>
                                <div class="docDate1_c lblElipssis">
                                    @Html.LabelFor(m => m.externalDocuments[i].description, Model.externalDocuments[i].description, new { id = "doc_name" })
                                </div>
                                <div class="docDate2_c">
                                    @Html.LabelFor(m => m.externalDocuments[i].date, Model.externalDocuments[i].date, new { id = "doc_date" })
                                </div>
                                <div class="docDate3_c">
                                    @Html.LabelFor(m => m.externalDocuments[i].type, Model.externalDocuments[i].type, new { id = "doc_Type" })
                                </div>
                            </div>
                        }
                    }
                </div>
                <div class="medClass">
                    <input id="hiddenDocList" type="hidden" />
                    <div id="bookingDiv" class="checkLabel">
                        <span>Appointment Date & Time:</span>
                        @Html.EditorFor(model => model.datepicker, new { htmlAttributes = new { @class = "form-control", tabindex = 9, autocomplete = "off" } })
                        @Html.DropDownList("timepickerId", new SelectList(Model.appTimeList, "time..."), "time...", new { htmlAttributes = new { @class = "form-control timepicker", autocomplete = "off" } })
                    </div>
                    <div class="checkBoxDiv">
                        @Html.EditorFor(x => x.bookingChB, new { htmlAttributes = new { } })
                        <label ID="Label55">Patient Will Book</label>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer">
            <div style="padding-top: 6px;">
                <div class="col-sm-4">
                    <button type="button" ID="submit_form" class="subButton"
                            name="Submit" value="Submit" style="float: right; width: 100px;">
                        Save
                    </button>
                </div>
                <div class="col-sm-8">
                    <input name="consultationFaxTo" type="text" id="consultationFaxTo" style="width: 160px; margin-top: 3px; margin-left: 100px; background-color: white;" placeholder="Fax To" />
                    <button type="button" ID="submit_fax" class="subButton"
                            name="SubmitFax" value="SubmitFax" style="width: 100px;">
                        Save & Fax
                    </button>
                </div>
            </div>
        </div>
        <div id="footer_" class="clearRight">
            @Html.LabelFor(m => m.message, Model.message, new { id = "message" })
        </div>
        @Html.HiddenFor(m => m.patientRecordId)

        @Html.HiddenFor(m => m.m_requisitionPatientId)
        @Html.HiddenFor(m => m.m_requisitionTypeId)
        @Html.HiddenFor(m => m.m_byAppointmentId)
        @Html.HiddenFor(m => m.m_practiceDoctorId)
        @Html.HiddenFor(m => m.m_nextAppointment)
        @Html.HiddenFor(m => m.m_nextAppointmentDoctor)
        @Html.HiddenFor(m => m.m_nextAppointmentOffice)
        @Html.HiddenFor(m => m.m_comment)
    }
</body>
