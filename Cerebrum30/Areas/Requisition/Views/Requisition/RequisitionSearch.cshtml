﻿@model Cerebrum.ViewModels.Requisition.RequisitionSearchResponse
@{
    ViewBag.Title = "To Do/Open work items";
    ViewBag.ModuleName = "To Do/Open work items";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";

}

<style type="text/css">
    .transparent {
        opacity: 0;
    }

    .inline-block {
        display: inline-block;
    }

    #requisition-div .dropdown-menu .sub-menu {
        left: 100%;
        position: absolute;
        top: 0;
        display: none;
        margin-top: -1px;
    }

    #requisition-div .dropdown:hover ul, #requisition-div .dropdown:hover ul li:hover ul, #requisition-div .dropdown:hover ul li:hover ul li:hover ul {
        display: block;
    }

    #requisition-div .dropdown:hover ul li ul, #requisition-div .dropdown:hover ul li:hover ul li ul {
        display: none;
    }

    #requisitionComment {
        width: 100%;
        resize: vertical;
    }

    .ui-dialog, .ui-widget, .ui-widget-content {
        background: white !important;
    }

    .btn-popover-container {
        display: inline-block;
    }

    .btn-popover-content{
        padding-top:0;
        padding-bottom:0;
        margin-top:0;
        margin-bottom:0;
    }

    .btn-popover-container .btn-popover-title, .btn-popover-container .btn-popover-content {
        display: none;
    }

    .popover-pointer {
        cursor:pointer;
    }
</style>
<link rel="stylesheet" href="Areas/Schedule/Content/shared-styles.css" />
<link rel="stylesheet" href="Areas/Schedule/Content/schedule.css" />
<link rel="stylesheet" href="Areas/Schedule/Content/appointments-modal.css" />

<script type="text/javascript" src="~/Areas/Requisition/Scripts/RequisitionSearch.js"></script>
<script type="text/javascript" src="~/Areas/Schedule/Scripts/appointments.js"></script>

<script type="text/javascript">
    var internalTestStatus = [];
    var requisitionStatus = [];
    @if (Model != null && Model.internalTestStatus != null) {
        foreach (var d in Model.internalTestStatus) {
            @:internalTestStatus.push(["@d.Item1", "@d.Item2", "@d.Item3"]);
        }
    }
    @if (Model != null && Model.requisitionStatus != null) {
        @:requisitionStatus = @Html.Raw(Json.Serialize(Model.requisitionStatus));
    }
</script>

@* preload appointment create view  *@
<script type="text/javascript">
        $(function () {
            loadAppScheduleModal(@Model.practiceId, @Model.officeId);
        });
</script>

<div class="col-sm-12" style="padding-top: 8px; margin-top: 8px;">
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="row col-sm-3">
            @*<div class="col-sm-12" style="padding: 0; margin-bottom: 16px; color: royalblue; font-size: 20px; font-weight: bold;">
                    Open work items
                </div>*@
            <div class="row" style="padding-top: 32px; margin-top: 32px;">
                <div class="col-sm-12" style="padding: 0; margin: 0;">
                    <div class="form-group col-sm-6">
                        <label>Start</label>
                        <input type="text" class="form-control" id="dateStart">
                    </div>
                    <div class="form-group col-sm-6">
                        <label>End</label>
                        <input type="text" class="form-control" id="dateEnd">
                    </div>
                </div>
            </div>
            <div class="row" style="padding-top: 8px;">
                <div class="col-sm-12" style="padding: 0; margin: 0;">
                    <div class="col-sm-6">
                        @Html.DropDownList("practiceDoctorIds", new SelectList(Model.doctors, "value", "text"), "All the Doctors", htmlAttributes: new { @class = "form-control", multiple = "true", @style = "height: 120px;" })
                    </div>
                    <div class="col-sm-6">
                        @if (Model.offices.Count() == 1)
                        {
                            @Html.DropDownList("officeIds", new SelectList(Model.offices, "value", "text"), htmlAttributes: new { @class = "form-control", multiple = "true", @style = "height: 120px;" })
                        }
                        else
                        {
                            @Html.DropDownList("officeIds", new SelectList(Model.offices, "value", "text"), "All the Offices", htmlAttributes: new { @class = "form-control", multiple = "true", @style = "height: 120px;" })
                        }
                    </div>
                </div>
            </div>
            <div class="row" style="padding-top: 16px;">
                <div class="col-sm-12" style="padding: 0; margin: 0;">
                    <div class="col-sm-6">
                        @Html.DropDownList("requisitionTypeIds", new SelectList(Model.requisitionTypes, "value", "text"), "All Requisition Types", htmlAttributes: new { @class = "form-control", multiple = "true", @style = "height: 120px;" })
                    </div>
                    <div class="col-sm-6">
                        @Html.DropDownList("requisitionStatusIds", new SelectList(Model.requisitionStatus.OrderBy(status => status.SortOrder).ToList(), "id", "name"), "All Status", htmlAttributes: new { @class = "form-control", multiple = "true", @style = "height: 120px;" })
                    </div>
                </div>
            </div>
            <div class="row" style="padding-top: 32px;">
                <div class="col-sm-12" style="padding: 0; margin: 0;">
                    <div class="form-group col-sm-4 col-sm-offset-8 text-right">
                        <input type="button" id="buttonSearch" name="buttonSearch" value="Search" class="btn-lg btn-info" style="margin-top: 12px;" />
                    </div>
                </div>
            </div>
        </div>
        <div class="row col-sm-9" style="padding-left: 64px;">
            <div class="row col-sm-12" id="requisition-div" name="requisition-div">
                <table id="requisitionList" name="requisitionList" class='table' style="width: 100%;">
                    <thead>
                        <tr> @*class="success vertical-center"*@
                            <th>Patient</th>
                            <th><a href="" onclick="sortBy('appointment'); return false;">Appointment<span id="imageAppointment" name="imageAppointment" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th>Requisition</th>
                            <th><a href="" onclick="sortBy('doctor'); return false;">Doctor<span id="imageDoctor" name="imageDoctor" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                            <th><a href="" onclick="sortBy('office'); return false;">Office<span id="imageOffice" name="imageOffice" class="glyphicon glyphicon-arrow-up transparent"></span></a></th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="row col-sm-12 text-right">
                <ul class="pagination" id="pagination" name="pagination" style="margin: 0;"></ul>
            </div>
            <div id="patientMenuTemplate" name="patientMenuTemplate" style="display:none;">
                <div class="btn-popover-container">
                    <span class="popover-btn popover-pointer cb-text16 text-primary">PatientMenuTemplatePatientName</span>
                    <div class="btn-popover-title">
                        <span class="default-text-color">e-Chart</span>
                    </div>
                    <div class="btn-popover-content">
                        @(await Html.PartialAsync("GetPatientMenu", new { area = "", Id = -123456 }))
                    </div>
                </div>
            </div>
            <div id="requisitionFormDialog" name="requisitionFormDialog" style="display:none;"></div>
            <div id="requisitionStatusDialog" name="requisitionStatusDialog" style="display:none; margin-top: 8px; ">
                @foreach (var requisitionStatus in Model.requisitionStatus.OrderBy(status => status.SortOrder).ToList())
                {
                    <div class="row" style="padding-top: 8px;">
                        <div class="col-sm-2 text-right"><input type="radio" id="requisitionStatus" name="requisitionStatus" value="@requisitionStatus.Id"></div>
                        <div class="col-sm-6">@requisitionStatus.Name</div>
                        <div class="col-sm-2 text-left" style="background-color: @requisitionStatus.Color; height: 16px; width: 8px;"></div>
                    </div>
                }
                <div class="row" style="padding-top: 18px;">
                    <div class="col-sm-2 text-right"></div>
                    <div class="col-sm-4">Test Date</div>
                    <div class="col-sm-6 text-left"><input type="text" id="requisitionTestDate" name="requisitionTestDate" value="" style="width: 128px;"></div>
                </div>
                <div class="row" style="padding: 24px 12px 0 12px;">
                    <div class="col-sm-4">Comment</div>
                </div>
                <div class="row" style="padding: 4px 4px 0 12px;">
                    <div class="col-sm-12">
                        <textarea rows="2" maxlength="3000" id="requisitionComment" name="requisitionComment"></textarea>
                    </div>
                </div>
            </div>
            <div id="internalTestStatusDialog" name="internalTestStatusDialog" style="display:none; margin-top: 8px; ">
                @foreach (var internalTestStatus in Model.internalTestStatus)
                {
                    <div class="row" style="padding-top: 8px;">
                        <div class="col-sm-2 text-right"><input type="radio" id="internalTestStatus" name="internalTestStatus" value="@internalTestStatus.Item1"></div>
                        <div class="col-sm-8">@internalTestStatus.Item2</div>
                        <div class="col-sm-2 text-left" style="background-color: @internalTestStatus.Item3; height: 16px; width: 8px;"></div>
                    </div>
                }
            </div>
            <div class="modal fade in col-sm-12" id="modal-template-id">
                <div class="modal-dialog col-sm-12" id="modal-template-content-id">
                    <div class="modal-content col-sm-12" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                        <div class="modal-header no-borders col-sm-12">
                            <div class="col-sm-10" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                                <h4 class="modal-title" id="modal-template-title-id"></h4>
                            </div>
                            <div class="col-sm-2" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        </div>
                        <div class="modal-body col-sm-12" id="modal-template-body-id" style="overflow-y: auto;"></div>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>