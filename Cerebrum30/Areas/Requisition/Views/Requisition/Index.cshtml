@model Cerebrum.ViewModels.Requisition.IndexResponse
@using Cerebrum30.Helpers;
@{
    ViewBag.Title = "Requisition";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss {
    <style type="text/css">
        #requisition-container {
            padding-top: 15px;
        }

        #requisition-container input, #requisition-container select, #requisition-container textarea {
            max-width: 100%;
        }

        #requisition-container .form-group {
            margin-bottom: 6px;
        }

        #requisition-container .inline-block {
            display: inline-block;
        }

        #requisition-container .info {
            background-color: #d9edf7;
        }

        #requisition-container .ui-dialog, #requisition-container .ui-widget, #requisition-container .ui-widget-content {
            background: white !important;
        }

        #requisition-container .noclose #requisition-container .ui-dialog-titlebar-close {
            display: none;
        }

        #requisition-container .ui-dialog-titlebar {
            background-color: steelblue;
            color: yellow;
        }

        #requisition-container .dropdown-menu .sub-menu {
            left: 100%;
            position: absolute;
            top: 0;
            display: none;
            margin-top: -1px;
        }

        #requisition-container .dropdown:hover ul, #requisition-container .dropdown:hover ul li:hover ul, #requisition-container .dropdown:hover ul li:hover ul li:hover ul {
            display: block;
        }

        #requisition-container .dropdown:hover ul li ul, #requisition-container .dropdown:hover ul li:hover ul li ul {
            display: none;
        }

        #requisitionComment {
            width: 100%;
            resize: vertical;
        }
    </style>
    <link rel="stylesheet" href="Areas/Schedule/Content/shared-styles.css" />
    <link rel="stylesheet" href="Areas/Schedule/Content/schedule.css" />
    <link rel="stylesheet" href="Areas/Schedule/Content/appointments-modal.css" />
}

@section scripts {
    <script type="text/javascript" src="~/Areas/Requisition/Scripts/Index.js"></script>
    <script type="text/javascript" src="~/Areas/Schedule/Scripts/sharedFunctions.js"></script>
    <script type="text/javascript" src="~/Areas/Schedule/Scripts/appointments.js"></script>
    <script type="text/javascript">
        var appointmentStatusIdOrdersToTranscribe = "@((int)AwareMD.Cerebrum.Shared.Enums.AppointmentStatus.OrdersToTranscribe)";
        var patientRecordId = "@Model.patientRecordId";
        var byAppointmentId = "@Model.appointmentId";
        var returnUrl = "@Model.returnUrl";
        var currentRequisitionIndex = "@Model.currentRequisitionIndex";
        var requisitionTypes = @Html.Raw(Json.Serialize(Model.requisitions));
        var internalTestStatus = [];
        var practiceId = "@Model.practiceId";

        var practiceDoctorId = "0";
        var practiceDoctorIdMRP = "@Model.practiceDoctorIdMRP";
        var officeId = "0";
        @if (Model != null && Model.requisitionDetail != null) {
            @:officeId = "@Model.requisitionDetail.nextAppointmentOffice";
        }
        @if (Model != null && Model.internalTestStatus != null) {
            foreach (var d in Model.internalTestStatus) {
                @:internalTestStatus.push(["@d.Item1", "@d.Item2", "@d.Item3"]);
            }
        }
        var requisitionStatus = [];
        @if (Model != null && Model.requisitionStatus != null) {
            @:requisitionStatus = @Html.Raw(Json.Serialize(Model.requisitionStatus));
        }
    </script>

    @* preload appointment create view  *@
    <script type="text/javascript">
        $(function () {
            loadAppScheduleModal(@Model.practiceId, @Model.officeId);
        });
    </script>
}

<div id="requisition-container">
    <div class="container body-content" style="margin-left: -15px; margin-right: -15px;">
        @if (string.IsNullOrEmpty(Model.errorMessage))
        {
            <div class="row" style="margin-bottom: 12px;">
                <div class="col-md-4">
                    Patient:
                    <div class="btn-popover-container">
                        <span class="popover-btn popover-pointer cb-text16 text-primary">@Model.patientName</span>
                        <div class="btn-popover-title">
                            <span class="default-text-color">e-Chart</span>
                        </div>
                        <div class="btn-popover-content">
                            @(await Html.PartialAsync("GetPatientMenu", new { area = "", Id = Model.patientRecordId }))
                        </div>
                    </div>
                </div>
                <div class="col-md-4">HIN ID: @Model.ohipId</div>
                <div class="col-md-4 text-right">
                    @{
                        string url = Html.GetReferrerUrl();
                        if (!string.IsNullOrEmpty(url))
                        {
                            <input type="button" value="Save & Close" class="btn btn-info" onclick="return saveandCloseClicked('@url', '@Model.closePage')" />
                            <input type="button" value="End Visit" class="btn btn-info" onclick="return endVisitClicked(@Model.appointmentId, '@url', '@Model.closePage'); " />
                        }
                    }
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading" id="requisition_Title" name="requisition_Title">&nbsp;</div>
                        <div class="panel-body">
                            <form class="form-horizontal">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Doctor
                                    </label>
                                    <div class="col-sm-4">
                                        @Html.DropDownList("requisition_PracticeDoctorId", new SelectList(Model.doctors, "value", "text", Model.requisitionDetail.practiceDoctorId), "All", htmlAttributes: new { @class = "form-control" })
                                    </div>
                                    <div class="col-sm-5">
                                        <label class="col-sm-6 control-label" style="text-align: left;" id="testOrdered" name="testOrdered"></label>
                                        <div class="col-sm-6 text-right dropdown" style="padding-right: 0;">
                                            <input type="button" id="buttonNewRequisition" name="buttonNewRequisition" value="Requisition" class="btn btn-info dropdown-toggle" data-toggle="dropdown" style="z-index: 100;" />
                                            <div id="menuNewRequisitionDiv" name="menuNewRequisitionDiv">
                                                <ul class="dropdown-menu text-left" id="menuNewRequisition" name="menuNewRequisition" style="left: 96px; top: 32px;"></ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Office
                                    </label>
                                    <div class="col-sm-4">
                                        @if (Model.offices.Count() == 1)
                                        {
                                            @Html.DropDownList("requisition_OfficeId", new SelectList(Model.offices, "value", "text", Model.offices[0].value), htmlAttributes: new { @class = "form-control" })
                                        }
                                        else
                                        {
                                            @Html.DropDownList("requisition_OfficeId", new SelectList(Model.offices, "value", "text", Model.requisitionDetail.nextAppointmentOffice), "All", htmlAttributes: new { @class = "form-control" })
                                        }
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 text-right">
                                        Date
                                    </label>
                                    <label class="col-sm-6 text-left" id="requisition_AppointmentDate" name="requisition_AppointmentDate">@Model.requisitionDetail.appointmentDate</label>
                                    <div class="col-sm-3 text-right">
                                        @*<input type="button" id="buttonAppoint" name="buttonAppoint" value="Appoint" class="btn btn-info" />*@
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">
                                        Comments
                                    </label>
                                    <div class="col-sm-9">
                                        <textarea id="requisition_Comment" name="requisition_Comment" required rows="3" class="form-control" style="height: auto;">@Model.requisitionDetail.comment</textarea>
                                    </div>
                                </div>
                                <div class="text-right">
                                    @{
                                        string buttonAddUpdateText = "Add";
                                        if (Model.requisitionDetail.requisitionPatientId > 0)
                                        {
                                            buttonAddUpdateText = "Update";
                                        }
                                    }
                                    <input type="button" id="buttonAddUpdate" name="buttonAddUpdate" value="@buttonAddUpdateText" class="btn btn-info" style="margin-top: 12px;" />
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <br />
            <div class="row">
                <div class="col-md-12" style="height: 400px; overflow-y: auto;">
                    <table class="table" id="requisitionList" name="requisitionList" style="font-size: 14px;">
                        <thead>
                            <tr>@*class="success vertical-center"*@
                                <th>date</th>
                                <th>doctor</th>
                                <th>tests done</th>
                                <th>status</th>
                                <th>tests ordered</th>
                                <th>billing code</th>
                                <th>Dx code</th>
                            </tr>
                        </thead>
                        <tbody>
                            @{
                                for (int n = 0; n < Model.requisitionList.Count; n++)
                                {
                                    var requisition = Model.requisitionList[n];
                                    string trClass = string.Empty;
                                    if (n % 2 == 1)
                                    {
                                        trClass = "active";
                                    }
                                    string dateDisplayed = requisition.dateDisplayed;
                                    if (!string.IsNullOrEmpty(requisition.noVisit))
                                    {
                                        dateDisplayed += " (" + requisition.noVisit + ")";
                                    }
                                    <tr class="clickable-row @trClass">
                                        <td>@Html.ActionLink(dateDisplayed, "index", new { area = "Schedule", controller = "daysheet", OfficeId = Model.officeId, ScheduleTypeId = 3, Date = requisition.dateDisplayed })</td>
                                        <td>@requisition.doctor</td>
                                        <td>
                                            @if (requisition.testDone != null)
                                            {
                                                string color;
                                                string status;
                                                string internalTestUrl = string.Empty;
                                                string internalTestUrlDesc = string.Empty;
                                                string letterUrl = string.Empty;
                                                foreach (var testDone in requisition.testDone)
                                                {
                                                    if (testDone.appointmentId > 0)
                                                    {
                                                        bool isVP = testDone.testName.ToLower() == "vp";
                                                        internalTestUrl = isVP ? Url.Action("Index", "Visit", new { area = "VP", AppointmentTestID = testDone.appointmentTestId }) :
                                                                                 Url.Action("Index", "Measurement", new { area = "Measurements", AppointmentID = testDone.appointmentId, AppointmentTestID = testDone.appointmentTestId, TestID = testDone.testId, officeId = Model.officeId, date = requisition.dateDisplayed });
                                                        internalTestUrlDesc = isVP ? "View VP" : "View Worksheet";

                                                        color = getInternalTestStatusColor(testDone.status);
                                                        status = getInternalTestStatusName(testDone.status);
                                                        letterUrl = "";
                                                        if (status == "Report Completed")
                                                        {
                                                            if (isVP)
                                                            {
                                                                letterUrl = Url.Action("VPReportLetter", "Reports", new { area = "Documents", appointmentId = testDone.appointmentId, appointmentTestId = testDone.appointmentTestId });
                                                            }
                                                            else
                                                            {
                                                                letterUrl = Url.Action("ViewReport", "Reports", new { area = "Documents", appointmentId = testDone.appointmentId, testId = testDone.testId, appointmentTestId = testDone.appointmentTestId, patientId = Model.patientRecordId });
                                                            }
                                                        }

                                                        <div class="btn-popover-container">
                                                            <span class="popover-btn popover-pointer cb-text16 text-primary"><span style="color: white; background-color: @color;">&nbsp;@testDone.testName&nbsp;</span></span>
                                                            <div class="btn-popover-content">
                                                                <ul class="ul-patient-menu">
                                                                    <li><a class="" href="@internalTestUrl" target="_blank">@internalTestUrlDesc</a></li>
                                                                    <li>
                                                                        @{
                                                                            if (string.IsNullOrEmpty(letterUrl))
                                                                            {
                                                                                <span title="@status">View Letter</span>
                                                                            }
                                                                            else
                                                                            {
                                                                                <a class="btn-preview"  href="@letterUrl" target="_blank">View Letter</a>
                                                                            }
                                                                        }
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        color = getRequisitionStatusColor(testDone.status);
                                                        <span style="color: @color">@testDone.testName &nbsp;&nbsp;</span>
                                                    }
                                                }
                                            }
                                        </td>
                                        <td>@requisition.appointmentStatusDisplayed</td>
                                        <td>
                                            @if (requisition.testOrdered != null)
                                            {
                                                foreach (var testOrdered in requisition.testOrdered)
                                                {
                                                    string data1 = string.Empty;
                                                    string data2 = string.Empty;
                                                    string additionInformation = string.Empty;

                                                    if (testOrdered.requisitionItem.Count > 0)
                                                    {
                                                        data1 = " {";
                                                        data2 = "} ";
                                                    }
                                                    if (testOrdered.source.ToLower() == "appointment")
                                                    {
                                                        foreach (var requisitionItem in testOrdered.requisitionItem)
                                                        {
                                                            <div class="dropdown inline-block">
                                                                <a href="#" onclick="return false;" onmouseover="return buildRequisitionMenu(this, '2', '@requisition.appointmentId', '@testOrdered.source', '@testOrdered.type.Replace("'","&quot;")', '@testOrdered.id', '@requisitionItem.status', '@testOrdered.testTime', '@requisitionItem.appointmentId', '@requisitionItem.requisitionId', '@requisitionItem.testId');">
                                                                    <span style="color: white; background-color: @getInternalTestStatusColor(requisitionItem.status)">@(requisitionItem.testName + " ")</span>
                                                                </a>
                                                            </div>
                                                        }
                                                    }
                                                    else if (testOrdered.type.ToLower() == "internal")
                                                    {
                                                        <div class="dropdown inline-block">
                                                            <a href="#" onclick="return false;" onmouseover="return buildRequisitionMenu(this, '1', '@requisition.appointmentId', '@testOrdered.source', '@testOrdered.type.Replace("'","&quot;")', '@testOrdered.id', '@testOrdered.status', '@testOrdered.testTime', '0', '0', '0');">
                                                                <span>@testOrdered.type</span>
                                                            </a>
                                                        </div>
                                                        @data1
                                                        foreach (var requisitionItem in testOrdered.requisitionItem)
                                                        {
                                                            <div class="dropdown inline-block">
                                                                <a href="#" onclick="return false;" data-patient-record-id="@Model.patientRecordId" data-requisition-id="@requisitionItem.requisitionId" data-test-id="@requisitionItem.testId" onmouseover="return buildInternalRequisitionMenu(this);">
                                                                    <span style="color: white; background-color: @getInternalTestStatusColor(requisitionItem.status)">&nbsp; @(requisitionItem.testName + "(" + requisitionItem.requisitionTimeDescription + ")")&nbsp;</span>
                                                                </a>
                                                            </div>
                                                        }
                                                        if (!string.IsNullOrEmpty(testOrdered.template))
                                                        {
                                                            @(" ," + testOrdered.template)
                                                        }
                                                        @data2@:&nbsp;
                                                    }
                                                    else if (testOrdered.type.ToLower() == "external")
                                                    {
                                                        var extTest = testOrdered.requisitionItem.Any() ? testOrdered.requisitionItem.First() : null;
                                                        int extTestId = extTest != null ? extTest.testId : -1;
                                                        int appId = requisition.appointmentId;

                                                        <div class="dropdown inline-block">
                                                            <a data-app-id="@appId" data-external-test-id="@extTestId" href="#" onclick="return false;" onmouseover="return buildRequisitionMenu(this, '0', '@requisition.appointmentId', '@testOrdered.source', '@testOrdered.type.Replace("'","&quot;")', '@testOrdered.id', '@testOrdered.status', '@testOrdered.testTime', '0', '@testOrdered.id', '0');">
                                                                <span style="color: @getRequisitionStatusColor(testOrdered.status)">
                                                                    @foreach (var requisitionItem in testOrdered.requisitionItem)
                                                                    {
                                                                        additionInformation = string.Empty;
                                                                        if (testOrdered.statusDescription.Trim().ToLower() == "ordered")
                                                                        {
                                                                            if (!string.IsNullOrEmpty(requisitionItem.requisitionTimeDescription))
                                                                            {
                                                                                additionInformation = requisitionItem.requisitionTimeDescription;
                                                                            }
                                                                        }
                                                                        else
                                                                        {
                                                                            if (!string.IsNullOrEmpty(testOrdered.testTime))
                                                                            {
                                                                                additionInformation = testOrdered.testTime;
                                                                            }
                                                                        }
                                                                        if (!string.IsNullOrEmpty(testOrdered.template))
                                                                        {
                                                                            additionInformation = string.IsNullOrEmpty(additionInformation) ? testOrdered.template : additionInformation + ", " + testOrdered.template;
                                                                        }

                                                                        @(requisitionItem.testName + (string.IsNullOrEmpty(additionInformation) ? string.Empty : " (" + additionInformation + ")"))
                                                                    }
                                                                </span>
                                                            </a>&nbsp;
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <div class="dropdown inline-block">
                                                            <a href="#" onclick="return false;" onmouseover="return buildRequisitionMenu(this, '0', '@requisition.appointmentId', '@testOrdered.source', '@testOrdered.type.Replace("'","&quot;")', '@testOrdered.id', '@testOrdered.status', '@testOrdered.testTime', '0', '@testOrdered.id', '0');">
                                                                <span style="color: @getRequisitionStatusColor(testOrdered.status)">
                                                                    @testOrdered.type @data1
                                                                    @foreach (var requisitionItem in testOrdered.requisitionItem)
                                                                    {
                                                                        @(requisitionItem.testName + " ")
                                                                    }
                                                                    @data2
                                                                    
                                                                    @{additionInformation = string.Empty;
                                                                        if (!string.IsNullOrEmpty(testOrdered.testTime))
                                                                        {
                                                                            additionInformation = testOrdered.testTime;
                                                                        }
                                                                        else if (!string.IsNullOrEmpty(testOrdered.requisitionTime))
                                                                        {
                                                                            additionInformation = testOrdered.requisitionTime;
                                                                        }
                                                                        if (!string.IsNullOrEmpty(testOrdered.template))
                                                                        {
                                                                            additionInformation = string.IsNullOrEmpty(additionInformation) ? testOrdered.template : additionInformation + ", " + testOrdered.template;
                                                                        }
                                                                    }
                                                                    @(string.IsNullOrEmpty(additionInformation) ? string.Empty : " (" + additionInformation + ")")
                                                                </span>
                                                            </a>&nbsp;
                                                        </div>
                                                    }
                                                }
                                            }
                                            @if (!string.IsNullOrEmpty(requisition.comment))
                                            {
                                                @Html.Raw("<br />Comments: " + requisition.comment)
                                            }
                                            <input type="hidden" id='@("appointmentId" + @n)' name='@("appointmentId" + @n)' value="@requisition.appointmentId">
                                            <input type="hidden" id='@("practiceDoctorId" + @n)' name='@("practiceDoctorId" + @n)' value="@requisition.practiceDoctorId">
                                            <input type="hidden" id='@("requisitionPatientId" + @n)' name='@("requisitionPatientId" + @n)' value="@requisition.requisitionPatientId">
                                        </td>
                                        <td>@requisition.consultCode</td>
                                        <td>@requisition.diagnoseCode</td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
            </div>
            <div id="imageLoading" name="imageLoading" style="position: fixed; left: 30%; top: 40%; display: none;">
                <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
            </div>
        }
        else
        {
            <div class="text-center text-danger">
                <br /><br /><br /><br /><br /><br /><br /><br />
                <h1>@Model.errorMessage</h1>
                <br /><br /><br /><br /><br /><br /><br /><br />
            </div>
        }
    </div>
    <div id="requisitionFormDialog" name="requisitionFormDialog" style="display:none;"></div>
    <div id="requisitionStatusDialog" name="requisitionStatusDialog" style="display:none; margin-top: 8px; ">
        @foreach (var requisitionStatus in Model.requisitionStatus.OrderBy(status => status.SortOrder).ToList())
            {
            <div class="row" style="padding-top: 8px;">
                <div class="col-sm-2 text-right"><input type="radio" id="requisitionStatus" name="requisitionStatus" value="@requisitionStatus.Id"></div>
                <div class="col-sm-6">@requisitionStatus.Name</div>
                <div class="col-sm-2 text-left" style="background-color: @requisitionStatus.Color; height: 16px; width: 8px;"></div>
            </div>
        }
        <div class="row" style="padding-top: 18px;">
            <div class="col-sm-2 text-right"></div>
            <div class="col-sm-4">Test Date</div>
            <div class="col-sm-6 text-left"><input type="text" id="requisitionTestDate" name="requisitionTestDate" value="" style="width: 128px;"></div>
        </div>
        <div class="row" style="padding: 24px 12px 0 12px;">
            <div class="col-sm-4">Comment</div>
        </div>
        <div class="row" style="padding: 4px 4px 0 12px;">
            <div class="col-sm-12">
                <textarea rows="2" maxlength="3000" id="requisitionComment" name="requisitionComment"></textarea>
            </div>
        </div>
    </div>
    <div id="internalTestStatusDialog" name="internalTestStatusDialog" style="display:none; margin-top: 8px; ">
        @foreach (var internalTestStatus in Model.internalTestStatus)
        {
            <div class="row" style="padding-top: 8px;">
                <div class="col-sm-2 text-right"><input type="radio" id="internalTestStatus" name="internalTestStatus" value="@internalTestStatus.Item1"></div>
                <div class="col-sm-8">@internalTestStatus.Item2</div>
                <div class="col-sm-2 text-left" style="background-color: @internalTestStatus.Item3; height: 16px; width: 8px;"></div>
            </div>
        }
    </div>
    <div id="faxDialog" name="faxDialog" class="text-center" style="display:none; margin-top: 8px; ">
        <br />
        <input name="requisitionFaxTo" type="text" id="requisitionFaxTo" class="requisitionFaxTo" style="width: 160px; margin-top: 3px; background-color: white;" placeholder="Fax To" />
    </div>
    <div class="modal fade in col-sm-12" id="modal-template-id">
        <div class="modal-dialog col-sm-12" id="modal-template-content-id" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
            <div class="modal-content col-sm-12" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                <div class="modal-header no-borders col-sm-12">
                    <div class="col-sm-10" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                        <h4 class="modal-title" id="modal-template-title-id"></h4>
                    </div>
                    <div class="col-sm-2" style="padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0;">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                </div>
                <div class="modal-body col-sm-12" id="modal-template-body-id" style="overflow-y: auto;"></div>
            </div>
        </div>
    </div>
    <div class="modal fade in col-sm-12" id="test-menu-template-id">
        <div class="btn-popover-container">
            <span class="popover-btn popover-pointer cb-text16 text-primary"></span>
            <div class="btn-popover-title">
                <span class="default-text-color">Menu</span>
            </div>
            <div class="btn-popover-content">
                <ul class="ul-patient-menu __776432">
                    <li><a class="" href="Requisition?PatientRecordId=3" target="_blank">Worksheet</a></li>
                    <li><a class="" href="Requisition?PatientRecordId=3" target="_blank">Letter</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<div class="modal fade in col-sm-12" id="modal-template-reason-id">
</div>
<div id="ws-modal-container" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document" style="width:auto">
        <div class="modal-content">
            <div class="modal-body ">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <div id="ws-modal-content"></div>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
@functions {
    string getRequisitionStatusColor(int statusId)
    {
        var status = Model.requisitionStatus.FirstOrDefault(s => s.Id == statusId)
            ?? Model.requisitionStatus[0];
        return status.Color;
    }

    string getInternalTestStatusColor(int statusId)
    {
        var status = Model.internalTestStatus.FirstOrDefault(s => s.Item1 == statusId);
        if (status == null)
        {
            return Model.internalTestStatus[0].Item3;
        }
        return status.Item3;
    }

    string getInternalTestStatusName(int statusId)
    {
        var status = Model.internalTestStatus.FirstOrDefault(s => s.Item1 == statusId);
        if (status == null)
        {
            return Model.internalTestStatus[0].Item2;
        }
        return status.Item2;
    }
}