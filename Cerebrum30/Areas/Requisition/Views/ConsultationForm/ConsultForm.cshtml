﻿@model Cerebrum.ViewModels.Consultation.ConsultationForm
@{
    ViewBag.Title = "ConsulForm";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="~/Content/jquery.timepicker.css" rel="stylesheet" />
<script src="~/Scripts/jquery-ui-1.12.1.min.js"></script>
<script src="~/Scripts/jquery.timepicker.js"></script>
<script src="~/Scripts/cf_form.js"></script>
<link href="~/Content/cf_form.css" rel="stylesheet" />
<head id="Head1">

</head>
<body>
    @using (Html.BeginForm("ConsultForm", "ConsultationForm", FormMethod.Post, new { consultFormModel = Model }))
    {
        <div id="mainFrame">
            <div id="header">
                <div id="header-1">
                    <div id="headerLeft">
                        <div id="forLogo">
                            <div id="reqDateDiv">
                                <div id="reqDateStr">
                                    <span>Request date :</span>
                                    @Html.HiddenFor(x => x.dateOfRequestHid)
                                </div>
                                <div id="reqDateDate">
                                    <span>
                                        @Html.LabelFor(m => m.dateOfRequest, Model.dateOfRequest, new { id = "dateOfRequest" })
                                        @Html.HiddenFor(x => x.officeId)
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="letterHead">
                            <div class="refDoctor_">
                                <div class="labelRefDoc">
                                    @Html.LabelFor(m => m.refDoctorName, Model.refDoctorName, new { id = "refDoctorName" })
                                    @Html.HiddenFor(x => x.refDoctorNameHid)
                                </div>
                                <span>Referring Doctor:</span>
                            </div>
                            <div class="refDoctor">
                                <div class="labelRefDoc">
                                    <span>
                                        @Html.LabelFor(m => m.refDoctorAddress, Model.refDoctorAddress, new { id = "refDoctorAddress" })
                                        @Html.HiddenFor(x => x.refDoctorAddressHid)
                                    </span>
                                </div>
                                <span>Address:</span>
                            </div>
                            <div class="refDoctor">
                                <div class="labelRefDoc">
                                    @Html.LabelFor(m => m.refDoctorBill, Model.refDoctorBill, new { id = "refDoctorBill" })
                                    @Html.HiddenFor(x => x.refDoctorBillHid)
                                </div>
                                <span>Bill # | CPSO</span>
                            </div>
                            <div id="consultantDoc">
                                <div class="consultantDoc-name">
                                    <div id="consDocNameID" class="headerLabel">
                                        <span id="consDocSpan">
                                            @Html.LabelFor(m => m.consDocNameLabel, Model.consDocNameLabel, new { id = "consDocNameLabel" })
                                        </span>
                                    </div>
                                    <span>Consulting Doctor:</span>
                                </div>
                                <div class="consultantDoc-name">
                                    <div id="consDocAddressID" class="headerLabel">
                                        <span>
                                            @Html.LabelFor(m => m.consDocAddress, Model.consDocAddress, new { id = "consDocAddress" })
                                        </span>
                                    </div>
                                    <span class="headerSpan">Address:</span>
                                </div>
                                <div class="consultantDoc-name">
                                    <div class="headerLabel">
                                        @Html.HiddenFor(x => x.consDocNameHidden)
                                        @Html.HiddenFor(x => x.consDocAddressHidden)
                                        @Html.HiddenFor(x => x.phone_faxHidden)
                                        @Html.HiddenFor(x => x.consDocId)
                                        @Html.HiddenFor(x => x.consDocBillN)
                                        @Html.HiddenFor(x => x.consDocFax)
                                        <span>
                                            @Html.LabelFor(m => m.phone_fax, Model.phone_fax, new { id = "phone_fax" })
                                        </span>
                                    </div>
                                    <span class="headerSpan_">Phone | Fax:</span>
                                </div>
                                <div class="refDoctor">
                                    <div class="labelRefDoc">
                                        @Html.LabelFor(m => m.ccDocNameLabel, Model.ccDocNameLabel, new { id = "ccDocNameLabel" })
                                        @Html.HiddenFor(x => x.ccDocNameHidden)
                                        @Html.HiddenFor(x => x.ccDocFax)
                                        @Html.HiddenFor(x => x.ccDocBillN)
                                    </div>
                                    <span>CC :</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="headerRight">
                        <div id="headerRight_">
                            <div class="headerRight-name">
                                <div class="headerRightLabel">
                                    <span>
                                        @Html.LabelFor(m => m.patientName, Model.patientName, new { id = "patientName" })
                                    </span>
                                    @*<input id="patientHiddenId" type="hidden" runat="server" />*@
                                </div>
                                <span class="headerRightSpan">Patient:</span>
                            </div>
                            <div class="headerRight-name">
                                <div id="patientAddressID" class="headerRightLabel">
                                    <span>
                                        @Html.LabelFor(m => m.patientAddress, Model.patientAddress, new { id = "patientAddress" })
                                    </span>
                                </div>
                                <span class="headerRightSpan" style="height: 40px;">Address:</span>
                            </div>
                            <div class="headerRight-name">
                                <div class="headerRightLabel">
                                    <span>
                                        @Html.LabelFor(m => m.patientHelthCard, Model.patientHelthCard, new { id = "patientHelthCard" })
                                    </span>
                                </div>
                                <span class="headerRightSpan  dobClass">Health Card No:</span>
                            </div>
                            <div class="headerRight-name">
                                <div class="headerRightLabel ">
                                    <span>
                                        @Html.LabelFor(m => m.patientDOB, Model.patientDOB, new { id = "patientDOB" })
                                    </span>
                                </div>
                                <span class="headerRightSpan dobClass">DOB:</span>
                            </div>
                            <div class="headerRight-name">
                                <div class="headerRightLabel">
                                    <span>
                                        @Html.LabelFor(m => m.patientPhone, Model.patientPhone, new { id = "patientPhone" })
                                    </span>
                                </div>
                                <span class="headerRightSpan  dobClass">Phone No:</span>
                            </div>
                            <div class="headerRight-name">
                                <div class="headerRightLabel">
                                    <span>
                                        @Html.LabelFor(m => m.patientSex, Model.patientSex, new { id = "patientSex" })
                                    </span>
                                </div>
                                <span class="headerRightSpan">Sex:</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="header-2">
                    <div id="docTypeDiv" class="clearRight">
                        @Html.DropDownListFor(model => model.SelectedDocType, Model.docTypeList, htmlAttributes: new { @class = "form-control selec_", tabindex = 2 })
                    </div>
                </div>
            </div>
            <div id="mainContent">
                <div id="menuBar">
                    <div id="servicesDiv">
                        @Html.DropDownListFor(model => model.SelectedService, Model.servicesList, htmlAttributes: new { @class = "form-control selec_", tabindex = 2 })
                    </div>
                    <div class="docDiv">
                        @Html.EditorFor(model => model.docList, new { htmlAttributes = new { @class = "form-control", tabindex = 9, placeholder = "Doctors", autocomplete = "off" } })
                    </div>
                    <div class="docDiv_">
                        @Html.EditorFor(model => model.ccDocList, new { htmlAttributes = new { @class = "form-control", tabindex = 9, placeholder = "CC:", autocomplete = "off" } })
                    </div>
                    <div id="reasonDiv" class="clearRight">
                        @Html.DropDownListFor(model => model.SelectedReason, Model.reasonList, htmlAttributes: new { @class = "form-control selec_", tabindex = 2 })
                    </div>
                </div>
                <div id="content">
                    <div id="reasonFor">
                        <div id="reasonHead">
                            <div id="reasonMenuBar">
                                @*<asp:Menu ID="Menu2" CssClass="menuStyle_" runat="server"></asp:Menu>*@
                            </div>
                            <span>Reasons For Referral</span>
                        </div>
                        <div id="reasonText">
                            @Html.TextAreaFor(x => x.reasonsTextBox, 20, 15, new { @class = "form-control text-box multy-line txtHeight", tabindex = 44 })
                        </div>
                    </div>
                    <div id="medications" class="medClass">
                        <div id="medicationsLabel" class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            @Html.EditorFor(x => x.medicationsChB, new { htmlAttributes = new { } })
                            <label ID="Label55">Medications</label>
                        </div>
                    </div>
                    <div id="medicationsDiv" class="medDivClass showXScroll">
                        <div id="repeaterDivMed">
                            @*<div class="repItemDivMed">
                                    <div class="repCheckBoxMed">
                                    </div>
                                    <div class="repNameMed">
                                        <label ID="medRepName">Name</label>
                                    </div>
                                </div>*@
                            <div class="repItemDivMed">
                                <div class="repCheckBoxMed">
                                </div>
                                <div class="repNameMed">
                                    <label ID="medRepName" title="Name">Name</label>
                                </div>
                            </div>
                            <div class="repItemDivMed">
                                <div class="repCheckBoxMed">
                                </div>
                                <div class="repNameMed">
                                    <label ID="medRepName" title="Name">Name</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="letter" class="medClass">
                        <div id="Div4" class="checkLabel">

                        </div>
                        <div class="checkBoxDiv">
                            @Html.EditorFor(x => x.letterChB, new { htmlAttributes = new { } })
                            <label ID="Label49">Letters</label>
                        </div>
                    </div>
                    <div id="letterDiv" class="medDivClass showXScroll">
                        <div id="repeaterDivLetter">
                            <div class="repItemDivLetter">
                                <div class="repCheckBoxLetter">
                                    <input id="letterIdHidden" type="hidden" value="Id" />
                                    <input id="letterFileTypeHidden" type="hidden" value="fileType" />
                                    <input id="letterUrlHidden" enableviewstate="true" type="hidden" runat="server" value='<%# Eval("URL")%>' />
                                </div>
                                <div class="repDate1Letter">
                                    <label ID="letRepDate">SSSSSS</label>
                                </div>
                            </div>
                            <div class="repItemDivLetter">
                                <div class="repCheckBoxLetter">
                                    <input id="letterIdHidden" type="hidden" value="Id" />
                                    <input id="letterFileTypeHidden" type="hidden" value="fileType" />
                                    <input id="letterUrlHidden" enableviewstate="true" type="hidden" runat="server" value='<%# Eval("URL")%>' />
                                </div>
                                <div class="repDate1Letter">
                                    <label ID="letRepDate">AAAAAAAAAA</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="Div3" class="medClass">
                        <div id="Div4" class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            @Html.EditorFor(x => x.internalTestChB, new { htmlAttributes = new { } })
                            <label ID="Label49">Internal Tests</label>
                        </div>
                    </div>
                    <div id="internalTestDiv" class="medDivClass showXScroll">
                        <div class="repItemIntTest_h">
                            <div class="appDate1_it">
                                <label>Visit Date</label>
                            </div>
                            <div class="appDate2_it">
                                <label>Report Date</label>
                            </div>
                            <div class="appDate3_it">
                                <label>Test Name</label>
                            </div>
                        </div>
                        <div class="repItemIntTest">
                            <input id="tres_IdHidden" type="hidden" value="tres_Id_str" />
                            <input id="UrlHidden" type="hidden" value="Url" />
                            <input id="fnameHidden" type="hidden" value="fname" />
                            <div class="appDate1_it">
                                <label ID="visitRepDate">04/04/2016</label>
                            </div>
                            <div class="appDate2_it">
                                <label ID="reportRepDate">04/04/2016</label>
                            </div>
                            <div class="appDate3_it">
                                <label ID="nameRep">ECG</label>
                            </div>
                            <div class="appDate1_it">
                                <label ID="visitRepDate">04/04/2016</label>
                            </div>
                            <div class="appDate2_it">
                                <label ID="reportRepDate">04/04/2016</label>
                            </div>
                            <div class="appDate3_it">
                                <label ID="nameRep">ECG</label>
                            </div>
                        </div>
                    </div>
                    <div id="Div1" class="medClass">
                        <div id="Div2" class="checkLabel">
                        </div>
                        <div id="extDocPlug">
                            @Html.EditorFor(x => x.extDocChB, new { htmlAttributes = new { } })
                            <label ID="Label65">External Documents</label>
                        </div>
                    </div>
                    <div id="extDocDiv" class="medDivClass  showXScroll">
                        <div class="docDiv_h">
                            <div class="docDate0_c">
                                @*<label>ch</label>*@
                            </div>
                            <div class="docDate1_c">
                                <label>Document Name</label>
                            </div>
                            <div class="docDate2_c">
                                <label>Date</label>
                            </div>
                            <div class="docDate3_c">
                                <label>Document Type</label>
                            </div>
                        </div>
                        @for (int i = 0; i < Model.externalDocuments.Count; i++)
                        {
                            <div class="docDate0_c">
                                @Html.CheckBoxFor(m => m.externalDocuments[i].isChecked)
                            </div>
                            <div class="docDate1_c">
                                @Html.LabelFor(m => m.externalDocuments[i].name, Model.externalDocuments[i].name, new { id = "doc_name" })
                                @Html.HiddenFor(m => m.externalDocuments[i].extDocType)
                                @Html.HiddenFor(m => m.externalDocuments[i].id)
                            </div>
                            <div class="docDate2_c">
                                @Html.LabelFor(m => m.externalDocuments[i].date, Model.externalDocuments[i].date, new { id = "doc_date" })
                            </div>
                            <div class="docDate3_c">
                                @Html.LabelFor(m => m.externalDocuments[i].type, Model.externalDocuments[i].type, new { id = "doc_Type" })
                            </div>
                        }
                    </div>
                    <div class="medClass">
                        <input id="hiddenDocList" type="hidden" />
                        <div id="bookingDiv" class="checkLabel">
                            <span>Appointment Date & Time:</span>
                            @Html.EditorFor(model => model.datepicker, new { htmlAttributes = new { @class = "form-control", tabindex = 9, autocomplete = "off" } })
                            @Html.EditorFor(model => model.timepickerId, new { htmlAttributes = new { @class = "form-control timepicker", tabindex = 9, autocomplete = "off" } })
                            @*<asp:TextBox ID="datepicker" runat="server"></asp:TextBox>
                                <asp:TextBox ID="timepickerId" class="timepicker" runat="server"></asp:TextBox>*@
                        </div>
                        <div class="checkBoxDiv">
                            @Html.EditorFor(x => x.bookingChB, new { htmlAttributes = new { } })
                            <label ID="Label55">Patient Will Book</label>
                        </div>
                    </div>
                </div>
            </div>
            <div id="footer">
                <div id="buttonsDiv">
                    <button ID="submit_form" OnClientClick="return sub_fax_check();" class="subButton"
                            name="Submit" value="Submit" OnClick="submit_form_Click">
                        Submit
                    </button>
                    <button ID="submit_fax" OnClientClick="return sub_fax_check();" class="subButton"
                            name="SubmitFax"  value="SubmitFax" OnClick="submit_fax_Click">
                        Submit & Fax
                    </button>
                    <button ID="submit_print" class="subButton"
                            name="SubmitPrint"  value="SubmitPrint">
                        Submit & Print
                    </button>
                </div>
            </div>
            <div id="footer_" class="clearRight">
                @Html.LabelFor(m => m.message, Model.message)
            </div>
        </div>
        @Html.HiddenFor(m =>m.patientRecordId)


        @Html.HiddenFor(m => m.m_requisitionPatientId)
        @Html.HiddenFor(m => m.m_requisitionTypeId)
        @Html.HiddenFor(m => m.m_practiceDoctorId)
        @Html.HiddenFor(m => m.m_nextAppointment)
        @Html.HiddenFor(m => m.m_nextAppointmentDoctor)
        @Html.HiddenFor(m => m.m_nextAppointmentOffice)
        @Html.HiddenFor(m => m.m_comment)
    }
</body>
