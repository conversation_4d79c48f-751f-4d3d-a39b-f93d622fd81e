@model IEnumerable<Cerebrum30.Areas.Measurements.Models.ViewModels.ReportHistory>

<div class="pre-scrollable">
    <table class="table  table-condensed " cellpadding="2">
        <tr>
            <td>
                <b>Doctor</b>
            </td>
            <td>
                <b>Email</b>
            </td>
            <td>
                <b>Fax</b>
            </td>
            <td>
                <b>Date</b>
            </td>
            <td>
                <b>Sent</b>
            </td>
            <td>
                <b>Sent Method</b>
            </td>
            <td>
                <b>URL</b>
            </td>
            <td><b>Error</b></td>
        </tr>
        @foreach (var item in Model)
        {
            if (!String.IsNullOrWhiteSpace(item.URL))
            {
                var trClass = !item.Sent ? "danger" : "";
                <tr class="@trClass">
                    <td>
                        @item.DocName
                    </td>
                    <td>
                        @item.Email
                    </td>
                    <td>
                        @item.Fax
                    </td>
                    <td>
                        @item.DateEntered
                    </td>
                    <td>
                        @item.Sent
                    </td>

                    <td>
                        @item.SendType
                    </td>
                    <td>
                        @*<a href="@item.URL" target="_blank"> @item.URL</a>*@
                        @Html.ActionLink(item.URL, "RetrieveFile", new { path = @item.URL, appointmentID = item.AppointmentId, token = item.Token }, new { @target = "_blank" })
                    </td>
                    <td>
                        @Html.TextAreaFor(modelItem => item.ErrorMessage, new { @class = "form-control" })
                    </td>
                </tr>
                }
            }
    </table>
</div>
