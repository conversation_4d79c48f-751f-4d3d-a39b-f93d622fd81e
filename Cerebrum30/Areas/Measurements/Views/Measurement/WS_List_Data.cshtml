@* minified html to save bandwidth; right click and select un-minify to edit, but remember to minify when you are done editing *@
@model  Cerebrum.ViewModels.Measurements.VMWorkList
@{
    Layout = null;
    var totalPages = Model.Pages.Count();
    var totalRecords = Model.TotalRecords;
    var pageNumber = Model.Page;
    var pageSize = Model.PageSize;
    bool showPriority = Convert.ToBoolean(ViewBag.ShowPriority);

}

<style type="text/css">
    .transparent {
        opacity: 0;
    }

    .inline-block {
        display: inline-block;
    }

    #requisition-div .dropdown-menu .sub-menu {
        left: 100%;
        position: absolute;
        top: 0;
        display: none;
        margin-top: -1px;
    }

    #requisition-div .dropdown:hover ul, #requisition-div .dropdown:hover ul li:hover ul, #requisition-div .dropdown:hover ul li:hover ul li:hover ul {
        display: block;
    }

        #requisition-div .dropdown:hover ul li ul, #requisition-div .dropdown:hover ul li:hover ul li ul {
            display: none;
        }

    .ui-dialog, .ui-widget, .ui-widget-content {
        background: white !important;
    }

    .btn-popover-container {
        display: inline-block;
    }

    .btn-popover-content {
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 0;
        margin-bottom: 0;
    }

    .btn-popover-container .btn-popover-title, .btn-popover-container .btn-popover-content {
        display: none;
    }

    .popover-pointer {
        cursor: pointer;
    }
</style>
<link rel="stylesheet" href="Areas/Schedule/Content/shared-styles.css" />
<link rel="stylesheet" href="Areas/Schedule/Content/schedule.css" />
<link rel="stylesheet" href="Areas/Schedule/Content/appointments-modal.css" />

<script type="text/javascript" src="~/Areas/Schedule/Scripts/appointments.js"></script>

<style>
    .redColor {
        color: red;
    }

    .notSentColor {
        height: 22px;
        width: 100px;
        color: white;
        background-color: #d9534f;
        padding: 3px;
        display: inline-block;
    }

    .partiallySentColor {
        height: 22px;
        width: 100px;
        color: white;
        background-color: #EB984E;
        padding: 3px;
        display: inline-block;
    }

    .completelySentColor {
        height: 22px;
        width: 100px;
        color: white;
        background-color: #5bc0de;
        padding: 3px;
        display: inline-block;
    }

    .fontsize {
        font-size: 12px;
    }

    .tableheadercentered tr th {
        text-align: center;
    }
</style>
<script>

    function initPopover() {
        $(".btn-popover-container").each(function () {
            var btn = $(this).find(".popover-btn").first();
            var titleContainer = $(this).find(".btn-popover-title").first();
            var contentContainer = $(this).find(".btn-popover-content").first();

            var title = $(titleContainer).html();
            var content = $(contentContainer).html();

            $(btn).popover({
                html: true,
                title: title,
                content: content,
                placement: 'auto right',
                trigger: 'manual'//,
                //container:'body'
            }).on("mouseenter", function () {
                var _this = this;
                $(this).popover("show");
                $(this).siblings(".popover").on("mouseleave", function () {
                    $(_this).popover('hide');
                });
            }).on("mouseleave", function () {
                var _this = this;
                setTimeout(function () {
                    if (!$(".popover:hover").length) {
                        $(_this).popover("hide");
                    }
                }, 100);
            });
        });
    }
    $(document).ready(function () {

        $('[data-toggle="tooltip"]').tooltip({ animation: false });
        initPopover();
        //var oTable = $('#table').DataTable({
        //    "fixedHeader": true,
        //    "paging": false,
        //});

        //$('#table_info').hide();
    });

</script>


@{
    string app_test_list = string.Empty;

    foreach (var item in Model.Data)
    {
        app_test_list = app_test_list + "_" + item.AppointmentID + "-" + item.TestID;
    }
    <a href="@Url.Action("PrefetchAll", "Prefetcher", new { area = "Admin", appteststring = app_test_list })">Prefetch All</a>
}

<div>
    <div class="row">
        <div class="col-md-3">Total: @totalRecords</div>
        <div class="col-md-9">
            @if (Model.HasPaging && !String.IsNullOrWhiteSpace(Model.PagingDescription))
            {
                <div class="pull-left">
                    <span>@Model.PagingDescription</span>
                </div>
            }
            <div class="dropdown pull-right">
                <button class="btn btn-default dropdown-toggle btn-xs" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                    Page Size @pageSize
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-right">
                    @foreach (var size in Model.PageSizes)
                    {
                        var active = Convert.ToInt32(size.Value) == pageNumber ? "active" : "";
                        <li class="@active"><a data-wl-pagesize="@size.Value" class="wl-paging-item-size" href="#">@size.Text</a></li>
                    }
                </ul>
            </div>

            @if (Model.HasPaging)
            {
                <div style="padding-top:3px;padding-right:3px;" class="pull-right">
                    <span> of </span><span>@totalPages</span>
                </div>
                <div class="pull-right">
                    <div class="dropdown pull-right">
                        <button class="btn btn-default dropdown-toggle btn-xs btn-spacing" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                            Page @pageNumber
                            <span class="caret"></span>
                        </button>
                        <ul style="max-height:300px; overflow-y:auto;" class="dropdown-menu dropdown-menu-right">
                            @foreach (var page in Model.Pages)
                            {
                                var active = Convert.ToInt32(page.Value) == pageNumber ? "active" : "";
                                <li class="@active" data-wl-pagenum="@(page.Value)"><a class="wl-paging-item" data-wl-pagenum="@(page.Value)" href="#">@(page.Text)</a></li>
                            }
                        </ul>
                    </div>
                </div>
            }
        </div>
    </div>
</div>
<div style="margin-bottom:25px;padding-bottom:25px;" class="text-center fontsize">
    @*<table id="table" class="tableheadercentered table table-striped table-bordered table-condensed">*@
    <table class="table table-striped table-bordered table-condensed">
        <thead>
            <tr>
                <th></th>
                <th>Patient</th>
                <th>Appointment Type</th>
                <th>Test</th>
                <th>Test Date</th>
                <th>Status</th>
                @if (showPriority)
                {
                    <th>Priority</th>
                }
                <th>Office</th>
                <th>Doctor</th>
                <th>Reassigned To</th>
                <th>&nbsp; </th>
                <th>&nbsp;</th>
                @*<th>&nbsp;</th>*@
                <th>&nbsp;</th>

            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model.Data)
            {
                var makeBlueClass = item.TestName == "ELR" ? "blueColor" : "";
                var makeRedClass = item.SetForReview ? "redColor" : "";
                var sentLabel = item.SentCompletely == 0 ? "notSentColor" : (item.SentCompletely == 1 ? "completelySentColor" : "partiallySentColor");
                var testOnlyExtLabel = item.IsTestOnlyExt ? "label label-test-only" : "";
                <tr>
                    <td>
                        <div class="btn-popover-container">
                            <span class="popover-btn popover-pointer cb-text16 text-primary"><i class="glyphicon glyphicon-info-sign">&nbsp;</i></span>
                            <div class="btn-popover-title">
                                <span class="default-text-color">

                                </span>
                            </div>
                            <div class="btn-popover-content">
                                @{
                                    var patientMenu = new Cerebrum.ViewModels.Patient.VMPatientMenu();
                                    patientMenu.PatientId = item.PatienID;
                                    patientMenu.AppointmentId = item.AppointmentID;
                                    patientMenu.Practiceid = item.PracticeID;
                                    patientMenu.OfficeId = item.OfficeID;
                                    patientMenu.PatientFhirId = item.PatientFhirId;
                                }
                                @Html.RenderPartialAsync("_PatientMenu", patientMenu); }
                            </div>
                        </div>
                    </td>
                    <td>@item.Name</td>
                    <td><span class="@testOnlyExtLabel">@item.AppointmentType</span></td>
                    <td class="@makeRedClass">
                        <span>@item.TestName</span>
                        @if (@makeRedClass == "redColor")
                        {
                            <span style='color:black;' class='glyphicon glyphicon-search' data-toggle='tooltip' data-placement='bottom' title='Set For Review'></span>
                        }
                    </td>
                    <td>@item.TestDate.Value.ToShortDateString()</td>
                    <td>@item.TestStatus </td>
                    @if (showPriority)
                    {
                        <td>@item.PriorityName</td>
                    }
                    <td>@item.OfficeName</td>
                    <td>@item.DoctorName</td>
                    <td id="<EMAIL>">@item.ReassignDocName</td>
                    <td>
                        @if (showPriority)
                        {
                            if (item.IsVP)
                            {
                                <a href='@Url.Action("Index", "visit",
                                new
                                {
                                    Area = "VP",
                                    AppointmentTestID = item.AppointmentTestID,
                                })' rel="opener" target="_blank">
                                    <span class="label label-info label-app-type">Open</span>
                                </a>
                            }
                            else
                            {
                                <a href='@Url.Action("Index", "Measurement",
                                new
                                {
                                    Area = "Measurements",
                                    AppointmentID = item.AppointmentID,
                                    TestID = item.TestID,
                                    AppointmentTestID = item.AppointmentTestID,
                                    isWorkList = true
                                })' rel="opener" target="_blank">
                                    <span class="label label-info label-app-type">Open</span>
                                </a>
                            }
                        }
                        else
                        {
                            <a href='@Url.Action("Index", (item.IsVP ? "VP" : "Measurement"),
                            new
                            {
                                Area = (item.IsVP ? "VP" : "Measurements"),
                                AppointmentID = item.AppointmentID,
                                TestID = item.TestID,
                                AppointmentTestID = item.AppointmentTestID,
                                isWorkList = true
                            })' rel="opener" target="_blank">
                                <span class="label label-info label-app-type">Open</span>
                            </a>
                        }
                        @if (item.IsAbnormal) // update: redmine #12567, cloned from svn 10647, to create abnormal in AppointmentTest level
                        {
                            <img data-toggle="tooltip" data-placement="bottom" title="Abnormal" src="~/Content/Images/A-info.png" />
                        }
                    </td>
                    <td>
                        <span class="@sentLabel">@item.SentCompletelyStr</span>
                        <a data-apptest-id="@item.AppointmentTestID" class="btn btn-default btn-xs btn-view-send-history" href="#">Send History</a>
                    </td>
                    @*<td>
                        <select id="<EMAIL>">
                        @foreach (var pd in Model.PraticeDoctors)
                        {
                        <option value="@pd.ID">@pd.Name</option>
                        }
                        </select>

                        <a href="#"
                        data-doc-cntrl="<EMAIL>"
                        data-app-id="@item.AppointmentID"
                        data-app-test-id="@item.AppointmentTestID"
                        data-app-patient-id="@item.PatienID"
                        id="btnreassign" data-url='@Url.Action("Reassign_WL_Item")' class="btn-reassign">
                        <span class="label label-primary label-app-type">Reassign</span>
                        </a>
                        </td>*@
                    <td>
                        <a href="@Url.Action("PrefetchImages", "Prefetcher", new { area = "Admin", appointmentID = item.AppointmentID  , testID =item.TestID  })">Prefetch </a>
                    </td>

                </tr>
            }
        </tbody>
    </table>
</div>

