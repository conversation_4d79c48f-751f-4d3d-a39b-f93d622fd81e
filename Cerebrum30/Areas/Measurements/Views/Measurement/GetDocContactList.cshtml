@model  Cerebrum.ViewModels.VP.VMPatientAppointement
@{
    Layout = null;
}
<script>
    $(document).ready(function () {

        LoadHistoryData();
    });
</script>
@using (Html.BeginForm("SendFile", "Measurements", FormMethod.Post, new { @id = "frm-send-rp-ws", model = @Model }))
{
    @Html.HiddenFor(X => X.AppointmentID)
    @Html.HiddenFor(X => X.TestID)
    @Html.HiddenFor(X => X.PatientID)
    @Html.HiddenFor(X => X.AppointmentTestId)
    <div class="row">&nbsp;</div>
    <div class="row">&nbsp;</div>
    <div class="text-center">
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div>
                    <h3 style="padding-top:5px;" class="panel-title">Contacts</h3>
                </div>
            </div>
            <div class="panel-body pre-scrollable">
                <div>
                    <table class="table  table-condensed">
                        <tr>
                            <td><b>Names</b></td>
                            <td><b>Type</b></td>
                            <td><b>HRM</b></td>
                            <td><b>Fax</b></td>
                            <td><b>Mail</b></td>
                            <td><b>Email</b></td>
                            <td>&nbsp;</td>
                        </tr>

                        @for (int i = 0; i < Model.Doctors.Count; i++)
                        {
                            @Html.HiddenFor( x => x.Doctors[i].ID)
                            @Html.HiddenFor(x => x.Doctors[i].DocType)
                            @Html.HiddenFor(x => x.Doctors[i].FirstName)
                            @Html.HiddenFor(x => x.Doctors[i].LastName)
                            @Html.HiddenFor(x => x.Doctors[i].Name)
                            @Html.HiddenFor(x => x.Doctors[i].FaxNumber)
                            @Html.HiddenFor(x => x.Doctors[i].EmailAddress)
                            @Html.HiddenFor(x => x.Doctors[i].OHIPId)
                            <tr>
                                <td>@Model.Doctors[i].Name</td>
                                <td>@Model.Doctors[i].DocType</td>
                                <td>
                                    @Html.CheckBox("Doctors[" + i + "].HRM", Model.Doctors[i].HRM)
                                </td>
                                <td>
                                    @Html.CheckBox("Doctors[" + i + "].Fax", Model.Doctors[i].Fax)
                                </td>
                                <td>
                                    @Html.CheckBox("Doctors[" + i + "].Mail", Model.Doctors[i].Mail)
                                </td>
                                <td>
                                    @Html.CheckBox("Doctors[" + i + "].Email", Model.Doctors[i].Email)
                                    @Html.TextBox("Doctors[" + i + "].EmailAddress", Model.Doctors[i].EmailAddress)
                                </td>

                            </tr>
                        }
                    </table>

                </div>
            </div>
        </div>
        <div class="row">
            @*<button data-url='@Url.Action("SendFile", "Measurement" )'
                    id="btn-send-tocontactlist" class="btn btn-default btn-sm">
                Send
            </button>*@
        </div>
        <label>
                <p style="color:red" id="div-result-frm-send-rp-ws"></p>
        </label>
        <div class="panel panel-primary">
            <div class="panel-heading">
                <div>
                    <h3 style="padding-top:5px;" class="panel-title">History</h3>
                </div>
            </div>
            <div class="panel-body pre-scrollable">
                <div id="div-data">
                    <img src="~/Content/fancybox_loading.gif" />
                </div>
            </div>
        </div>
    </div>
}

