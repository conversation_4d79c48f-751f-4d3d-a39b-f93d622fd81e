@model Cerebrum.ViewModels.Measurements.VMWorklistRequest

@{
    var filterRequest = Model;
    var offices = ViewBag.Offices;
    var tests = ViewBag.Tests;
    var statuses = ViewBag.Statuses;
    var doctors = ViewBag.Doctors;
    var testGroups = ViewBag.TestGroups;
    var appointmentPriorities = ViewBag.AppointmentPriorities;
    bool showPriority = Convert.ToBoolean(ViewBag.ShowPriority);
}

@using (Html.BeginForm("WS_List_Data", "Measurement", FormMethod.Post, new { @id = "frm-wl" }))
{
    @Html.HiddenFor(x => filterRequest.PracticeID)

    @Html.HiddenFor(x => filterRequest.IsPracticeDoctor)
    @Html.HiddenFor(x => filterRequest.DoctorName)
    @Html.HiddenFor(x => filterRequest.FilterSet)
    @Html.HiddenFor(x => filterRequest.Page)
    @Html.HiddenFor(x => filterRequest.PageSize)
    @Html.HiddenFor(x => filterRequest.StatusID)

    <link href="~/Content/bootstrap-multiselect.min.css" rel="stylesheet" />
    <link href="~/Areas/Measurements/Content/work-list-filters.css" rel="stylesheet" />
    <script src="~/Scripts/bootstrap-multiselect.min.js"></script>
    <script src="~/Areas/Measurements/Scripts/work-list-filters.js"></script>

    <div>
        <div class="row">
            <div class="col-md-4"></div>
            <div class="col-md-4">
                <div id="lst-doc-name">
                    @if (filterRequest.SelectedDoctorID > 0)
                    {
                        <h4 class="nopadding">List of studies <NAME_EMAIL></h4>
                    }
                </div>
            </div>
            <div class="col-md-4"></div>
        </div>

        <div class="ds-filter-container">
            <div class="col-md-12">
                <div class="lbl-filter">Filters</div>
                <div class="form-inline pull-left">
                    <div class="form-inline">


                        @if (filterRequest.IsPracticeDoctor)
                        {
                            @Html.HiddenFor(x => filterRequest.SelectedDoctorID)
                        }
                        else
                        {
                            <div class="form-group form-group-sm">
                                <label for="DoctorID" class="control-label">Doctor</label>
                                @Html.DropDownListFor(m => filterRequest.SelectedDoctorID, new SelectList(doctors, "Value", "Text", filterRequest.SelectedDoctorID), "--All--", new { @class = "dropdown form-control" })

                            </div>
                        }

                        <div class="form-group form-group-sm">
                            <label for="OfficeID" class="control-label">Office</label>
                            @Html.DropDownListFor(m => filterRequest.OfficeID, new SelectList(offices, "Value", "Text", filterRequest.OfficeID), "--All--", new { @class = "dropdown form-control" })
                        </div>
                        <div class="form-group form-group-sm">
                            <label for="SelectedTestID" class="control-label">Test Type(s)</label>
                            @Html.DropDownListFor(m => filterRequest.SelectedTestGroupId, new SelectList(testGroups, "Value", "Text", filterRequest.SelectedTestGroupId), "All", new { @class = "dropdown form-control" })
                        </div>
                        <div class="form-group form-group-sm">
                            <label for="SelectedStatusIds" class="control-label">Status</label>
                            @Html.ListBoxFor(m => filterRequest.SelectedStatusIds, new MultiSelectList(statuses, "Value", "Text", filterRequest.SelectedStatusIds))
                        </div>

                        @if (showPriority)
                        {
                            <div class="form-group form-group-sm">
                                <label for="SelectedAppointmentPriorityIds" class="control-label">Priority</label>
                                @Html.ListBoxFor(m => filterRequest.SelectedAppointmentPriorityIds, appointmentPriorities as List<SelectListItem>)
                            </div>
                        }
                        <div class="form-group form-group-sm">
                            <label for="DateStart" class="control-label">Start</label>
                            @Html.TextBox("filterRequest.DateStart", filterRequest.DateStartStr, new { @id = "txt-start-date" })
                        </div>
                        <div class="form-group form-group-sm">
                            <label for="DateEnd" class="control-label">End</label>
                            @Html.TextBox("filterRequest.DateEnd", filterRequest.DateEndStr, new { @id = "txt-end-date" })
                        </div>
                        <div class="form-group form-group-sm">
                            <a class="btn btn-default btn-xs btn-spacing" data-url='@Url.Action("WS_List_Data")' id="btn-submit"><span class="glyphicon glyphicon-refresh default-text-color"> </span> Refresh</a>
                        </div>
                    </div>
                </div>
            </div>



            @*<div class="col-md-12">&nbsp;</div>*@
            <div @*class="col-md-12"*@>
                <div class="form-group-sm">
                    <div class="form-inline">
                        <div class="form-group form-group-sm">
                            @*chk-search-mode*@
                            @Html.CheckBoxFor(x => filterRequest.TestsOnlyExt, new { @class = "checkbox" })
                            <label class="control-label" for="chk-test-only-ecg ">Tests Only Ext</label>

                            @Html.CheckBoxFor(x => filterRequest.ExcludeECG, new { @class = "checkbox" })
                            <label class="control-label" for="chk-exclude-ecg ">Exclude ECGs</label>

                            @Html.CheckBoxFor(x => filterRequest.ShowForReview, new { @class = "checkbox" })
                            <label class="control-label" for="chk-tests-for-review">Show tests for review only</label>

                            @Html.CheckBoxFor(x => filterRequest.ShowAbnormal, new { @class = "checkbox" })
                            <label class="control-label" for="chk-tests-for-review">Show Abnormal only</label>

                            @*@Html.CheckBoxFor(x => filterRequest.ShowPartiallySent, new { @class = "checkbox" })
                                <label class="control-label" for="chk-tests-for-review">Show Partially Sent</label>*@
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div class="clearfix">&nbsp;</div>
        <div id="div-search-results"></div>
    </div>


}