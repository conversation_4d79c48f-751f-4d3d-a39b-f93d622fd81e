@model Cerebrum30.Areas.Measurements.Models.ViewModels.LegacyDocURL
@{
    ViewBag.Title = " Raw Data Classic";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

<h3>Legacy Data</h3>

<div class="row">&nbsp;</div>
<div class="row">&nbsp;</div>

<div class="text-center form-group">
    <table class="table table-condensed table-striped pre-scrollable">
        <tr>
            <td><label>Report</label></td>
            <td>
                @if (!string.IsNullOrEmpty(Model.ReportURL))
                {
                    @Html.ActionLink(Model.ReportURL, "RetrieveFileClassic", new { path = @Model.ReportURL, token = Model.Token, appointmentID =Model.AppointmentID })
                }
            </td>
        </tr>
        <tr>
            <td><label>Raw Data</label></td>
            <td>
                @if (!string.IsNullOrEmpty(Model.RawDocumentURL))
                {
                    @Html.ActionLink(Model.RawDocumentURL, "RetrieveFileClassic", new { path = @Model.RawDocumentURL, token = Model.Token, appointmentID = Model.AppointmentID })
                }
                
            </td>
        </tr>
        <tr>
            <td><label>Letter</label></td>
            <td>
                @if (!string.IsNullOrEmpty(Model.LetterURL))
                {
                    @Html.ActionLink(Model.LetterURL, "RetrieveFileClassic", new { path = @Model.LetterURL, token = Model.Token, appointmentID = Model.AppointmentID })
                }
            </td>
        </tr>
    </table>
</div>
