@model List<Cerebrum30.Areas.Measurements.Models.ViewModels.LegacyDocURL>
@{
    ViewBag.Title = "Raw Data Classic";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h3>Legacy Data</h3>

<div class="row">&nbsp;</div>
<div class="text-center form-group">
    <table class="table table-condensed table-striped pre-scrollable">
        <tr>
            <td><label>Letters</label></td>
        </tr>
        @foreach (var item in Model.Where(x => !string.IsNullOrEmpty(x.LetterURL)).ToList())
        {
            <tr>
                <td>
                    @if (!string.IsNullOrEmpty(item.LetterURL))
                    {
                        @Html.ActionLink(item.LetterURL, "RetrieveFileClassic", new { path = @item.LetterURL, token = item.Token, appointmentID = item.AppointmentID });
                    }
                </td>
            </tr>
        }
    </table>
    <table class="table table-condensed table-striped pre-scrollable">
        <tr>
            <td><label>Reports</label></td>
        </tr>
        @foreach (var item in Model.Where(x => !string.IsNullOrEmpty(x.ReportURL)).ToList())
        {
            <tr>
                <td>
                    @if (!string.IsNullOrEmpty(item.ReportURL))
                    {
                        @Html.ActionLink(item.ReportURL, "RetrieveFileClassic", new { path = @item.ReportURL, token = item.Token, appointmentID = item.AppointmentID });
                    }
                </td>
            </tr>
        }
    </table>
    <table class="table table-condensed table-striped pre-scrollable">
        <tr>
            <td><label>Raw Data</label></td>
        </tr>
        @foreach (var item in Model.Where(x => !string.IsNullOrEmpty(x.RawDocumentURL)).ToList())
            {
            <tr>

                <td>
                    @if (!string.IsNullOrEmpty(item.RawDocumentURL))
                    {
                        @Html.ActionLink(item.RawDocumentURL, "RetrieveFileClassic", new { path = @item.RawDocumentURL, token = item.Token, appointmentID = item.AppointmentID });
                    }
                </td>
            </tr>
        }
    </table>

</div>
