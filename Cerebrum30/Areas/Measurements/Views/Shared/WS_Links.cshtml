@model Cerebrum30.Areas.Measurements.Models.ViewModels.WorkSheetVM
@{
    List<string> us2AiLinks = new List<string>();
    var subdomain = Model.PracticeUs2AiSubdomain;
    if (!string.IsNullOrWhiteSpace(subdomain))
    {
        var accessionNumber = $"{Model.AppointmentID}_{Model.TestID}";
        us2AiLinks = new Cerebrum.BLL.RadAdapter.RadBLL().GetStudies(accessionNumber)
            .Select(study => study.StudyInstanceUid)
            .Where(studyInstanceUid => !string.IsNullOrWhiteSpace(studyInstanceUid))
            .Distinct()
            .Select(studyInstanceUid => $"https://{subdomain}.us2.ai/#/us2/search;tags=StudyInstanceUID:{studyInstanceUid}/report")
            .ToList();
    }
}

<script src="~/Areas/ExternalDocument/Scripts/ExternalDocument.js"></script>

<div class="form-inline">
    @if (Model.IsTestAbnormal && Model.ActionOnAbnormal)
    {
        <div class="alert alert-danger">Please remember to book a consultation as this was action on abnormal test.</div>
    }
    <div class="form-group form-group-sm menu-links">
        @(await Html.PartialAsync("_PreviousTests", new Cerebrum.ViewModels.Measurements.VMPreviousTestItem())
   {
       AppointmentID =
        Model.AppointmentID,
       TestID = Model.TestID,
       PatientID = Model.PatientID
   })
    </div>
    <div class="form-group form-group-sm menu-links">
        <div class="dropdown">
            <button class="btn btn-default btn-sm custom-menu-link" type="button" data-toggle="dropdown"
                    aria-haspopup="true" aria-expanded="true">
                <span class="menuText">Show</span> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu menuText">
                <li>
                    <a id="btn-ws-showimages"
                       href='@Url.Action("ShowImages", new { appointmentId = Model.AppointmentID, testID= Model.TestID })'>
                        Show
                        Images
                    </a>
                </li>
                <li>
                    <a target="_blank"
                       href='@Url.Action("ShowImagesWeb", new { appointmentId = Model.AppointmentID, testID= Model.TestID })'>
                        Show
                        Web Images
                    </a>
                </li>
                <li>
                    <a id="btn-ws-showpdf" data-appointment-id="@Model.AppointmentID" data-test-id="@Model.TestID"
                       style="cursor: pointer;" onclick="ShowPDF(this)">Show PDF</a>
                </li>
                <li>
                    <a id="hlRawDataClassic" target="_blank"
                       href='@Url.Action("OpenRawDataClassicByPatient", new { appointmentId = Model.AppointmentID, patientID = Model.PatientID })'>
                        Legacy Data&nbsp;<i class='glyphicon   c-pointer'></i>
                    </a>
                </li>

                <li>
                    <a id="hlRawData" rel="opener" target="_blank"
                       href='@Url.Action("OpenRawData", new { appointmentId = Model.AppointmentID, testID= Model.TestID })'>
                        Upload Raw Data&nbsp;<i class='glyphicon   c-pointer @(Model.HasRawData ? "glyphicon-ok color-green" : "glyphicon-remove color-red")'></i>
                    </a>
                </li>
                <li>
                    @if (Model.HasRawData)
                    {
                        <a id="hlRawData" style="cursor: pointer;" data-appointment-id="@Model.AppointmentID"
                           data-test-id="@Model.TestID"
                           data-url='@Url.Action("ShowRawData", new { appointmentId = Model.AppointmentID, testID = Model.TestID })'
                           onclick="ShowRawData(this)">
                            Show Raw Data&nbsp;
                        </a>
                    }
                    else
                    {
                        <a style="cursor: default;" href="javascript:void(0);">
                            <span style="color: lightgrey;">
                                Show Raw
                                Data
                            </span>
                        </a>
                    }
                </li>
                <li>
                    <a target="_blank" id="btn-report-history"
                       data-url='@Url.Action("ReportHistory", new { appointmentID = Model.AppointmentID, testID = Model.TestID, patientID = Model.PatientID })'
                       href="#">Report History</a>
                </li>
                <li>
                    @Html.ActionLink("Preview Report", "GetReportPreview", "Reports", new
                        {
                            area = "documents",
                            appointmentTestId = Model.AppointmentTestID,
                            isAmended = Model.IsAmended
                        }, new
                        {
                            @id =
                            "btn-preview",
                            @target = "_blank"
                        })
                </li>
                <li>
                    <a class="btn-view-ref-documents" data-app-id="@Model.AppointmentID"
                       data-modal-url="@Url.Action("getreferraldocuments", "uploads", new { area = "documents", appointmentId = Model.AppointmentID })"
                       href="#">Referral Documents</a>
                </li>
                <li>
                    <a data-apptest-id="@Model.AppointmentTestID" class="btn-view-send-history" href="#">
                        Send
                        History
                    </a>
                </li>
                <li>
                    <a target="_blank" class=""
                       href="requisition?patientRecordId=@Model.PatientID&practiceDoctorId=@Model.PracticeDoctorID&officeId=@Model.OfficeId"
                       id="btn-req">Requisition</a>
                </li>
                <li>
                    <a target="_blank"
                       href="http://www.zunis.org/Duke%20Treadmill%20Score%20-%20CAD%20Predictor.htm">
                        Duke Treadmill
                        Score
                    </a>
                </li>
                @if (us2AiLinks.Count > 0)
                {
                    <li class="dropdown-submenu">
                        <a class="submenu-header" data-toggle="dropdown">
                            US2.AI Reports
                        </a>
                        <ul class="dropdown-menu menuText">
                            @for (int i = 0; i < us2AiLinks.Count; i++)
                            {
                                <li>
                                    <a class="dropdown-item" href="@us2AiLinks[i]" target="_blank">
                                        Report @(i + 1)
                                    </a>
                                </li>
                            }
                        </ul>
                    </li>
                }
            </ul>
        </div>
    </div>
    <div class="form-group form-group-sm menu-links">
        <div class="dropdown">
            <button class="btn btn-default btn-sm custom-menu-link" type="button" id="dropdownMenu1"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                <span class="menuText">Actions</span> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu sub-menu menuText">
                <li>
                    <a href="#" id="btnNormalAll" title="Set Normal for All" height="20px" width="20px">
                        Set Normal for
                        All
                    </a>
                </li>
                @* update: redmine #12567, cloned from svn 10647, to create abnormal in AppointmentTest level *@
                <li>
                    <a href="#" data-test-id="@Model.TestID" data-test-abnormal="@Model.IsTestAbnormal"
                       data-url='@Url.Action("SetActionOnAbnormal")' id="btn-set-abnormal" title="Set Abnormal"
                       height="20px" width="20px">
                        Set Abnormal
                        &nbsp;<i class='glyphicon c-pointer @(Model.IsTestAbnormal ? "glyphicon-ok color-green" : "glyphicon-remove color-red")'></i>
                    </a>
                </li>
                <li>
                    <a href="#" data-url='@Url.Action("SetForReview")' data-atid="@Model.AppointmentTestID"
                       id="btn-set-review" title="Set For Review" height="20px" width="20px">
                        Set For Review
                        <i class='glyphicon c-pointer @(Model.SetForReview ? "glyphicon-ok color-green" : "glyphicon-remove color-red")'></i>
                    </a>
                </li>

                <li>
                    <a href="#" data-url='@Url.Action("AutoPhrases")' id="btn-auto-phrase" title="Auto Phrases"
                       height="20px" width="20px">Auto Phrases</a>
                </li>
                @*<li>
                    <a href="#"
                    data-url='@Url.Action("MeasMultipleValues")'
                    id="btn-add-multiple-meas-values" title="Add Multiple Values" height="20px" width="20px">Add Multiple
                    Values</a>
                    </li>*@
                <li>
                    <a id="btn-editphrases" href="#" data-url='@Url.Action("EditReportPhraseSetting", "Measurement",
new
{
    patientID = @Model.PatientID,
    practiceID = @Model.PracticeID,
    userID = @Model.DoctorID,
    testID = @Model.TestID,
    DocID = @Model.DoctorID
})'>
                        Edit Root Categories
                    </a>
                </li>
            </ul>
        </div>
    </div>
    @*<div class="form-group form-group-sm">
        <a class="btn btn-default btn-sm custom-menu-link"
        href='@Url.Action("Day", "Appointments",
        new
        {
        Area = "Schedule",
        OfficeId = Model.OfficeId,
        Date = Model.DateStr
        })'>
        DaySheet
        </a>
        </div>*@
    <div class="form-group form-group-sm">
        <a class="btn btn-default btn-sm custom-menu-link" target="_blank"
           href='@Url.Action("WorkList",new {DoctorID=CerebrumUser.IsDoctor?CerebrumUser.PracticeDoctorId:0})'
           id="btn-worklist">Worklist</a>
    </div>
    @if (Model.HasBullsEye)
    {
        <div class="form-group form-group-sm">
            <a class="btn btn-default btn-sm custom-menu-link btn-bull-eye" data-url="@Url.Action("BullEye")"
               data-appointment-id="@Model.AppointmentID" data-test-id="@Model.TestID" href="">Bull's Eye</a>
        </div>
    }
    @if (Model.IsTestAbnormal)
    {
        <div class="form-group form-group-sm">
            <label class="label label-danger">Abnormal</label>
        </div>
    }
    <div style="position: relative;display: inline-block;">
        <button class="btn btn-default btn-sm custom-menu-link" type="button" data-toggle="dropdown"
                aria-haspopup="true" aria-expanded="true">
            <span class="menuText ">Resources</span> <span class="caret"></span>
        </button>
        <ul class="dropdown-menu sub-menu menuText">
            <li>
                <a href="/StaticDocs/E1_AORTA.png" id="btn-static-link-aorta" title="Aorta Reference"
                   target="_blank">Aorta Reference</a>
            </li>
            <li>
                <a href="/StaticDocs/2016_LVDiastolicFunction.pdf" id="btn-static-link-ase-DiastolicFunction"
                   title="DiastolicFunction" target="_blank">2016 LV DiastolicFunction</a>
            </li>
            <li>
                <a href="/StaticDocs/2018 ASE Guideline - Performing Comprehensive TTE in Adults.pdf"
                   id="btn-static-link-ChamberQuantification2015" title="diaston" height="20px" width="20px"
                   target="_blank">2018 ASE Guideline - Performing Comprehensive TTE in Adults</a>
            </li>
            <li>
                <a href="/StaticDocs/2018 ASE Guideline - Chamber Quantification.pdf"
                   id="btn-static-link-ChamberQuantification2015" title="diaston" height="20px" width="20px"
                   target="_blank">2018 ASE Guideline - Chamber Quantification</a>
            </li>
            <li>
                <a href="/StaticDocs/2019 ASE Guideline - Stress Echo.pdf"
                   id="btn-static-link-ChamberQuantification2015" title="diaston" height="20px" width="20px"
                   target="_blank">2019 ASE Guideline - Stress Echo</a>
            </li>
            <li>
                <a href="/StaticDocs/Estimated FC according to METS Score.pdf" id="btn-static-link-MetsScore"
                   title="METS Score" height="20px" width="20px" target="_blank">
                    Estimated FC according to METS
                    Score
                </a>
            </li>
            <li>
                <a href="/StaticDocs/Classification of Cardiac Workload.pdf"
                   id="btn-static-link-ClassificationOfCardiacWorkload" title="Classification of Cardiac Workload"
                   height="20px" width="20px" target="_blank">Classification of Cardiac Workload</a>
            </li>
            <li>
                <a href="/StaticDocs/Lipid_Algorithm_2021_CCS_Guidelines.pdf" id="btn-static-link-aorta" title="2021 CCS Guidelines - Lipid Algorithm"
                   target="_blank">2021 CCS Guidelines - Lipid Algorithm</a>
            </li>
        </ul>
    </div>
</div>
<script>
    $(document).ready(function () {
        var hasRawData = @(Model.OpenRawData ? 1 : 0);
        if (hasRawData && typeof openRawData !== 'undefined' && openRawData == 1) {
            openRawData = 0;
            var url = $("#hlRawData").attr('href') + "&openRawData=1";
            window.open(url);
        }

        var hasImage = @(Model.OpenImage ? 1 : 0);
        if (hasImage && typeof openImage !== 'undefined' && openImage == 1) {
            openImage = 0;
            var url = $("#btn-ws-showimages").attr('href');
            window.open(url);
        }

        var hasReferralDocument = @(Model.AutoOpenReferralDocuments.Count > 0 ? 1 : 0);
        if (hasReferralDocument && typeof openReferralDocument !== 'undefined' && openReferralDocument == 1) {
            openReferralDocument = 0;
            var url = "";
    @{
        foreach (var referralDocument in Model.AutoOpenReferralDocuments)
        {
            @:url = "Measurements/Measurement/RetrieveFile?path=@(referralDocument.FilePath.Replace("\\", "|").Replace(":", "||"))&appointmentID=@(referralDocument.ApppintmentID)";
            @:window.open(url);
        }
    }
}
    });

    function ShowPDF(element) {
        var appointmentId = $(element).data("appointment-id");
        var testId = $(element).data("test-id");
        var url = "Measurements/Measurement/GetPDFInfo?appointmentId=" + appointmentId + "&testID=" + testId;

        $.ajax({
            url: url,
            type: "GET",
            beforeSend: function () { $("#ajax-loader").show(); },
            complete: function () { $("#ajax-loader").hide(); },
            success: function (result) {
                var clinicServerUrl = result.clinicServerUrl;
                var dataUrl = result.path;
                var token = result.token;
                var mainServerUrl = "Measurements/Measurement/ShowPDF?appointmentId=" + appointmentId + "&testID=" + testId;

                if (dataUrl == null) {
                    showMessageModal("error", "PDF does not exist", false);
                }
                else {
                    readExternalDocument(clinicServerUrl, dataUrl, token, appointmentId, 0, "", mainServerUrl, 1, 0);
                }
            },
            error: function (jqXHR, textStatus, errorThrown) {
                checkAjaxError(jqXHR);
            }
        });

        return false;
    }
    function ShowRawData(element) {
        var appointmentId = $(element).data("appointment-id");
        var testId = $(element).data("test-id");
        var url = $(element).data("url");
        var data = {};
        ajaxCall(url, data, false, function (result) {
            if (result.Files != null && result.Files.length > 0) {
                var clinicServerUrl = result.clinicServerUrl;
                var dataUrl = result.Files[0].Path;
                var token = result.Files[0].Token;
                //var appointmentId = $(element).data("appointmentid");
                var mainServerUrl = "Measurements/Measurement/RetrieveFile?path=" + escape(dataUrl) + "&appointmentID=" + appointmentId + "&token=" + encodeURIComponent(token);
                readExternalDocument(clinicServerUrl, dataUrl, token, appointmentId, 0, "", mainServerUrl, 1, 0);
            }
        });

        return false;
    }
</script>