﻿@model Cerebrum.ViewModels.Triage.VMWaitListSearch

@using (Html.BeginForm("GetWaitList", "waitlist", new { area = "triage" }, FormMethod.Get, true, new { @class = "form-inline", @id = "frm-triage-search" }))
{    
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.DoctorId, htmlAttributes: new { @class = "control-label" })
        @Html.DropDownListFor(model => model.DoctorId, new SelectList(ViewBag.PracticeDoctors, "PracticeDoctorId", "FullName", Model.DoctorId), new { @class = "form-control" })
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.OfficeId, htmlAttributes: new { @class = "control-label" })
        @Html.DropDownListFor(model => model.OfficeId, new SelectList(ViewBag.Offices, "Id", "Name", Model.OfficeId), new { @class = "form-control" })
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.StartDate, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.StartDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.EndDate, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.EndDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
        <button type="button" style="margin-right:5px;margin-left:5px;" class="btn btn-default btn-xs" id="btn-waitlist-prev">Prev 30 days</button>
        <button type="button" style="margin-right:5px;margin-left:5px;" class="btn btn-default btn-xs" id="btn-waitlist-next">Next 30 days</button>
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.Patient, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.Patient, new { htmlAttributes = new { @class = "form-control" } })
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.AppointmentStatusId, htmlAttributes: new { @class = "control-label" })
        @Html.DropDownListFor(model => model.AppointmentStatusId, new SelectList(ViewBag.LKAppointmentstatus, "Value", "Text", Model.AppointmentStatusId), new { @class = "form-control" })
    </div>   

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.TriageStatusId, htmlAttributes: new { @class = "control-label" })
        @Html.DropDownListFor(model => model.TriageStatusId, new SelectList(ViewBag.LKTriageStatuses, "Value", "Text", Model.TriageStatusId), new { @class = "form-control" })
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.TriageUrgencyId, htmlAttributes: new { @class = "control-label" })
        @Html.DropDownListFor(model => model.TriageUrgencyId, new SelectList(ViewBag.LKTriageUrgencies, "Value", "Text", Model.TriageUrgencyId), new { @class = "form-control" })
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.TestGroupId, htmlAttributes: new { @class = "control-label" })
        @Html.DropDownListFor(model => model.TestGroupId, new SelectList(ViewBag.LKTestGroups, "Id", "Description", Model.TestGroupId), new { @class = "form-control" })
    </div>
    <div class="checkbox">
        <label>
            @Html.EditorFor(model => model.CancellationList) <span class="checkbox-text">@Html.DisplayNameFor(model => model.CancellationList)</span>
        </label>        
    </div>


    <button type="submit" style="margin-right:5px;margin-left:5px;" class="btn btn-default btn-sm">Search</button>

            }
<div>
    @Html.ValidationMessageFor(model => model.DoctorId, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.TriageUrgencyId, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.TriageStatusId, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.StartDate, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.EndDate, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.Patient, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.CancellationList, "", new { @class = "text-danger" })
</div>


@*@section Scripts
{


    <script type="text/javascript">
    loadMainPatientAutoCompleteTriage();


    function loadMainPatientAutoCompleteTriage() {


      //  alert("aaaaa");
        if ($("#Patient").length) {
            $('#Patient').autocomplete({
                source: function (request, response) {
                    var searchUrl = '/patients/getpracticepatients/';
                    var search = request.term;

                    console.log(request.term);
                    console.log(searchUrl);

                    $.ajax({
                        url: searchUrl,
                        type: 'GET',
                        data:
                            {
                                patientSearch: search
                            },
                        success: function (data) {
                            response(data);

                          
                            console.log(JSON.stringify(data));
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            checkAjaxError(jqXHR);
                        },
                        complete: function () {

                        },
                    });
                },
                minLength: 2,
                select: function (event, ui) {
                    alert(ui.item.FullName);

                    document.getElementById('Patient').value=ui.item.FullName;
                 

                //   $('#Patient').val(ui.item.FullName);
                }
            }).autocomplete("instance")._renderItem = function (ul, item) {
                return $('<li style="color:#808080;">' +
                    '<div style="border-bottom:1px solid #808080;">' + item.FullName + '<br>'
                    + '<span>Birth Date: ' + item.DOB + ' </span>'
                    + '<span>OHIP: ' + item.OHIP + '</span>'
                    + '</div>'
                    + '</li>').appendTo(ul);
            };
              
            }
        }
   


        </script>
}*@


