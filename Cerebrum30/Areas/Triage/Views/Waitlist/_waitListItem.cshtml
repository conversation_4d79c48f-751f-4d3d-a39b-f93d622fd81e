@model Cerebrum.ViewModels.Triage.VMWaitListItem
    
    @{ 
        var item = Model;
        var wlTriageUrgencies = new List<Cerebrum.ViewModels.Common.VMLookupItem>();
        var wlTriageStatuses = new List<Cerebrum.ViewModels.Common.VMLookupItem>();
        var tests = new List<Cerebrum.ViewModels.Schedule.VMAppointmentTest>();

        if (ViewBag.LKTriageUrgencies != null)
        {
            wlTriageUrgencies = (List<Cerebrum.ViewModels.Common.VMLookupItem>)ViewBag.LKTriageUrgencies;
            wlTriageUrgencies = wlTriageUrgencies.Where(w =>w.Text!=null && w.Text.ToLower() != "all").ToList();
        }

        if (ViewBag.LKTriageStatuses != null)
        {
            wlTriageStatuses = (List<Cerebrum.ViewModels.Common.VMLookupItem>)ViewBag.LKTriageStatuses;
            wlTriageStatuses = wlTriageStatuses.Where(w =>w.Text!=null && w.Text.ToLower() != "all").ToList();
        }

        if (ViewBag.LKPracticeTests != null)
        {
            tests = (List<Cerebrum.ViewModels.Schedule.VMAppointmentTest>)ViewBag.LKPracticeTests;
        }
    }   
         
    <td>
        <div class="td-wl-patient-info">            
            <div class="btn-popover-container">
                <button type="button" class="btn btn-default btn-xs popover-btn text-info">
                    @item.Patient
                </button>
                <div class="btn-popover-title">e-Chart</div>
                <div class="btn-popover-content">                    
                    @{
                        var patientMenu = new Cerebrum.ViewModels.Patient.VMPatientMenu();
                        patientMenu.PatientId = Model.PatientId;
                        patientMenu.AppointmentId = Model.Id;
                        patientMenu.Practiceid = Model.PracticeId;
                        patientMenu.OfficeId = Model.OfficeId;
                        patientMenu.PatientFhirId = Model.PatientFhirId;
                    }
                    @Html.RenderPartialAsync("_PatientMenu", patientMenu); }
                </div>
            </div>
        </div>
    </td>
    <td>
        <div class="td-wl-patient-info">
            <div class="btn-popover-container">
                <button type="button" class="btn btn-default btn-xs popover-btn text-info">
                    <span class="glyphicon glyphicon-info-sign"> </span>
                </button>
                <div class="btn-popover-title">
                    Additional Info
                </div>
                <div class="btn-popover-content">
                    <div><span>Appointment Id: </span><span>@item.Id</span></div>
                    <div><span>Family Doctor: </span><span>@item.FamilyDoctor</span></div>
                    <div><span>Phone Numbers: </span><span>@item.PatientPhoneNumbers</span></div>
                </div>
            </div>
        </div>
    </td>
    <td>
        <div class="td-wl-patient-info">
            <div class="btn-popover-container">
                @{
                    var appNotesColor = String.IsNullOrWhiteSpace(Model.AppointmentNotes) ? "text-primary" : "text-danger";
                }
                <span class="popover-btn c-pointer glyphicon glyphicon-tag @appNotesColor"></span>
                <div class="btn-popover-title">
                    <span class="default-text-color">Appointment Notes</span>
                </div>
                <div class="btn-popover-content">
                    <form class="frm-appointment-notes-edit-waitlist" method="post" role="form" action="@Url.Action("editnotes", "appointments", new { area = "schedule" })">
                        <div class="form-horizontal">
                            <div class="form-group form-group-sm">
                                <div class="col-md-12">
                                    <input type="hidden" id="<EMAIL>" name="appointmentId" value="@item.Id" />
                                    <textarea id="<EMAIL>" name="notes" class="_etb" rows="3" cols="6">@item.AppointmentNotes</textarea>
                                </div>
                            </div>
                            <div class="form-group form-group-sm">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-primary btn-xs">Save</button>
                                    <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </td>
    <td>
        @item.Doctor
    </td>
               
    <td>
        @item.Office
    </td>
    <td>
        @item.AppointmentDateTime
    </td>
    <td>
        @item.AppointmentType
    </td>
               
    <td>
        @item.RequestedTests
    </td>
    

    <td >        
        <div  class="td-wl-added-tests">     
            
            @if (Model.AddedTestsList.Any())
            {
                <ul style="list-style:none;">
                    @foreach (var test in Model.AddedTestsList)
                    {
                        <li style="margin-right:3px;" class="pull-left">                            
                            <button class="triage-remove-addedtest btn btn-default btn-xs" data-toggle="tooltip" data-placement="bottom" title="Remove Test" data-app-id="@test.AppointmentId" data-app-test-id="@test.AppointmentTestId" data-confirm-msg="Remove Test: @test.TestName">@test.TestName</button>
                        </li>
                    }
                </ul>
                
                <div class="clearfix"></div>
                <div style="border-bottom: 1px solid #ededed; margin-top:3px;margin-bottom:3px;"></div>
            }

            <div class="btn-popover-container">
                <button type="button" class="btn btn-default btn-xs popover-btn">
                    Internal                
                </button> @*@item.AddedTests*@
                <div class="btn-popover-title">
                    Internal Tests
                </div>
                <div class="btn-popover-content">
                    <ul class="ul-popover-list">
                        @foreach (var test in tests)
                        {
                            <li class="triage-test-item" data-app-id="@item.Id" data-test-id="@test.TestId">
                                <div>@test.Name</div>
                            </li>
                        }
                    </ul>
                </div>
            </div>         
           
            <a class="btn btn-default btn-xs" href="requisition?patientRecordId=@item.PatientId&practiceDoctorId=@item.DoctorId&officeId=@item.OfficeId">Flow</a>

        </div>

       
        
        

    </td>

    <td>
        @item.DateEntered
    </td>
    <td>
        @item.EnteredBy
    </td>
    <td>
        <button data-app-id="@item.Id" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Click to Add / Edit Triage Dispositions" class="btn btn-default btn-xs btn-triage-disposition">
        <span class="glyphicon glyphicon-pencil text-primary"></span>
        </button> @Html.Raw(item.TriageDisposition)  
                      
    </td>
    <td class="td-wl-traige-urg">       
        <div class="btn-popover-container">           
          
        <button type="button" class="btn btn-default btn-xs popover-btn">
            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Add / Edit Triage Urgency" class="glyphicon glyphicon-pencil text-primary"></span>            
        </button> @item.TriageUrgency

            <div class="btn-popover-title">
                Triage Urgencies
            </div>
            <div class="btn-popover-content">
                <ul class="ul-popover-list">
                    @foreach (var urg in wlTriageUrgencies)
                    {
                        <li class="app-wailist-item" data-app-id="@item.Id" data-triage-urgency-id="@urg.Value">
                            <div>@urg.Text</div>
                        </li>
                    }                    
                </ul>
            </div>
        </div>
    </td> 

    <td class="td-wl-traige-status">   
    <div class="btn-popover-container">       
       
        <button type="button" class="btn btn-default btn-xs popover-btn">                    
            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Add / Edit Triage Status" class="glyphicon glyphicon-pencil text-primary"></span>
        </button>
        
          @item.TriageStatus
        <div class="btn-popover-title">
            Triage Statuses
        </div>
        <div class="btn-popover-content">
            <ul class="ul-popover-list">
                @foreach (var status in wlTriageStatuses)
                {
                    <li class="app-wailist-item" data-app-id="@item.Id" data-triage-status-id="@status.Value">
                        <div>@status.Text</div>
                    </li>
                }
            </ul>
        </div>
    </div>
</td>
      
         
    <td class="td-wl-menu">
 
            <div class="btn-popover-container">
                <button type="button" class="btn btn-default btn-xs popover-btn">
                    <span class="glyphicon glyphicon-option-vertical text-primary"></span>
                </button>
                <div class="btn-popover-title">
                    Appointment Menu
                </div>
                <div class="btn-popover-content">
                    <ul class="ul-popover-list">

                        @{
                            var refDocumentColor = Model.TotalReferralDocuments > 0 ? "text-danger bold-text" : "";
                            var refDocumentLink = "btn-view-ref-documents";
                            int testCount = (string.IsNullOrWhiteSpace(Model.RequestedTests)?0: Model.RequestedTests.Split(',').Count()) + Model.AddedTestsList.Count();
                            //var refDocTotal = Model.TotalReferralDocuments > 0 ? "(" + Model.TotalReferralDocuments + ")" : "";
                        }

                        <li><a href="#" class="appointment-edit" data-modal-url="@Url.Action("edit", "appointments", new { area = "schedule", appointmentId = Model.Id,triagetobook=true })">Edit Appointment</a></li>
                        @if (testCount > 1)
                        {
                            <li><a href="#" class="appointment-split" data-modal-url="@Url.Action("SplitAppointment", "appointments", new { area = "schedule", appointmentId = Model.Id })">Split Appointment</a></li>
                        }
                        <li><a href="#" class="booking-comments" data-modal-url="@Url.Action("EditBookingComments", "appointments", new { area = "schedule", appointmentId = Model.Id })">Booking Comments</a></li>
                        <li><a href="#" data-modal-url="@Url.Action("getappointmenthistory", "appointments", new { area = "schedule", appointmentId = Model.Id })" class="btn-app-history">Appointment History</a></li>
                        <li><a href="#" class="btn-contact-message" data-recipient-id="@Model.EnteredById" data-patient-id="@Model.PatientId" data-office-id="@Model.OfficeId">Contact Manager</a></li>
                        <li><a class="@refDocumentLink @refDocumentColor" data-app-id="@Model.Id" data-modal-url="@Url.Action("getreferraldocuments", "uploads", new { area = "documents", appointmentId =  Model.Id })" href="#">RD</a></li>
                        @if (Model.IseReferral)
                        {
                            <li><a href="#" class="appointment-reject" data-modal-url="@Url.Action("Reject", "appointments", new { area = "schedule", appointmentId = Model.Id,triagetobook=true })">Reject Appointment</a></li>
                            <li><a href="#" class="appointment-additionalInfo" data-modal-url="@Url.Action("RequestAdditionalInfo", "appointments", new { area = "schedule", appointmentId = Model.Id})">Request Additional Info</a></li>
                        }
                    </ul>
                </div>
            </div>
</td>          
            