@model Cerebrum.ViewModels.Triage.VMAppTriageDisMain

@{ 
    var maxColumns = 4;
    var totalItems = Model.TriageDispositions.Count();
    var totalPerColumn = ((int) (totalItems / maxColumns) +1);   
    var htmlContent = "";
    
}


@using (Html.BeginForm("savetriagedispositions", "waitlist", new { area = "triage" }, FormMethod.Post, true, new { @id = "frm-triage-dispositions" }))
{    
    @Html.ModalHeader("Triage Dispositions", Model.PatientFullName)
    <div class="modal-body">
        @*@(await Html.PartialAsync("_validationSummary"))*@
        @Html.HiddenFor(model => model.AppointmentId)
        @Html.HiddenFor(model => model.PatientId)
        @Html.HiddenFor(model => model.PatientFullName)
        <div class="form-horizontal">
            <div class="form-group form-group-sm">
                @for (int i = 0; i < Model.TriageDispositions.Count(); i++)
                {
                    if(i % totalPerColumn == 0 )// start new column
                    {
                        if(i > 0)
                        {
                            htmlContent += string.Format("</div>");
                        }
                        htmlContent += string.Format("<div class=\"col-md-3\">");                       
                    }

                    htmlContent += Html.EditorFor(model => Model.TriageDispositions[i], "_appTriageDisposition", String.Format("{0}[{1}]", "TriageDispositions", i)).ToHtmlString();

                    if (totalItems == (i + 1))
                    {
                        htmlContent += string.Format("</div>");
                    }                   
                }
                @Html.Raw(htmlContent)
            </div>
        </div>
    </div>
    @Html.ModalFooter("Save", "blue")
}



