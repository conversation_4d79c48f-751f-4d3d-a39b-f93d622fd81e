﻿@model Cerebrum.ViewModels.Triage.VMAppTriageDisposition

@Html.HiddenFor(model => model.Id)
@Html.HiddenFor(model => model.AppointmentId)
@Html.HiddenFor(model => model.UserId)
@Html.HiddenFor(model => model.TriageDispositionId)
@Html.HiddenFor(model => model.TriageDispositionDesc)

<div class="checkbox">
    <label>
        @Html.EditorFor(model => model.IsSelected) <span class="checkbox-text">@Model.TriageDispositionDesc</span>
    </label>
    @Html.ValidationMessageFor(model => model.IsSelected, "", new { @class = "text-danger" })
</div>
