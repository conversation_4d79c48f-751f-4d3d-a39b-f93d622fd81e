@model Cerebrum30.Areas.Daysheet.Models.AppCreateEditModel



<label>Appointment Edit ID = @Request["EditAppId"]</label>
@Scripts.Render("~/bundles/jquery")
<link rel="stylesheet" href="Content/jquery-ui-1.12.1.min.css" />
<link href="~/Content/cerebrum3-appointment-btns.css" rel="stylesheet" />
<style type="text/css">
    .btn-selected {
        background-color: lightgreen;
    }

    input, select, textarea {
        max-width: 100%;
    }

    .form-group {
        margin-bottom: 6px;
    }

    .clearable-button {
        position: absolute;
        right: 20px;
        top: 0;
        bottom: 0;
        height: 14px;
        padding-top: 12px;
        margin: auto;
        font-size: 14px;
        cursor: pointer;
        color: #ccc;
    }
</style>

<script type="text/javascript">
    var selectedTests = new Array();
    var inits = new Array();
    $(function () {
        $('#btnAppointment').on("click", function (e) {
            $("#patientName").val($("#patientNameInput").val());
            $("#ReferralDoctorName").val($("#ReferralDoctorInput").val());
            $("#prDocIndex").val($("#prDocIndex").val());
            $("#appNewTests").val(JSON.stringify(selectedTests));
        });

        inits = GetInitTests();
        $("#ReferralDoctorInput").autocomplete({
            source: function (request, response) {
                $.post("Appointments/GetReferralDoctors", request, response);
            },
            minLength: 3,
            delay: 25,
            select: function (event, ui) {
                ReferralDoctorSelect(ui);
                return false;
            }
        });
        $(".clearable").keyup(function () { $(this).next().toggle(Boolean($(this).val()); });
        $(".clearable-button").toggle(Boolean($(".clearable").val());
        $(".clearable-button").click(function () { $(this).prev().val("").focus(); $(this).hide(); });
    });
    function GetInitTests() {
        var initselTests = new Array();
        $(".btn-selected").each(function (i) {
            var index = jQuery.inArray($(this).attr('id'), initselTests);
            if (index == -1) {
                initselTests.push($(this).attr('id'));
                selectedTests.push($(this).attr('id'));
            }
        });
        return initselTests;
    };

    $(document).on("click", function (e) {
        //Edit the list of the tests
        var index = -5;
        var id = e.target.id;
        if (id.indexOf("EditTestLbl") >= 0) {
            var innerText = e.target.innerText;//delete
            inits.forEach(function (init) {
                var i = jQuery.inArray(init, selectedTests);//delete
                if (jQuery.inArray(init, selectedTests) == -1 && init != id) {
                    selectedTests.push(init);
                }
            });
            e.target.style.backgroundColor = 'lightgreen';
            index = jQuery.inArray(id, inits);// is predefined?
            if (index > -1) {
                //yes=>check it.
                var ind = jQuery.inArray(id, selectedTests);
                if (ind >= 0) {
                    e.target.style.backgroundColor = 'white';
                    index = selectedTests.splice(index, 1);
                    var it = jQuery.inArray(id, inits);
                    if (it >= 0) {
                        inits.splice(it, 1);
                    }
                } else {
                    selectedTests.push(id);
                }
            } else {
                //no
                index = jQuery.inArray(id, selectedTests);
                if (index > -1) {
                    selectedTests.splice(index, 1);
                    e.target.style.backgroundColor = 'white';
                } else {
                    selectedTests.push(id);
                }
            }
            var dataToSend = JSON.stringify(selectedTests);
            $("#appNewTests").val(dataToSend);
        }
    });

    $('#OhipOrName').on("keypress", function (e) {
        if (e.keyCode == 13) {
            var ohipornametxt = $(this).val();
            OHIP(ohipornametxt);
            e.preventDefault();
        }
    });
    $('#OhipOrName').focusout(function () {
        var ohipornametxt = $(this).val();
        OHIP(ohipornametxt);
    });

    function ReferralDoctorSelect(ui) {
        $("#ReferralDoctorName").val(ui.item.label);
        $("#ReferralDoctorInput").val(ui.item.value);
    }
</script>

@using (Html.BeginForm(null, null, FormMethod.Post, new { }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.appNewTests)
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <div class="row">
        <div class="col-xs-2">
            <div class="form-group">
                @Html.Label("Patient:", "", new { @class = "control-label col-md-2" })
                <div class="col-sm-12">
                    @{
                        @Html.Hidden("patientName", @Model.PatientName)
                        @Html.TextBox("patientNameInput", @Model.PatientName, new { @class = "form-control clearable" })
                        @*<span class="clearable-button glyphicon glyphicon-remove-circle"></span>*@
                    }
                    @Html.HiddenFor(model => model.Id)
                    @Html.HiddenFor(model => model.PatientRecordId)
                    @Html.ValidationMessage("OhipOrName", "", new { @class = "text-success" })
                    @Html.ValidationMessage("OhipExpiry", "", new { @class = "text-success" })
                    @Html.ValidationMessageFor(model => model.PatientRecordId, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="col-xs-3">
            @Html.Label("Office:", "", new { @class = "control-label col-md-2" })
            @Html.DropDownListFor(model => model.OfficeId, Model.Offices, "", new { @class = "form-control" })
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                @Html.Label("Practice Doctors", "", new { @class = "control-label col-md-12" })
                <div class="col-md-12">
                    @Html.DropDownListFor(model => model.prDocIndex, Model.PracticeDoctors, "", new { @class = "form-control" })
                </div>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                @Html.LabelFor(model => model.DayTime, htmlAttributes: new { @class = "control-label col-md-5" }, new {})
                @Html.EditorFor(model => model.DayTime, new { htmlAttributes = new { @class = "form-control" } })
            </div>
        </div>
        <div class="col-xs-3">
            <div class="form-group">
                @Html.Label("Referral Doctors", "", new { @class = "control-label col-md-7" })
                <div class="col-md-11">
                    @Html.Hidden("ReferralDoctorName", @Model.ReferralDoctorName)
                    @Html.TextBox("ReferralDoctorInput", @Model.ReferralDoctorName, new { @class = "form-control clearable" })
                    <span class="clearable-button glyphicon glyphicon-remove-circle"></span>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-2">
            <div class="form-group">
                @Html.LabelFor(model => model.AppTypes, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                <div class="col-md-11">
                    @Html.DropDownListFor(model => model.AppTypeIndex, Model.AppTypes, "", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.AppTypes, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                @Html.LabelFor(model => model.PaymentMethod, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                <div class="col-md-11">
                    @Html.EnumDropDownListFor(model => model.PaymentMethod, htmlAttributes: new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.PaymentMethod, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">

                @Html.LabelFor(model => model.Notes, htmlAttributes: new { @class = "control-label col-md-2" }, new {})
                <div class="col-md-12">
                    @Html.TextAreaFor(model => model.Notes, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Notes, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-4">
            <div class="form-group">
                <label class="control-label" for="tests">Tests:</label>
                <br />
                <div class="btn-group" id="tests" data-toggle="buttons">
                    @foreach (var t in Model.AppTests)
                    {
                        bool flag = true;
                        string tid = "EditTestLbl" + t.PracticeTestId.ToString();
                        foreach (var s in Model.EditTests)
                        {
                            if (t.PracticeTestId == s.PracticeTestId)
                            {
                                <label id=@tid class="btn btn-md btn-default  btn-selected">  @t.testShortName.Trim() </label>
                                flag = false;
                            }
                        }
                        if (flag)
                        {
                            <label id=@tid class="btn btn-md btn-default">  @t.testShortName.Trim() </label>
                        }
                    }
                </div>

            </div>
        </div>
        <div class="col-lg-4  pull-right">
            <div class="form-group">
                <label class="control-label" for="tests">Pre-conditions:</label>
                <br />
                    <div class="btn-group" id="AppPreConditions" data-toggle="buttons">
                        @foreach (var pre in ViewBag.PreConditions)
                        {
                            var selected = pre.Selected ? "active" : "";
                            <label class="btn btn-md btn-preconditions @selected">
                                <input type="checkbox" autocomplete="off" data-value="@pre.Text" checked="@pre.Selected" name="PreConditions" value="@pre.Text"> @pre.Text
                            </label>
                        }
                </div>
            </div>
        </div>

    </div>
    <div class="row">
        <div class="col-xs-2">
            <div class="form-group">
                <div class="col-md-11">
                    <br>
                </div>
                <div class="btn-group" data-toggle="buttons" id="appBookingConfirmation">

                    <label class="btn btn-actionable @(Model.bookingConfirmation ? "active" : "")">
                        @Html.CheckBoxFor(model => model.bookingConfirmation)Send Booking Confirmation
                    </label>
                    <div class="col-md-11">
                    </div>
                </div>
                
                <div class="col-md-11">
                </div>
            </div>
        </div>
        <div class="col-xs-1">
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                <div class="col-md-11">
                    <br>
                </div>
                <div class="btn-group" data-toggle="buttons" id="AppActionOnAbnormal">
                    
                    <label class="btn btn-actionable @(Model.actionOnAbnormal ? "active" : "")">
                        @Html.CheckBoxFor(model => model.actionOnAbnormal)Action on Abnormal
                    </label>
                    <div class="col-md-11">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4 pull-right">
            <div class="form-group">
                @Html.HiddenFor(model => model.Purpose)
                @Html.HiddenFor(model => model.Registrar)
                @Html.LabelFor(model => model.appointmentChangeReason, htmlAttributes: new { @class = "control-label" }, new {})
                <br />
                    @Html.TextAreaFor(model => model.appointmentChangeReason, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.appointmentChangeReason, "", new { @class = "text-danger" })
                <br />
                <br />
                
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-2 pull-right">
            <input type="submit" value="Update Appointment" id="btnAppointment" class="btn btn-primary" />
        </div>
    </div>
                        }
