@model Cerebrum30.Areas.Daysheet.Models.AppCreateEditModel
<link rel="stylesheet" href="Content/jquery-ui-1.12.1.min.css" />

<style type="text/css">
    input, select, textarea {
        max-width: 100%;
    }

    .form-group {
        margin-bottom: 6px;
    }

    .clearable-button {
        position: absolute;
        right: 20px;
        top: 0;
        bottom: 0;
        height: 14px;
        padding-top: 12px;
        margin: auto;
        font-size: 14px;
        cursor: pointer;
        color: #ccc;
    }
</style>

<script type="text/javascript">
    var selectedTests = new Array();
    $(document).ready(function () {
        $('[id^="testLbl"]').click(function (e) {
            var id = jQuery(this).attr("id");
            var innerText = e.innerText;//delete
            $(this).css('background-color', 'lightgreen');
            var index = jQuery.inArray(id, selectedTests);
            if (index > -1) {
                selectedTests.splice(index, 1);
                $(this).css('background-color', 'white');
            } else {
                selectedTests.push(id);
            }
            var dataToSend = JSON.stringify(selectedTests);
            $("#appNewTests").val(dataToSend);
        });

        $("#patientNameInput").autocomplete({
            source: function (request, response) {
                $.post("daysheet/Appointments/GetPatientList", request, response);
            },
            minLength: 3,
            delay: 25,
            select: function (event, ui) {
                patientSelect(ui);
                return false;
            }
        });

        $("#ReferralDoctorInput").autocomplete({
            source: function (request, response) {
                $.post("daysheet/Appointments/GetReferralDoctors", request, response);
            },
            minLength: 3,
            delay: 25,
            select: function (event, ui) {
                ReferralDoctorSelect(ui);
                return false;
            }
        });

        $(".clearable").keyup(function () { $(this).next().toggle(Boolean($(this).val()); });
        $(".clearable-button").toggle(Boolean($(".clearable").val());
        $(".clearable-button").click(function () { $(this).prev().val("").focus(); $(this).hide(); });

        $('#btnAppointment').on("click", function (e) {
            $("#patientName").val($("#patientNameInput").val());
            $("#ReferralDoctorName").val($("#ReferralDoctorInput").val());
        });
    });

    $('#OhipOrName').on("keypress", function (e) {
        if (e.keyCode == 13) {
            var ohipornametxt = $(this).val();
            OHIP(ohipornametxt);
            e.preventDefault();
        }
    });
    $('#OhipOrName').focusout(function () {
        var ohipornametxt = $(this).val();
        OHIP(ohipornametxt);
    });

    function patientSelect(ui) {
        $("#patientName").val(ui.item.label);
        $("#patientNameInput").val(ui.item.label);
        $.ajax({
            method: 'GET',
            url: "/daysheet/Appointments/GetPatientDefaults",
            data: { patName: ui.item.label },
            async: false,
            success: function (data) {
                $("#ReferralDoctorInput").val(data.refDoctor);
                $("#practiceDocIndex").val(data.apdoctor);
                $("#PaymentMethod").val(data.appPayMethod);
            },
            error: function (xhr, Error) {
                alert("Error while tryng to call  '/daysheet/Appointments/GetPatientDefaults'  " + xhr.status + " " + Error);
            }
        });
    }
    function ReferralDoctorSelect(ui) {
        $("#ReferralDoctorName").val(ui.item.label);
        $("#ReferralDoctorInput").val(ui.item.value);
    }
</script>

@using (Html.BeginForm(null, null, FormMethod.Post, new { }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.appNewTests)
    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
    <div class="row">
        <div class="col-xs-2">
            <div class="form-group">
                @Html.Label("Patient:", htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-sm-12">
                    @{
//var demo = Model != null ? Model.PatientRecord.Demographics.FirstOrDefault() : null;
                        @*<input type="text" class="form-control" name="OhipOrName" id="OhipOrName" value="@(demo!=null? demo.lastName+" ,"+demo.firstName:"")" />*@
//<input type="text" class="form-control" name="OhipOrName" id="OhipOrName" placeholder="Ohip Or Name" />
                        @*@Html.EditorFor(model => model.PatientName, new {
                            htmlAttributes = new { @class = "form-control" } })*@
                        @Html.Hidden("patientName", @Model.PatientName)
                        @Html.TextBox("patientNameInput", @Model.PatientName, new { @class = "form-control clearable" })
                        <span class="clearable-button glyphicon glyphicon-remove-circle"></span>
                    }
                    @Html.HiddenFor(model => model.Id)
                    @Html.HiddenFor(model => model.PatientRecordId)
                    @Html.ValidationMessage("OhipOrName", "", new { @class = "text-success" })
                    @Html.ValidationMessage("OhipExpiry", "", new { @class = "text-success" })
                    @Html.ValidationMessageFor(model => model.PatientRecordId, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="col-xs-3">
            @Html.Label("Office:", htmlAttributes: new { @class = "control-label col-md-2" })
            @*@Html.EditorFor(model => model.Offices, new { htmlAttributes = new { @class = "form-control" } })*@
            @Html.DropDownListFor(model => model.OfficeId, Model.Offices, htmlAttributes: new { @class = "form-control" })

        </div>
        <div class="col-xs-2">
            <div class="form-group">
                @Html.Label("Practice Doctors", htmlAttributes: new { @class = "control-label col-md-12" })
                <div class="col-md-12">
                    @Html.DropDownListFor(model => model.practiceDocIndex, Model.PracticeDoctors, htmlAttributes: new { @class = "form-control" })
                    @*<input type="text" class="form-control" name="Doctor" id="Doctor" />*@
                    @*@Html.Hidden("ExternalDoctorId")*@
                    @*@Html.ValidationMessageFor(model => model.appointmentProviders, "", new { @class = "text-danger" })*@
                </div>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                @*@Html.LabelFor(model => model.Time, htmlAttributes: new { @class = "control-label col-md-5" })
                    <div class="col-md-12 input-append date">
                        @Html.EditorFor(model => model.appointmentTime, new { htmlAttributes = new { @class = "form-control  input-append date", data_format = "MM/dd/yyyy HH:mm:ss PP" } })
                        <span class="add-on">
                            <i data-time-icon="icon-time" data-date-icon="icon-calendar">
                            </i>
                        </span>
                        @Html.ValidationMessageFor(model => model.appointmentTime, "", new { @class = "text-danger" })
                    </div>*@
                @Html.LabelFor(model => model.DayTime, htmlAttributes: new { @class = "control-label col-md-5" })
                @Html.EditorFor(model => model.DayTime, new { htmlAttributes = new { @class = "form-control" } })
            </div>
        </div>
        <div class="col-xs-3">
            <div class="form-group">

                @Html.Label("Referral Doctors", htmlAttributes: new { @class = "control-label col-md-7" })
                <div class="col-md-11">

                    @Html.Hidden("ReferralDoctorName", @Model.ReferralDoctorName)
                    @Html.TextBox("ReferralDoctorInput", @Model.ReferralDoctorName, new { @class = "form-control clearable" })
                    <span class="clearable-button glyphicon glyphicon-remove-circle"></span>

                    @*@Html.DropDownListFor(model => model.ReferralDoctorIndex, Model.ReferralDoctors, htmlAttributes: new { @class = "form-control" })*@

                    @*@Html.HiddenFor(model => model.ReferralDoctorId)*@
                    @*<select name="referralDoctorList" id="referralDoctorList" class="form-control"></select>*@
                    @*@Html.ValidationMessageFor(model => model.ReferralDoctors, "", new { @class = "text-danger" })*@
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-2">
            <div class="form-group">

                @Html.LabelFor(model => model.AppTypes, htmlAttributes: new { @class = "control-label col-md-2" })
                <br />
                @Html.DropDownListFor(model => model.AppTypeIndex, Model.AppTypes, htmlAttributes: new { @class = "form-control" })
                @*@Html.DropDownList("AppointmentTypes", null, htmlAttributes: new { @class = "form-control" })*@
                @Html.ValidationMessageFor(model => model.AppTypes, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                @Html.LabelFor(model => model.PaymentMethod, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-11">
                    @Html.EnumDropDownListFor(model => model.PaymentMethod, htmlAttributes: new { @class = "form-control" })

                    @Html.ValidationMessageFor(model => model.PaymentMethod, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                @Html.LabelFor(model => model.Notes, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-12">
                    @Html.TextAreaFor(model => model.Notes, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Notes, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-4">
            <div class="form-group">
                <label class="control-label" for="tests">Tests:</label>
                <br />
                <div class="btn-group" id="tests" data-toggle="buttons">
                    @foreach (var t in Model.AppTests)
                    {
                        string tid = "testLbl" + t.PracticeTestId.ToString();
                        <label id=@tid class="btn btn-md btn-default ">  @t.testShortName </label>
                    }
                </div>
            </div>
        </div>
        <div class="col-md-4  pull-right">
            <div class="form-group">
                <label class="control-label" for="tests">Pre-conditions:</label>
                <br />
                <div class="btn-group" id="AppPreConditions" data-toggle="buttons">
                    @foreach (var pre in ViewBag.PreConditions)
                    {
                        <label class="btn btn-md btn-preconditions">
                            <input type="checkbox" autocomplete="off" data-value="@pre.Value" name="PreConditions" value="@pre.Value"> @pre.Text
                        </label>
                    }
                </div>
            </div>
        </div>

    </div>
    <div class="row">

        <div class="col-xs-2">
            <div class="form-group">
                <div class="col-md-11">
                    <br>
                </div>
                <div class="btn-group" data-toggle="buttons" id="appBookingConfirmation">
                    <label class="btn btn-actionable">
                        @Html.CheckBoxFor(model => model.bookingConfirmation)Send Booking Confirmation
                    </label>
                    <div class="col-md-11">
                    </div>
                </div>
              
                <div class="col-md-11">
                </div>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="form-group">
                <div class="col-md-11">
                    <br>
                </div>
                <div class="btn-group" data-toggle="buttons" id="AppActionOnAbnormal">
                    <label class="btn btn-actionable">
                        @Html.CheckBoxFor(model => model.actionOnAbnormal)Action on Abnormal
                    </label>
                    <div class="col-md-11">
                    </div>
                </div>
            </div>
            <div class="col-xs-15; text-right">
                <div class="form-group">
                    @Html.HiddenFor(model => model.Purpose)
                    @Html.HiddenFor(model => model.Registrar)
                    @Html.HiddenFor(model => model.roomNumber)
                    <br />
                    <div class="col-md-offset-2 col-md-10">

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-2 pull-right">
            <input type="submit" value="New Appointment" id="btnAppointment" class="btn btn-primary" />
        </div>
    </div>

                        }
