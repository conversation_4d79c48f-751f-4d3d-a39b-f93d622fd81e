@model Cerebrum.Data.AppointmentData
@using Cerebrum30.Utility
@using System.ComponentModel
<tbody id="@("appointment_" + Model.Id)" class="togglebox">
    <tr>
        <td>
            @{string apptime = Model.Time.ToShortTimeString();} @*Time*@
            @apptime
            <br />
            <div class="RoomEditable" id="<EMAIL>" data-pk="@Model.Id" data-original-title="Room Number" data-value="@Model.roomNumber" data-name="Room Number">
                Room #
                @Model.roomNumber
                <span class="glyphicon glyphicon-pencil"></span>
            </div>
        </td>
        <td id="status" class="@(Model.appointmentStatus.ToString())">
            @{
                <div class="row">
                    <div class="col-xs-1">
                        <a href="/daysheet/appointments/change/@Model.Id"> <img src="~/Content/Images/A-user.png" alt="" width="17" height="19" /></a>
                    </div>
                    <div class="col-xs-1"><img src="~/Content/Images/A-comment.png" alt="" width="16" height="19" /></div>
                    <div class="col-xs-1"><img src="~/Content/Images/A-calender.png" alt="" width="17" height="19" /></div>
                    <div class="col-xs-1"><img src="~/Content/Images/A-info.png" alt="" width="16" height="19" data-toggle="tooltip" title="@Model.AppModifiers" /></div>
                </div>
            }
            @Html.TextBox("Arrived" + @Model.Id, @Model.ArrivedTime, new { style = "width:90px", @maxlength = "10", @class = "form-control" })
            <br />
            @Html.TextBox("Left" + @Model.Id, @Model.LeftTime, new { style = "width:90px", @maxlength = "10", @class = "form-control" })
        </td>
        <td>
            <div class="btn-group btn-group-sm btn.sharp" role="group" id="AppPreConditions" data-toggle="buttons">
                @foreach (var t in Model.Tests)
                {
                    //string tid = "testLbl" + t.PracticeTestId.ToString();

                    <a href="/Measurements/Measurement?AppointmentID=@Model.Id&TestID=@t.PracticeTestId">
                        <label class="btn btn-md btn-default ">  @t.testShortName <br />  @DateTime.Parse(t.started.ToString()).ToString("HH:mm")</label>
                    </a>

                    @*@Html.ActionLink("WorkSheet", "Index", "Measurement", new { area = "Measurements", AppointmentID = @Model.Id, TestID = @t.Id }, null)*@
                }
            </div>
            <br />
            @if (Model.appPreconditions.Count() > 0)
            {
                <div class="btn-group btn-group-sm btn.sharp" role="group" id="AppPreConditions" data-toggle="buttons">
                    @foreach (var pre in Model.appPreconditions)
                {
                    var selected = pre.status ? "active" : "";
                        <label class="btn btn-md btn-preconditions @selected" onclick="javascript:preConditionStatusChange(@Model.Id, @pre.Id);">
                            <input type="checkbox" autocomplete="off" data-value="@pre.Id" name="PreConditions" value="@pre.type"> @pre.type
                        </label>
                    }
                </div>

            }
            <br />
            @if (Model.actionOnAbnormal)
            {
                <div class="btn-group" data-toggle="buttons" id="AppActionOnAbnormal">
                    <label class="btn btn-md btn-actionable active">
                        <input type="checkbox" autocomplete="off" data-value="@Model.actionOnAbnormal" name="PreConditions" value="@Model.actionOnAbnormal"> Action on Abnormal
                    </label>
                </div>

            }
        </td>
        <td>
            <a href="@Model.MWLUrl">MWL</a> <br />
            RD
        </td>

        <td>
            @Model.appProvider<br />
            @*@Model.patientDemo.Patient*@
            @Html.ActionLink(Model.patientDemo.Patient, "NewPatientEdit", "DemographicsGB", new { area = "", demographicId = Model.patientDemo.Id }, null)
            <br>
            @Model.Type
        </td>
        <td>
            @Html.ActionLink("VP", "Index", "VP",
                new { area = "VP", AppointmentID = Model.Id, PatientID = Model.PatientRecordId }, null)
        </td>
        <td>
            @Model.ConfirmationStr<br>
            @Model.PaymentMethodStr
        </td>

        <td>
            @Model.Notes
        </td>
        <td>
            <a target="_blank" href='@Url.Action("EditDoctor", "Doctor", new {area = "Daysheet", doctorID=@Model.referralDoctorId,Name=Model.ReferralDoctor}  )'>
                @Model.ReferralDoctor
            </a>
        </td>
    </tr>
    <tr style="border-top: 1px solid #ffffff">
        <td class="appStatus" id="@Model.Id"></td>
        <td class="@(Model.appointmentStatus.ToString())">
            @{
                var desc = Model.appointmentStatus.GetAttributeValue<DescriptionAttribute, string>(x => x.Description);
                @(desc != null ? desc : Model.appointmentStatus.ToString())
            }
        </td>
        <td bgcolor="#FDEDEE" colspan="2">ACTIONABLE:</td>
        <td bgcolor="#FDEDEE" style="font-size: 14px; color: #F00;">
            @if (Model.patientActionable.Count() > 0)
            {
                string actionable = string.Join(", ", Model.patientActionable.ToArray());
                @actionable
            }
        </td>
        <td bgcolor="#FDEDEE" style="font-size: 14px; color: #F00;"></td>

        <td bgcolor="#FDEDEE" style="font-size: 14px; color: #F00;"></td>
        <td bgcolor="#FDEDEE" style="font-size: 14px; color: #F00;"></td>
        <td bgcolor="#FDEDEE" style="font-size: 14px; color: #F00;" colspan="3">
            @if (Model.doctorActionable.Count() > 0)
            {
                string actionable = string.Join(", ", Model.doctorActionable.ToArray());
                @actionable
            }
        </td>


    </tr>
</tbody>