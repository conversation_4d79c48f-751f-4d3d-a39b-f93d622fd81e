﻿@model Cerebrum.ViewModels.Reminder.ReminderResponse

<script type="text/javascript" src="~/Areas/Reminder/Scripts/EmailReminder.js"></script>

<h2>Email <PERSON>minder</h2>
<hr />
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Office</label></div>
    <div class="col-sm-3">
        @if (Model.offices.Count() == 1)
        {
            @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), htmlAttributes: new { @class = "form-control input-sm" })
        }
        else
        {
            @Html.DropDownList("officeId", new SelectList(Model.offices, "value", "text"), "Please Select Office", htmlAttributes: new { @class = "form-control input-sm" })
        }
    </div>
</div>
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Subject</label></div>
    <div class="form-group col-sm-6">
        <input class="form-control" id="subject" name="subject" type="text" value="">
    </div>
</div>
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label title="">Body</label></div>
    <div class="col-sm-6">
        <textarea id="body" name="body" rows="12" class="form-control"></textarea>
    </div>
    <div class="col-sm-4">
        <span style="font-weight: bold;">Keywords:</span><br />
        {{patientFirstName}} <span class="text-danger">*</span><br />
        {{patientLastName}} <span class="text-danger">*</span><br />
        {{doctorFirstName}}<br />
        {{doctorLastName}}<br />
        {{appointmentDate}}<br />
        {{appointmentTime}} <span class="text-danger">*</span><br />
        {{officeName}} <span class="text-danger">*</span><br />
        {{officeFullName}} <span class="text-danger">*</span><br />
        {{officeAddress}} <span class="text-danger">*</span><br />
        {{testName}}<br />
        {{testFullName}}<br />
        {{testInstruction}} <span class="text-danger">*</span><br />
        {{ClickConfirmedLinkToConfirm}} <span class="text-danger">*</span><br />
        {{multipleAppointmentsPerDay}} <span class="text-danger">*</span>
        <hr />
        <span class="text-danger">*</span> only these keywords can be used if {{multipleAppointmentsPerDay}} is applied
    </div>
</div>
<hr />
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Schedule Email 1</label></div>
    <div class="col-sm-1">
        <input class="form-control" id="schedule" name="schedule" type="text" value="">
    </div>
    <div class="col-sm-6">
        day(s) before appointment
    </div>
</div>
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Schedule Email 2</label></div>
    <div class="col-sm-1">
        <input class="form-control" id="schedule2" name="schedule2" type="text" value="">
    </div>
    <div class="col-sm-6">
        day(s) before appointment
    </div>
</div>
<hr />
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Mail Server's URL</label></div>
    <div class="col-sm-3">
        <input class="form-control" id="mailServerUrl" name="mailServerUrl" type="text" value="">
    </div>
</div>
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Mail Server's Port</label></div>
    <div class="col-sm-3">
        <input class="form-control" id="mailServerPort" name="mailServerPort" type="text" value="">
    </div>
</div>
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Username</label></div>
    <div class="col-sm-3">
        <input class="form-control" id="userName" name="userName" type="text" value="">
    </div>
</div>
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Password</label></div>
    <div class="col-sm-3">
        <input class="form-control" id="email-reminder-password" name="password" type="password" value="" autocomplete="new-password">
    </div>
</div>
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>Confirm Password</label></div>
    <div class="col-sm-3">
        <input class="form-control" id="email-reminder-password-confirmation" name="password" type="password" value="" autocomplete="new-password">
    </div>
</div>
<div class="form-group col-sm-12">
    <div class="col-sm-2"><label>SSL Connection</label></div>
    <div class="col-sm-3">
        <input id="enableSsl" name="enableSsl" type="checkbox">
    </div>
</div>
<hr />
<br />
<div class="form-group col-sm-12">
    <button type="button" class="btn btn-default btn-sm btn-primary" id="buttonEmailReminderSave" name="buttonEmailReminderSave">Save</button>
</div>
<br />
<br />
<br />
<br />
<br />