﻿@model Cerebrum.ViewModels.RadDicom.VMAttachReattachDicomExam

@using (Html.BeginForm("AttachReattachDicomExam", "Admin", new { area = "Dicom" }, FormMethod.Post, true, new { @id = "frm-reattach-dicom-exam" }))
{
    @Html.AntiForgeryToken()

    <div class="form-inline">

        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.StudyId, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.StudyId, new { htmlAttributes = new { @class = "form-control" , @readonly = "readonly" } })
            @Html.ValidationMessageFor(model => model.StudyId, "", new { @class = "text-danger" })
        </div>

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.RemovedAccession, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.RemovedAccession, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.RemovedAccession, "", new { @class = "text-danger" })
        </div>

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.AddToAccession, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.AddToAccession, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.AddToAccession, "", new { @class = "text-danger" })
        </div>

        <div class="form-group form-group-sm">
            <div class="col-md-offset-2">
                <input type="submit" value="Attach" class="btn btn-default btn-sm btn-attach-reattach" />
            </div>
        </div>
        <div class="checkbox" style="margin-left:10px">
            <label><span id="reattach-accession-message"></span></label>
        </div>
    </div>
}
