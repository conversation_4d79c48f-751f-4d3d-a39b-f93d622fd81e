﻿@using (Html.BeginForm("UploadSRFile", "Admin", new { area = "DICOM" }, FormMethod.Post, true, new { @enctype = "multipart/form-data", @id = "frm-dicom-sr-upload" }))
            {
    <div class="form-group form-group-sm">
        @Html.Label("File Name", htmlAttributes: new { @class = "control-label" })

        <input type="file" name="file" id="file" class="btn btn-default" />
    </div>
    <div class="form-group form-group-sm">
        <br />
        <input class="btn btn-default" type="submit" value="Upload" />
    </div>


}
<hr />

