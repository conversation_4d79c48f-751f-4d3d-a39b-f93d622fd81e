﻿@model Cerebrum.ViewModels.RadDicom.VMRadDicomPatientSearch

@using (Html.BeginForm("FindDicomExam", "Admin", new { area = "Dicom" }, FormMethod.Post, true, new { @id = "frm-dicom-exam-search" }))
{
    @Html.AntiForgeryToken()

    <div class="form-inline">

        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.AccessionNumber, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.AccessionNumber, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.AccessionNumber, "", new { @class = "text-danger" })
        </div>

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.ExamDate, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.ExamDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
            @Html.ValidationMessageFor(model => model.ExamDate, "", new { @class = "text-danger" })
        </div>

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.Patient, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.Patient, new { htmlAttributes = new { @class = "form-control", placeholder = "LastName,FirstName" } })
            @Html.ValidationMessageFor(model => model.Patient, "", new { @class = "text-danger" })
            @Html.HiddenFor(model=>model.RadPatientId)
        </div>
     
        <div class="form-group form-group-sm">
            <div class="col-md-offset-2">
                @Html.HiddenFor(model=>model.PatientDOB)
                <input type="submit" value="Search" class="btn btn-sm btn-default btn-rad-dicom-srch" />                
            </div>
        </div>
    </div>
}
<hr />
<div id="rad-dicom-study-srch"></div>