﻿@using (Html.BeginForm("ImportDicomMeasurements", "Admin", new { area = "Dicom" }, FormMethod.Post, true, new { @id = "frm-import-dicom-exam" }))
{
    @Html.AntiForgeryToken()

    <div class="form-inline">
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group form-group-sm">
            @Html.Label("Accession Number", htmlAttributes: new { @class = "control-label" })
            @Html.Editor("AccessionNumber", new { htmlAttributes = new { @class = "form-control" } })
        </div>
        <div class="form-group form-group-sm">
            <div class="col-md-offset-2">
                <input type="submit" value="Import" class="btn btn-sm btn-default btn-import-dicom-exam" />
            </div>
        </div>
        <div class="form-group form-group-sm">
            <div class="col-md-1"></div><br />
        </div>
        <div class="form-group form-group-sm">
            <div id="msg-import-dicom-exam" class="clearfix float_r "></div>
        </div>
    </div>
}
<hr />

