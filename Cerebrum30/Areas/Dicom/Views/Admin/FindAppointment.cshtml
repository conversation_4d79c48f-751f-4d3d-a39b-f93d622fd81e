﻿@model Cerebrum.ViewModels.RadDicom.VMPatientAppointmentSearch

@using (Html.BeginForm("FindAppointment", "Admin", new { area = "Dicom" }, FormMethod.Post, true, new { @id = "frm-patient-appointment-search" }))
{
    @Html.AntiForgeryToken()
    
    <div class="form-inline">
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.AccessionNumber, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.AccessionNumber, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.AccessionNumber, "", new { @class = "text-danger" })
        </div>

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.TestDate, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.TestDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
            @Html.ValidationMessageFor(model => model.TestDate, "", new { @class = "text-danger" })
        </div>

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.Patient, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.Patient, new { htmlAttributes = new { @class = "form-control", placeholder = "LastName,FirstName" } })
            @Html.ValidationMessageFor(model => model.Patient, "", new { @class = "text-danger" })
            @Html.HiddenFor(model => model.PatientId)
        </div>
        <div class="form-group form-group-sm">
            <div class="col-md-offset-2 col-md-10">
                @Html.HiddenFor(model => model.PatientDOB)
                <input type="submit" value="Search" class="btn btn-default btn-sm btn-patient-appointment-srch" />
            </div>
        </div>
    </div>
}
<hr />
<div id="patient-appointments-srch"></div>
