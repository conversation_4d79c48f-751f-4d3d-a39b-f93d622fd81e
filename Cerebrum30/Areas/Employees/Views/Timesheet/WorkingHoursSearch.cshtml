@model Cerebrum.ViewModels.Timesheet.VMWorkingHoursSearch

@using (Html.BeginForm("OfficeUsersWorkingHours", "Timesheet", new { area = "Employees" }, FormMethod.Post, true, new { @id = "frm-worked-hours-search" }))
{
    @Html.AntiForgeryToken()

    <div class="form-inline">
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group">
            @Html.LabelFor(model => model.OfficeId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.OfficeId, new SelectList(Model.Offices, "Id", "OfficeName"), "Choose One...", new { @class = "form-control office-selection", data_url = "" })
                @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.SearchUserId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @*@Html.ListBoxFor(model => model.SearchUserId, new MultiSelectList(Model.Users, "UserId", "FullNameType"), new { @class = "form-control office-selection", data_url = "" })*@
                @Html.DropDownListFor(model => model.SearchUserId, new MultiSelectList(Model.Users, "UserId", "FullNameType"), "Choose One...", new { @class = "form-control office-selection", data_url = "" })
                @Html.ValidationMessageFor(model => model.SearchUserId, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.FromDate, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.FromDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
                @Html.ValidationMessageFor(model => model.FromDate, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ToDate, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.ToDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
                @Html.ValidationMessageFor(model => model.ToDate, "", new { @class = "text-danger" })
            </div>
        </div>

        @*<div class="row" style="margin-bottom: 8px;">*@
        <div class="form-group">
            <div class="col-md-10">
                @Html.RadioButtonFor(x => x.ReportType, AwareMD.Cerebrum.Shared.Enums.TimesheetReportType.EachDayTotal, new { @checked = true })
                @Html.Label("Each Day Total", "", new { @class = "control-label" })
            </div>
            <div class="col-md-10">
                @Html.RadioButtonFor(x => x.ReportType, AwareMD.Cerebrum.Shared.Enums.TimesheetReportType.Total, new {})
                @Html.Label("Total", "", new { @class = "control-label" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Search" class="btn btn-default btn-worked-hour-search" />
            </div>
        </div>
    </div>
}


