@model IEnumerable<Cerebrum.ViewModels.Medications.VMMedication>

@Html.ModalHeader("Dose Change Search Results")
<div class="modal-body">
    <h3 class="panel-title">Search Results <span class="badge cbadge">@Model.Count()</span></h3>
    <div class="content-height300">
        <table class="table table-bordered table-hover table-condensed table-input">
            <tr>
                <th>
                    @Html.DisplayNameFor(model => model.Name)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.IngredientsStr)
                </th>                
                <th></th>
            </tr>

            @foreach (var item in Model)
            {
                <tr>
                    <td>
                        @item.Name
                    </td>
                    <td>
                        @item.IngredientsStr
                    </td>
                    <td>
                        <a class="btn btn-xs btn-primary edit-dose-med" href="#" 
                           data-med-id="@item.Id" 
                           data-med-name="@item.Name" 
                           data-med-din="@item.DIN"
                           data-med-form="@item.FormsStr"
                           data-med-classes="@item.ClassesStr"
                           data-med-ingredients="@item.IngredientsStr"
                           data-med-ingredient-strength="@item.FirstIngredientsStr"
                           data-med-ingredient-strength-msg="@item.FirstIngredientsStrMsg">
                        Select
                        </a>
                    </td>
                </tr>
            }

        </table>
    </div>
</div>

@Html.ModalFooter(isInfoModal: true)
