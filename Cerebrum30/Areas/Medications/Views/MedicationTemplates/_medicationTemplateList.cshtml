@model IEnumerable<Cerebrum.ViewModels.Medications.VMMedicationTemplate>



@Html.ModalHeader("Medication Search Results")
<div class="modal-body content-height300">
    <table class="table table-bordered table-hover table-condensed">
        <tr>           
            <th>
                @Html.DisplayNameFor(model => model.Name)
            </th>            
            <th></th>
        </tr>       
        
        @foreach (var item in Model)
        {
            <tr>                
                <td>
                    @item.Name
                </td>                
                <td>
                    <button class="btn btn-xs btn-primary medtemp-select" href="#" 
                            data-post-url="@Url.Action("createfromtemplate","patientmedications",new { area = "medications" })" 
                            data-medtemp-id="@item.Id">Select</button>
                </td>
            </tr>
        }
        
    </table>
</div>
@Html.ModalFooter(isInfoModal: true)