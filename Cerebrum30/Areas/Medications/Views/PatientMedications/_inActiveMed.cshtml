@model Cerebrum.ViewModels.Medications.VMPatientMedCreate

    @Html.ModalHeader(Model.MedicationName)
    <div class="modal-body">
        
        <div class="row">
            <div class="col-md-12">
                <div class="text-danger">
                    <span class="glyphicon glyphicon-warning-sign gl-100 text-danger"></span>
                    <span class="text-danger">The following medication(s) are no longer active. Please select a different brand.</span>                    
                </div>
                <ul>
                    @foreach (var item in Model.InActiveMedications)
                    {
                        <li>
                            <div>@item.Name</div>
                            <div>DIN: @item.DIN</div>
                            <div>Ingredients: @item.IngredientsStr</div>
                        </li>
                    }
                </ul>
            </div>
        </div>
              


    </div><!--Modal body end-->
    @Html.ModalFooter(isInfoModal:true)

