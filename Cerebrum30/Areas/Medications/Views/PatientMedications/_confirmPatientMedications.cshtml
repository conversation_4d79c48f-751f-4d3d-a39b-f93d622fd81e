@model Cerebrum.ViewModels.Medications.VMRxCheck

@{ 
    var url = "";
    
    if (Model.IsPrint)
    {
        url = Url.Action("printprescriptions", "patientmedications", new { area = "medications"}); 
    }
    else
    {
        url = Url.Action("createpatientprescriptions", "patientmedications", new { area = "medications"}); 
    }
}

<form id="frm-rxcheck" action="@url" method="get">
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.patientId)
    @Html.HiddenFor(model => model.IsPrint)
    @{
        for (int i = 0; i < Model.PatientMedicationIds.Count; i++)
        {
            @Html.HiddenFor(model => Model.PatientMedicationIds[i])
        }
    }
    @Html.ModalHeader("WARNING")
    <div id="rxCheckConfirmModal" class="modal-body">
        <h3>Are you sure you want to continue?</h3>

        @if (Model.AllergyTherapeuticClasses.Any())
        {
            <div style="margin-bottom:10px;" class="row">
                <div class="col-md-12">
                    <div class="text-danger">
                        <span class="glyphicon glyphicon-info-sign gl-100"></span> <span> One or more of active ingredients is in the allergy/intolerance list for this patient.</span>
                        @*<span class="glyphicon glyphicon-info-sign gl-100"></span> <span>The patient is allergic to the following Therapeutic Classes:</span>
                        <div>
                            One or more of active ingredients is in the allergy/intolerance list for this patient.
                            <ul>
                                @foreach (var item in Model.AllergyTherapeuticClasses)
                                {
                                    <li>@item</li>
                                }
                            </ul>
                        </div>*@
                    </div>
                </div>
            </div>
        }

        @if (Model.Allergies.Any())
        {
            <div style="margin-bottom:10px;" class="row">
                <div class="col-md-12">
                    <span class="glyphicon glyphicon-warning-sign gl-100 text-danger"></span>
                    <span class="text-danger"> The patient is allergic to the following medication(s)</span>
                </div>
                <ul>
                    @foreach (var item in Model.Allergies)
                    {
                        <li>@item</li>
                    }
                </ul>

            </div>
        }

        @if (Model.AllergicIngredients.Any())
        {
            <div class="row">
                <div class="col-md-12">
                    <div class="text-danger">
                        <span class="glyphicon glyphicon-warning-sign gl-100 text-danger"></span>
                        <span class="text-danger"> The patient is allergic to the following ingredient(s)</span>
                    </div>
                    <ul>
                        @foreach (var item in Model.AllergicIngredients)
                        {
                            <li>@item</li>
                        }
                    </ul>
                </div>
            </div>
        }

        @if (Model.Interactions.Any())
        {
            <div class="row">
                <div class="col-md-12">
                    <div class="text-warning">
                        <span class="glyphicon glyphicon-warning-sign gl-100 text-warning"></span>
                        <span class="text-warning">Please review the following interaction(s)</span>
                    </div>

                    <table class="table">
                        <thead>
                            <tr>
                                <th>
                                   Medication Name
                                </th>
                                <th>
                                   Medication Name
                                </th>

                                <th>
                                   Description
                                </th>

                                <th>
                                    Severity
                                </th>
                            </tr>
                        </thead>
                        @foreach (var item in Model.Interactions)
                        {
                            <tr>
                                <td>
                                    @item.MedicationName
                                </td>
                                <td>
                                    @item.InteratingMediationName
                                </td>
                                <td>
                                    @item.Description
                                </td>
                                <td>
                                    @item.SeverityDescription
                                </td>

                            </tr>
                        }
                    </table>
                </div>
            </div>
        }      


    </div><!--Modal body end-->
    <div class="modal-footer">
        <button data-modal-url="@url" type="submit" class="btn btn-primary btn-sm">Confirm</button>
        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
    </div>
</form>

