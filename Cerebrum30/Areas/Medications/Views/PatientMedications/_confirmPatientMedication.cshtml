@model Cerebrum.ViewModels.Medications.VMPatientMedCreate


@using (Html.BeginForm("createpatientmedication", "patientmedications", new { area = "medications" }, FormMethod.Post, true, new { @id = "frm-med-create" }))
{
    @Html.Mo<PERSON>eader(Model.MedicationName)
    <div id="medAddConfirmModal" class="modal-body">
        <h3>Are you sure you want to add this?</h3>
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => model.Id)
        @Html.HiddenFor(model => model.SelectionType)
        @Html.HiddenFor(model => model.PatientId)
        @Html.HiddenFor(model => model.MedicationNoDinId)
        @Html.HiddenFor(model => model.DIN)
        @Html.HiddenFor(model => model.LU)
        @Html.HiddenFor(model => model.IsDin)
        @Html.HiddenFor(model => model.Form)
        @Html.HiddenFor(model => model.Classes)
        @Html.HiddenFor(model => model.Strength)
        @Html.HiddenFor(model => model.Ingredients)
        @Html.HiddenFor(model => model.MedicationName)
        @Html.HiddenFor(model => model.IsAllergic)
        @Html.HiddenFor(model => model.HasAllergicIngredients)
        @Html.HiddenFor(model => model.IsOnMedication)
        @Html.HiddenFor(model => model.HasTherapeuticClass)
        @Html.HiddenFor(model => model.Dose)
        @Html.HiddenFor(model => model.SIG)
        @Html.HiddenFor(model => model.MedicationRoute)
        @Html.HiddenFor(model => model.OutsideProviderFirstName)
        @Html.HiddenFor(model => model.OutsideProviderLastName)
        @Html.HiddenFor(model => model.Ingredients)
        @Html.HiddenFor(model => model.Repeats)
        @Html.HiddenFor(model => model.Mitte)
        @Html.HiddenFor(model => model.MitteUnitId)
        @Html.HiddenFor(model => model.StartDate)
        @Html.HiddenFor(model => model.IsConfirmed)
        @Html.HiddenFor(model => model.DateStartedDay)
        @Html.HiddenFor(model => model.DateStartedMonth)
        @Html.HiddenFor(model => model.DateStartedYear)
        @Html.HiddenFor(model => model.Instructions)


        @if (Model.IsOnMedication)
        {
            <div style="margin-bottom:10px;" class="row">
                <div class="col-md-12">
                    <div class="text-primary"><span class="glyphicon glyphicon-info-sign gl-100"></span> The patient is currently on this medication</div>
                </div>
            </div>
        }

        @if (Model.HasTherapeuticClass)
        {
            <div style="margin-bottom:10px;" class="row">
                <div class="col-md-12">
                    <div class="text-primary">
                        <span class="glyphicon glyphicon-info-sign gl-100"></span> <span>The patient is currently on medications in the following Therapeutic Classes:</span>
                        <div>
                            <ul>
                                @foreach (var item in Model.TherapeuticClasses)
                                {
                                    <li>@item</li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        }

        @if (Model.AllergyTherapeuticClasses.Any())
        {
            <div style="margin-bottom:10px;" class="row">
                <div class="col-md-12">
                    <div class="text-danger">
                        <span class="glyphicon glyphicon-info-sign gl-100"></span> <span> One or more of active ingredients is in the allergy/intolerance list for this patient.</span>
                        @*<span class="glyphicon glyphicon-info-sign gl-100"></span> <span>The patient is allergic to the following Therapeutic Classes:</span>
                            <div>
                                One or more of active ingredients is in the allergy/intolerance list for this patient.
                                <ul>
                                    @foreach (var item in Model.AllergyTherapeuticClasses)
                                    {
                                        <li>@item</li>
                                    }
                                </ul>
                            </div>*@
                    </div>
                </div>
            </div>
        }

        @if (Model.IsAllergic || Model.AllergicIngredients.Any())
        {
            <div style="margin-bottom:10px;" class="row">
                <div class="col-md-12">
                    <span class="glyphicon glyphicon-warning-sign gl-100 text-danger"></span>
                    <span class="text-danger"> The patient is allergic to the medication</span>
                </div>
            </div>
        }

        @if (Model.AllergicIngredients.Any())
        {
            <div class="row">
                <div class="col-md-12">
                    <div class="text-danger">
                        <span class="glyphicon glyphicon-warning-sign gl-100 text-danger"></span>
                        <span class="text-danger"> The patient is allergic to the following ingredient(s)</span>
                    </div>
                    <ul>
                        @foreach (var item in Model.AllergicIngredients)
                        {
                            <li>@item</li>
                        }
                    </ul>
                </div>
            </div>
        }

        @if (Model.Interactions.Any())
        {
            <div class="row">
                <div class="col-md-12">
                    <div class="text-warning">
                        <span class="glyphicon glyphicon-warning-sign gl-100 text-warning"></span>
                        <span class="text-warning">Please review the following interaction(s)</span>
                    </div>

                    <table class="table">
                        <thead>
                            <tr>
                                <th>
                                    Medication Name
                                </th>
                                <th>
                                    Medication Name
                                </th>

                                <th>
                                    Description
                                </th>

                                <th>
                                    Severity
                                </th>
                            </tr>
                        </thead>
                        @foreach (var item in Model.Interactions)
                        {
                            <tr>
                                <td>
                                    @item.MedicationName
                                </td>
                                <td>
                                    @item.InteratingMediationName
                                </td>
                                <td>
                                    @item.Description
                                </td>
                                <td>
                                    @item.SeverityDescription
                                </td>

                            </tr>
                        }
                    </table>
                </div>
            </div>
        }


    </div><!--Modal body end-->
    @Html.ModalFooter("Confirm", btnColor: "blue", ccsClasses: "", isInfoModal: false, addAnother: false, showMedCoverAndSadieLinks: true)
}

