@using Cerebrum.DHDR.Services.Models
@model Cerebrum.ViewModels.MedicationDespense.VMPatientMedSearchDHDR<DrugDispenseSummary, DrugDispense<Patient>, Patient>
@{
    ViewBag.Title = "DHDR";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}
@section patientinfo{
    @Html.GetPatientInfo(Model.patientRecordId)
}
<style type="text/css">
    @@media print {
        .printNot {
            display: none !important;
        }

        .headerPrint {
            display: block;
            margin-bottom: 30px;
            position: fixed;
            top: 0px;
            font-size: 7px;
            color: #f00;
        }

        .footerPrint {
            display: block;
            margin-top: 17em;
            position: fixed;
            bottom: 0px;
            font-size: 7px;
            color: #f00;
        }
    }
</style>
<script src="~/Areas/Medications/Scripts/index-dhdr.js"></script>

<div style="padding-left:5px; padding-right:5px;">
    <h3 class="printNot">DHDR search</h3>
    @(await Html.PartialAsync("_searchDHDR", Model))
</div>

