@model Cerebrum30.Areas.AdminUser.Models.VistStatistics
@{
    ViewBag.Title = "PatientVisitsStatistics";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link href="~/Content/emrmetrics.css" rel="stylesheet" />
<script src="~/Scripts/emrmetrics.js"></script>
<div id="patient_visits" class="container" style="width:1200px;background-color: rgb(215, 221, 226);margin-top:40px;">
    <div class="row" style="text-align:center;border-bottom:1px solid white;"><h2>Patient Visits Statistics</h2></div>
@using (Html.BeginForm("PatientVisitsStatistics", "EMRMetrics", FormMethod.Post, new { emrModel = Model }))
{
    <div class="row margin-top-bottom-2">
        <div class="col-md-4">
            <div class="col-lg-3">
                @Html.LabelFor(model => model.docName_s)
            </div>
            <div class="col-md-9">
                @Html.EditorFor(model => model.docName_s, new { htmlAttributes = new { @class = "form-control", tabindex = 1 } })
                @Html.HiddenFor(model => model.doctorIdHid)
            </div>
        </div>
        <div class="col-md-4">
            <div class="col-md-2">
                @Html.LabelFor(model => model.startDate_s)
            </div>
            <div class="col-md-4">
                @Html.EditorFor(model => model.startDate_s, new { htmlAttributes = new { @class = "form-control", tabindex = 2 } })
            </div>
            <div class="col-md-2">
                @Html.LabelFor(model => model.endDate_s)
            </div>
            <div class="col-md-4">
                @Html.EditorFor(model => model.endDate_s, new { htmlAttributes = new { @class = "form-control", tabindex = 3 } })
            </div>
        </div>
        <div class="col-md-4">
            <div class="col-md-5">
            </div>
            <div class="col-md-3">
                <button class="btn btn-default margin-top-bottom-2" type="submit" id="">SEARCH</button>
            </div>
            <div class="col-md-3 btn btn-default margin-top-bottom-2 hov" style="height:25px;padding-top:2px;">
                @*<button class="btn btn-default margin-top-bottom-2" type="submit" id="">PRINT</button>*@
                <a href=@Url.Action("PatientVisitsStatisticsPrint", "EMRMetrics", new { doctorIdHid = "docIdHiddXXXXX", startDate = "startDateXXXXX", endDate = "endDateXXXXX", docName = "docNameXXXXX" }) id='lnk_s' target="_blank">PRINT</a>
            </div>
        </div>
    </div>
    <div class="row">
        <table class="table border_a" id="patient_visits_tbl" name="dem_addr_List" style="font-size: 12px;margin-bottom:0px;">
                <thead>
                    <tr class="vertical-center">
                        <th class="border_n">Sheduled Appointments</th>
                        <th class="border_n">Billing</th>
                        <th class="border_n">Encounter Notes</th>
                        <th class="border_n">Problem Lists</th>
                        <th class="border_n">Stored Documents</th>
                        <th class="border_n">Prescriptions new/renewals</th>
                        <th class="border_n">Use of reminders/alerts</th>
                        <th class="border_n">Labs</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="vertical-center">
                        <td class="border_n">@Html.LabelFor(model => model.appointments, @Model.appointments, new {})</td>
                        <td class="border_n">@Html.LabelFor(model => model.billing, @Model.billing, new {})</td>
                        <td class="border_n">@Html.LabelFor(model => model.enc_notes, @Model.enc_notes, new {})</td>
                        <td class="border_n">@Html.LabelFor(model => model.problem_list, @Model.problem_list, new {})</td>
                        <td class="border_n">@Html.LabelFor(model => model.documents, @Model.documents, new {})</td>
                        <td class="border_n">@Html.LabelFor(model => model.prescriptions, @Model.prescriptions, new {})</td>
                        <td class="border_n">@Html.LabelFor(model => model.reminders, @Model.reminders, new {})</td>
                        <td class="border_n">@Html.LabelFor(model => model.labs, @Model.labs, new {})</td>
                    </tr>
                </tbody>
        </table>
        <div class="row marginTop2">
            <div class="col-md-12 text-center green">
                <span id="pvs_messageId">@Html.LabelFor(x => x.message, @Model.message ?? "", new {})</span>
            </div>
        </div>
    </div>
}
</div>

