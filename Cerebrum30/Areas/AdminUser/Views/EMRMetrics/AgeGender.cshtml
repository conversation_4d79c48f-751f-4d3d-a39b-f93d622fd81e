    @model Cerebrum30.Areas.AdminUser.Models.AgeAndGender
@{
    ViewBag.Title = "AgeGender";
    Layout = "~/Views/Shared/_LayoutDemographics.cshtml";
}

<link href="~/Content/emrmetrics.css" rel="stylesheet" />
<script src="~/Scripts/emrmetrics.js"></script>
<style>
    .dt_padd {
    padding-left:1px;padding-right:0px;
    }
</style>
<div id="mainAGdiv">
    <div id="div_L1" class="div-f-l-1">
        <span>Age And Gender Distribution</span>
    </div>
@using (Html.BeginForm("AgeGender", "EMRMetrics", FormMethod.Post, new { emrModel = Model }))
{
    <div id="div_L2" class="div-f-l-1">
        <div class="form-group margin_t_5">
            <div class="col-md-8">
                <div class="form-group">
                    <div class="col-md-3">
                        <div class="col-md-2">
                            @Html.LabelFor(model => model.practiceName)
                        </div>
                    </div>
                    <div class="col-md-9">
                        @Html.LabelFor(model => model.practiceName, Model.practiceName, new {})
                        @Html.HiddenFor(model => model.practiceId)
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3">
                        <div class="col-md-2">
                            @Html.LabelFor(model => model.docName)
                        </div>
                    </div>
                    <div class="col-md-9"  style="padding-right: 0px;">
                        @Html.EditorFor(model => model.docName, new { htmlAttributes = new { @class = "form-control", tabindex = 1 } })
                        @Html.HiddenFor(model => model.docIdHidd)
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.startDate)
                    </div>
                    <div class="col-md-3">
                        @Html.EditorFor(model => model.startDate, new { htmlAttributes = new { @class = "form-control dt_padd", tabindex = 2 } })
                    </div>
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.endDate)
                    </div>
                    <div class="col-md-3">
                        @Html.EditorFor(model => model.endDate, new { htmlAttributes = new { @class = "form-control dt_padd", tabindex = 3 } })
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <div class="form-group">
                        <input type="submit" name="Search" value="Search" class="btn btn-default a_g_btn" />
                    </div>
                    <div class="form-group">
                        <div class="btn btn-default a_g_btn_">
                            <a href=@Url.Action("AGPrint", "EMRMetrics", new { docIdHidd = "docIdHiddXXXXX", startDate = "startDateXXXXX", endDate = "endDateXXXXX", docName = "docNameXXXXX" }) id='lnk' target="_blank">Print  Document</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

    <div id="div_L3" class="div-f-l-1">
        <table style="margin-left:1px;margin-top:2px;border:2px solid rgb(176, 178, 180);">
            <tr class="tr_brd">
                <th style="width:20px;"></th>
                <th style="width:315px;">Age Groups - Years</th>
                <th style="width:120px;">Percentage</th>
                <th style="width:120px;">Male</th>
                <th style="width:120px;">Female</th>
            </tr>
            <tr class="tr_brd">
                <td></td>
                <td>0  -  19</td>
                <td>
                    @Html.LabelFor(model => model.ag_19, Model.ag_19, new {})
                </td>
                <td>
                    @Html.LabelFor(model => model.ag_19_M, Model.ag_19_M, new {})
                </td>
                <td>
                    @Html.LabelFor(model => model.ag_19_F, Model.ag_19_F, new {})
                </td>
            </tr>
            <tr class="tr_brd">
                <th></th>
                <th>20  -  44</th>
                <td>
                    @Html.LabelFor(model => model.ag_20_44, Model.ag_20_44, new {})
                </td>
                <td>
                    @Html.LabelFor(model => model.ag_20_44_M, Model.ag_20_44_M, new {})
                </td>
                <td>
                    @Html.LabelFor(model => model.ag_20_44_F, Model.ag_20_44_F, new {})
                </td>
            </tr>
            <tr class="tr_brd">
                <th></th>
                <th>45  -  64</th>
                <td>
                    @Html.LabelFor(model => model.ag_45_64, Model.ag_45_64, new {})
                </td>
                <td>
                    @Html.LabelFor(model => model.ag_45_64_M, Model.ag_45_64_M, new {})
                </td>
                <td>
                    @Html.LabelFor(model => model.ag_45_64_F, Model.ag_45_64_F, new {})
                </td>
            </tr>
            <tr class="tr_brd">
                <th></th>
                <th>65  -  84</th>
                <td>
                    @Html.LabelFor(model => model.ag_65_84, Model.ag_65_84, new {})
                </td>
                <td>
                    @Html.LabelFor(model => model.ag_65_84_M, Model.ag_65_84_M, new {})
                </td>
                <td>
                    @Html.LabelFor(model => model.ag_65_84_F, Model.ag_65_84_F, new {})
                </td>
            </tr>
            <tr class="tr_brd tr_brd_last">
                <th></th>
                <th>85  -</th>
                <td>
                    @Html.LabelFor(model => model.ag_85, Model.ag_85, new {})
                </td>
                <td>
                    @Html.LabelFor(model => model.ag_85_M, Model.ag_85_M, new {})
                </td>
                <td>
                    @Html.LabelFor(model => model.ag_85_F, Model.ag_85_F, new {})
                </td>
            </tr>
        </table>
        <div class="col-md-8 green">
            @Html.LabelFor(model => model.message, Model.message, new {})
        </div>
    </div>
    <div class="row marginTop2">
        <div class="col-md-12 text-center green">
            <span id="ag_messageId">@Html.LabelFor(x => x.message, @Model.message ?? "", new {})</span>
        </div>
    </div>
</div>

