@model Cerebrum30.Models.UploadForm
@{
    ViewBag.Title = "CreatePdfForm";
    Layout = "~/Views/Shared/_Layout.cshtml";
}
<br />
@*<h2>CreatePdfForm</h2>*@

<html>
<head>
    @*<link href="~/Content/admin-user.css" rel="stylesheet" />*@
    <script src="~/Scripts/jquery-3.1.0.min.js"></script>
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <meta name="viewport" content="width=device-width" />
    <title>Create Pdf Form</title>
    <script>
        $(document).ready(function () {
            $("#close").click(function () { window.close(); });

            $(document).on("click", ".seeel", function () {
                //alert($("select[name=fileName]").val());
                if ($("select[name=fileName]").val() == undefined || $("select[name=fileName]").val() == "")
                {
                    alert("File should be selected!");
                    return false;
                }
                //$(".specA").attr("href") = $(".specA").attr("href") + "/fileName=" + $("select[name=fileName]").val();

                //alert($(".specA").attr("href"));
            });
        });
    </script>
    <style>
        .abo select {
        max-width:1200px !important;
        width:500px;
        }
        body {
            padding-top: 0px !important;
            padding-bottom: 0px !important;
            background-color: rgb(233, 238, 240) !important;
        }

        #mainDiv {
            width: 450px;
            height: 85px;
        }

            #mainDiv:after {
                content: " ";
                display: block;
                clear: both;
            }

        .headerSig {
            width: 450px;
            float: left;
        }

            .headerSig span {
                display: inline-block;
                float: left;
                margin-left: 150px;
                font-size: 15px;
                font-weight: 700;
            }

        .uploadDiv1 {
            width: 450px;
            float: left;
        }

        .uploadDiv1_1 {
            width: 350px;
            float: left;
        }

        .uploadDiv1_2 {
            width: 100px;
            float: left;
        }

            .uploadDiv1_2 input {
                width: 115px;
            }

        .closeDiv {
            /*width: 450px;*/
            float: right;
            margin-top: 15px;
        }

            .closeDiv a {
                background-color: rgb(207, 210, 212);
                height: 23px;
                /*margin-left: 200px;*/
                padding-top: 0px;
            }

        .closeDiv_ {
            /*width: 450px;*/
            float: left;
            margin-top: 15px;
        }

            .closeDiv_ input, a {
                background-color: rgb(207, 210, 212);
                height: 23px;
                /*margin-left: 200px;*/
                padding-top: 0px;
            }

        .green_ {
            color: #4b9e02 !important;
        }
    </style>
</head>
<body>
    <div class="" id="mainDiv">
        @using (Html.BeginForm("Upload", "PdfForms", FormMethod.Post, new { enctype = "multipart/form-data" }))
        {
            @Html.HiddenFor(model => model.message)
            @*@Html.HiddenFor(model => model.fileName)*@
            @Html.HiddenFor(model => model.uploadingUserId)
            @Html.HiddenFor(model => model.referralDoctorIf)
            @Html.HiddenFor(model => model.patientRecordId)

            <div class="form-horizontal" style="width:720px;">
                @*<div class="row headerSig">*@
                <div class="row" style="background-color:rgb(195, 203, 206) !important;">
                    <div class="col-sm-12" style="text-align:center;">
                        <span style="font-size:x-large">Create Pdf Form</span>
                    </div>
                </div>
                @*<div class="row uploadDiv1">*@
                <div class="row" style="margin-top:5px;">
                    <div class="col-sm-10">
                        @Html.TextBoxFor(model => model.Files, new { type = "file", name = "Files" })
                        @*<input type="file" name="fileUpload" />*@
                    </div>
                    <div class="col-sm-2">
                        <input type="submit" name="submit" value="Upload Form" />
                    </div>
                </div>
                <div class="row abo">
                    @Html.DropDownListFor(model => model.fileName, new SelectList(Model.fileNameList, "Value", "Text"), "Please select a file",new { @class="s_f"})
                </div>
                <div class="row">
                    @*<table class="table" id="refList" name="refList" style="width:100%">

                        <thead>
                            <tr class="vertical-center" style="background-color:#dddddd;">
                                <th width="25%">Control</th>
                                <th width="25%">Control</th>
                                <th width="25%">Control</th>
                                <th width="25%">Control</th>
                            </tr>
                        </thead>
                        <tbody>

                            @if (Model != null && Model.retControlDataList != null && Model.retControlDataList.Count > 0)
                            {
                                for (int i = 0; i < Model.retControlDataList.Count; i++)
                                {
                                    <tr>
                                        <td class="ref_List btn-edit-ext-doctor">
                                           @Html.DisplayFor(model => model.retControlDataList[i].name)
                                            @Html.HiddenFor(model => model.retControlDataList[i].name, new { @id = Model.retControlDataList[i].name })
                                        </td>
                                    </tr>
                                }

                            }
                        </tbody>

                    </table>*@
                </div>
                <div class="row">
                    @Html.LabelFor(model => model.message, Model.message, new { @class = "green_" })
                </div>
                <div class="row">
                    <div class="col-sm-3 closeDiv_">
                        <input disabled type="submit" value="Download Form controls" name="Download_Form_controls" />
                    </div>
                    <div class="col-sm-3 closeDiv_">
                        <input disabled type="submit" value="Check Form controls" name="Check_Form_controls" />
                    </div>
                    <div class="col-sm-4">
                    </div>
                    <div class="col-sm-1 closeDiv">
                        <input type="submit" value="Pdf" name="Pdf" class="seeel" />
                        @*@Html.ActionLink("pdf", "FillPdf", "PdfForms", new { filename = Model.fileName}, new { @class = "btn btn-default specA", @target = "_blank" })*@
                    </div>
                    <div class="col-sm-1 closeDiv" style="margin-right:2px;">
                        @Html.ActionLink("Close", null, null, null, new { @class = "btn btn-default", id = "close" })
                    </div>
                </div>
            </div>

        }
    </div>
</body>
</html>