
@{
    ViewBag.Title = "Test";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Test</h2>
@*@Html.ActionLink("Patient Chart(Newmarket)", "GetPatientData", "PatientChart", new { officeId = 2 }, null) <br />*@
@Html.ActionLink("Patient Chart(Newmarket)", "GetPatientData", "PatientChart", new { }, null) <br />
@Html.ActionLink("Patient Chart By PatientRecordId", "GetPatientDataById", "PatientChart", new { patientRecordId = 1027 }, null) <br />
<br /><br /><br />
@Html.ActionLink("Patient Merge", "PatientMerge", "PatientMerge", new { patientRecordId = 2 }, null) <br />


