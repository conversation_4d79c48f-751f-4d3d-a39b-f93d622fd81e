﻿@model Cerebrum.ViewModels.Patient.PatientChartModel
@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_LayoutDemographics.cshtml";
}
<link href="~/Content/patientChart.css" rel="stylesheet" />
<script src="~/Scripts/patientChart.js"></script>
<head id="Head1">

</head>
<body>
    @using (Html.BeginForm("GetPatientData", "PatientChart", FormMethod.Post, new { patientChartModel = Model }))
    {
        <div id="mainFrame">
            <div id="ajax-loader">
                <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
            </div>
            <div id="header-cap-ptn">
                <span>PATIENT DATA CHART</span>
                @Html.HiddenFor(m => m.officeId)
                @Html.HiddenFor(m => m.UserID)
                @Html.HiddenFor(m => m.practiceId)
            </div>
            <div id="patient-demographic">
                <div id="" class="medClass">
                    <div id="pat_name_right" class="checkLabel">
                        <span id="patNameSpan">Patient Name:</span>
                        @Html.EditorFor(model => model.PatientName, new { htmlAttributes = new { @class = "patNameSpan1" } })
                        @Html.HiddenFor(m => m.patientRecordId)
                        <span id="patDateSpan">Date:</span>
                        @Html.EditorFor(model => model.DateStr, new { htmlAttributes = new { @class = "patDateSpan1" } })
                        <button type="submit" ID="fillButton" class="subButton" value="Refresh" name="Refresh">Select</button>
                    </div>
                    <div id="pat_name_dem" class="checkLabel">
                        <span>Patient demographic data</span>
                    </div>
                </div>
                <div id="patient-dem-data">
                    <div id="patient_dem_left">
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50 alignR">
                                    <label class="rowLbl" ID="Label1" value="OHIP #">HIN #</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.ohipId??"")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label3">SIN #</label>
                                </div>
                                <div class="r_div_r_150 alignR">
                                    <label>@(Model.sinId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label5">RMB #</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.rmbId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label7">Chart ID</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.chartId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label9">MRN:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.mrnId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <label class="rowLbl" ID="Label11"></label>
                                <label class="rowShowLbl" ID="Label12"></label>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label13">L. Name:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.lName ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <label class="rowLbl" ID="Label15">Title:</label>
                                <label>@(Model.titleId ?? "")</label>
                                <label class="rowLbl" ID="Label2">Suffix:</label>
                                <label>@(Model.suffixId ?? "")</label>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label17">F. Name</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.fName ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label19">M.Name:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.middleId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label4">Date Of Birth:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.birthId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label8">Gender:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.genderId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label6">Phone#</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.phoneId ?? "")</label>
                                    <label class="rowLbl" ID="Label14">Ext:</label>
                                    <label>@(Model.extId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label10">Cell Ph.#</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.CellId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left_b">
                                <label class="rowLbl" ID="Label18">E-Mail:</label>
                            </div>
                            <div class="p_dem_row_right_b alignR ">
                                <label>@(Model.emailId ?? "")</label>
                            </div>
                        </div>
                    </div>
                    <div id="patient_dem_middle">
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label16">Phone#</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.Phone_Id ?? "")</label>
                                    <label class="rowLbl" ID="Label28">Ext:</label>
                                    <label>@(Model.Ext_Id ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label21">FAX#</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.faxId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label20">Postal Code:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.postlCodeId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label25">Address Type:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.addressTypeId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left_b">
                                <label class="rowLbl" ID="Label27">Address:</label>
                            </div>
                            <div class="p_dem_row_right_b alignR ">
                                <label>@(Model.addressId ?? "")</label>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label31">City:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.cityId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label33">Province:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.provinceId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label39">Country:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.countryId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label41">Translation:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.translationId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left_b">
                                <label class="rowLbl">F.Doctor:</label>
                            </div>
                            <div class="p_dem_row_right_b alignR ">
                                <label>@(Model.famDocId ?? "")</label>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label45">Enrolment Status:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.enrolmentStatusId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label36">Person Status:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.personStatusId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label51">Enrolment Date:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.enrolmentDateId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right">
                                <div class="r_div_l_50">
                                    <label class="rowLbl" ID="Label30">Status Date:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.statusDateId ?? "")</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="patient_dem_right">
                        <div class="p_dem_row p_dem_row_3">
                            <div class="p_dem_row_left_b_64">
                                <label class="rowLbl" ID="Label24">Enrol.Term.Reason :</label>
                            </div>
                            <div class="p_dem_row_right_b alignR ">
                                <label>@(Model.enrolTerminReasonId ?? "")</label>
                            </div>
                        </div>
                        <div class="p_dem_row p_dem_row_3">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label32">Verification Status:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.verificationStatusId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right p_dem_row_right_3">
                                <div class="r_div_l_46">
                                    <label class="rowLbl" ID="Label35">Verif. Date:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.verificationDateId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row p_dem_row_3">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label38">Payment:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.paymentId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right p_dem_row_right_3">
                                <div class="r_div_l_46">
                                    <label class="rowLbl" ID="Label42">Issue Date:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.issueDateId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row p_dem_row_3">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label26">N.O.KIN Ph #</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.nextofKinPhoneId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right p_dem_row_right_3">
                                <div class="r_div_l_46">
                                    <label class="rowLbl" ID="Label48">Health Card Exp.:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.helthExpId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row p_dem_row_3">
                            <div class="p_dem_row_left_b_64">
                                <label class="rowLbl" ID="Label46">N.O.KIN:</label>
                            </div>
                            <div class="p_dem_row_right_b alignR ">
                                <label>@(Model.nextofKinId ?? "")</label>
                            </div>
                        </div>
                        <div class="p_dem_row p_dem_row_3">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label58">Official Language:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.oficialLanguageId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right p_dem_row_right_3">
                                <div class="r_div_l_46">
                                    <label class="rowLbl" ID="Label60">Spoken Language:</label>
                                </div>
                                <div class="r_div_r_150 alignR ">
                                    <label>@(Model.spokenLanguageId ?? "")</label>
                                </div>
                            </div>
                        </div>
                        <div class="p_dem_row p_dem_row_3">
                            <div class="p_dem_row_left">
                                <div class="l_div_l_50">
                                    <label class="rowLbl" ID="Label62">Enrol.Term. Date:</label>
                                </div>
                                <div class="l_div_r_170 alignR ">
                                    <label>@(Model.enrolTerminDateId ?? "")</label>
                                </div>
                            </div>
                            <div class="p_dem_row_right p_dem_row_right_3">
                            </div>
                        </div>
                        <div class="p_dem_row p_dem_row_3">
                            <div class="p_dem_row_left_b_64">
                                <label class="rowLbl" ID="Label68">Prim.Physician:</label>
                            </div>
                            <div class="p_dem_row_right_b alignR">
                                <label>@(Model.primaryPhysicianId ?? "")</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="mainContent">
                <div id="content">
                    <!-- Letters tests b -->
                    <div id="letter" class="medClass">
                        <div id="letterLabel" class="checkLabel">
                            <div class="timePeriodDiv">
                                @Html.HiddenFor(model => model.hiddenLetters)
                                @Html.EditorFor(model => model.endLetters, new { htmlAttributes = new { @class = "DateCss" } })
                                <label class="marginTop_1">To:</label>
                                @Html.EditorFor(model => model.startLetters, new { htmlAttributes = new { @class = "DateCss" } })
                                <label class="marginTop_1">From:</label>
                                @Html.EditorFor(model => model.lettersChB_pch)
                            </div>
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label47">Letters</label>
                        </div>
                    </div>
                    <div id="letterDiv" class="medDivClass showXScroll">
                        <div id="repeaterDivLetter">
                            <div class="repItemDivLetter">
                                @if (Model.letters != null)
                                {
                                    for (int i = 0; i < Model.letters.Count; i++)
                                    {
                                <div style="position:relative;float:left">
                                    <div class="repCheckBoxLetter">
                                        @Html.HiddenFor(model => model.letters[i].id)
                                    </div>
                                    <div class="repDate1Letter">
                                        @Html.DisplayFor(model => model.letters[i].name)
                                        @Html.HiddenFor(model => model.letters[i].name)
                                    </div>
                                </div>
                                    }
                                }
                            </div>
                        </div>
                    </div>
                    <!-- Letters tests e -->
                    <!-- Internal tests b -->
                    <div id="Div3" class="medClass">
                        <div id="Div4" class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label49">Internal Tests</label>
                        </div>
                    </div>
                    <div id="internalTestDiv" class="medDivClass showXScroll">
                        <div class="repItemIntTest_h">
                            <div class="appDate1_it">
                                <label>Visit Date</label>
                            </div>
                            <div class="appDate2_it">
                                <label>Report Date</label>
                            </div>
                            <div class="appDate3_it">
                                <label>Test Name</label>
                            </div>
                        </div>
                        <div class="repItemIntTest">
                            @if (Model.internalTests != null)
                            {
                                for (int i = 0; i < Model.internalTests.Count; i++)
                                {
                            <div style="position:relative;float:left">
                                <div class="appDate1_it">
                                    @Html.DisplayFor(model => model.internalTests[i].testDate)
                                    @Html.HiddenFor(model => model.internalTests[i].testDate)
                                    @Html.HiddenFor(model => model.internalTests[i].appointmentId)
                                    @Html.HiddenFor(model => model.internalTests[i].testID)
                                </div>
                                <div class="appDate2_it">
                                    @Html.DisplayFor(model => model.internalTests[i].requisitionDate)
                                    @Html.HiddenFor(model => model.internalTests[i].requisitionDate)
                                </div>
                                <div class="appDate3_it">
                                    @Html.DisplayFor(model => model.internalTests[i].testName)
                                    @Html.HiddenFor(model => model.internalTests[i].testName)
                                </div>
                                <div class="appDate1_it">
                                </div>
                            </div>
                                }
                            }
                        </div>
                    </div>
                    <!-- Internal tests e -->
                    <!-- Doctor's orders b -->
                    <!-- Doctor's orders e -->
                    <!-- Appointment history b -->
                    <div class="medClass">
                        <div class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label52">Appointment History</label>
                        </div>
                    </div>
                    <div id="appHistoryDiv" class="medDivClass showXScroll">
                        <div>
                            <div class="repItemDivLetter_a_h">
                                <div class="appDate0_a">
                                    <label>-</label>
                                </div>
                                <div class="appDate1_a">
                                    <label ID="Label1">Appointment</label>
                                </div>
                                <div class="appDate2_a">
                                    <label ID="Label2">Doctor</label>
                                </div>
                                <div class="appDate3_a">
                                    <label ID="Label3">Office</label>
                                </div>
                                <div class="appDate4_a">
                                    <label ID="Label4">Reason</label>
                                </div>
                                <div class="appDate5_a">
                                    <label ID="Label5">Ref.Md</label>
                                </div>
                                <div class="appDate6_a">
                                    <label ID="Label6_a">Tests</label>
                                </div>
                                <div class="appDate7_a">
                                    <label ID="Label7">Cons.</label>
                                </div>
                                <div class="appDate8_a">
                                    <label ID="Label8">Diag.</label>
                                </div>
                                <div class="appDate9_a">
                                    <label ID="Label9">Status</label>
                                </div>
                                <div class="appDate10_a">
                                    <label ID="Label10">Letter</label>
                                </div>
                            </div>
                            <div class="repItemDivLetter_a">
                                @if (Model.appointments != null)
                                {
                                    for (int i = 0; i < Model.appointments.Count; i++)
                                    {
                                        <div style="position:relative;float:left">
                                            <div class="appDate0_a">
                                                <label>-</label>
                                            </div>
                                            <div class="appDate1_a">
                                                @Html.DisplayFor(model => model.appointments[i].appointmentDateTimeStr)
                                                @Html.HiddenFor(model => model.appointments[i].appointmentDateTimeStr)
                                            </div>
                                            <div class="appDate2_a" title=@( Model.appointments[i].Doctor != null ? Model.appointments[i].Doctor.Replace(" ",""):"")>
                                                @Html.DisplayFor(model => model.appointments[i].Doctor)
                                                @Html.HiddenFor(model => model.appointments[i].Doctor)
                                            </div>
                                            <div class="appDate3_a">
                                                @Html.DisplayFor(model => model.appointments[i].Office)
                                                @Html.HiddenFor(model => model.appointments[i].Office)
                                            </div>
                                            <div class="appDate4_a">
                                                @Html.DisplayFor(model => model.appointments[i].Reason)
                                                @Html.HiddenFor(model => model.appointments[i].Reason)
                                            </div>
                                            <div class="appDate5_a">
                                                @Html.DisplayFor(model => model.appointments[i].RefDoctor)
                                                @Html.HiddenFor(model => model.appointments[i].RefDoctor)
                                            </div>
                                            <div class="appDate6_a" title=@( Model.appointments[i].Tests != null ? Model.appointments[i].Tests.Replace(" ",""):"")>
                                                @Html.DisplayFor(model => model.appointments[i].Tests)
                                                @Html.HiddenFor(model => model.appointments[i].Tests)
                                            </div>
                                            <div class="appDate7_a" title=@( Model.appointments[i].ConsultCodes != null ? Model.appointments[i].ConsultCodes.Replace(" ",""):"")>
                                                @Html.DisplayFor(model => model.appointments[i].ConsultCodes)
                                                @Html.HiddenFor(model => model.appointments[i].ConsultCodes)
                                            </div>
                                            <div class="appDate8_a" title=@( Model.appointments[i].DiagCodes != null ? Model.appointments[i].DiagCodes.Replace(" ",""):"")>
                                                @Html.DisplayFor(model => model.appointments[i].DiagCodes)
                                                @Html.HiddenFor(model => model.appointments[i].DiagCodes)
                                            </div>
                                            <div class="appDate9_a">
                                                -
                                            </div>
                                            <div class="appDate10_a">
                                                @Html.DisplayFor(model => model.appointments[i].Letter)
                                                @Html.HiddenFor(model => model.appointments[i].Letter)
                                            </div>
                                        </div>
                                    }
                                }
                            </div>
                        </div>
                        <!-- appointment changed b -->
                        <div class="repItemDivLetter_a_h_ch">
                            <div class="appDate1_a_ch">
                                <label>-</label>
                            </div>
                            <div class="appDate2_a_ch">
                                <label>Change Date</label>
                            </div>
                            <div class="appDate3_a_ch">
                                <label>Changed By</label>
                            </div>
                            <div class="appDate4_a_ch">
                                <label>Ch. Res.</label>
                            </div>
                            <div class="appDate5_a_ch">
                                <label>Date Old</label>
                            </div>
                            <div class="appDate6_a_ch">
                                <label>Date New</label>
                            </div>
                            <div class="appDate7_a_ch">
                                <label>Doctor</label>
                            </div>
                            <div class="appDate8_a_ch">
                                <label>Office</label>
                            </div>
                            <div class="appDate9_a_ch">
                                <label>Reason</label>
                            </div>
                            <div class="appDate10_a_ch">
                                <label>Ref.Doc</label>
                            </div>
                        </div>
                        <div class="repItemDivLetter_a_ch">
                            @if (Model.changedAppointments != null)
                            {
                                for (int i = 0; i < Model.changedAppointments.Count; i++)
                                {
                                    <div style="position:relative;float:left">
                                        <div class="appDate1_a_ch">
                                            <label>-</label>
                                        </div>
                                        <div class="appDate2_a_ch">
                                            @Html.DisplayFor(model => model.changedAppointments[i].ChangeDateStr)
                                            @Html.HiddenFor(model => model.changedAppointments[i].ChangeDateStr)
                                        </div>
                                        <div class="appDate3_a_ch">
                                            @Html.DisplayFor(model => model.changedAppointments[i].ChangedBy)
                                            @Html.HiddenFor(model => model.changedAppointments[i].ChangedBy)
                                        </div>
                                        <div class="appDate4_a_ch">
                                            @Html.DisplayFor(model => model.changedAppointments[i].ChangeReason)
                                            @Html.HiddenFor(model => model.changedAppointments[i].ChangeReason)
                                        </div>
                                        <div class="appDate5_a_ch">
                                            @Html.DisplayFor(model => model.changedAppointments[i].appointmentDateOldStr)
                                            @Html.HiddenFor(model => model.changedAppointments[i].appointmentDateOldStr)
                                        </div>
                                        <div class="appDate6_a_ch">
                                            @Html.DisplayFor(model => model.changedAppointments[i].appointmentDateNewStr)
                                            @Html.HiddenFor(model => model.changedAppointments[i].appointmentDateNewStr)
                                        </div>
                                        <div class="appDate7_a_ch" title=@( Model.changedAppointments[i].Doctor != null ? Model.changedAppointments[i].Doctor.Replace(" ",""):"")>
                                            @Html.DisplayFor(model => model.changedAppointments[i].Doctor)
                                            @Html.HiddenFor(model => model.changedAppointments[i].Doctor)
                                        </div>
                                        <div class="appDate8_a_ch" title=@( Model.changedAppointments[i].Office != null ? Model.changedAppointments[i].Office.Replace(" ",""):"")>
                                            @Html.DisplayFor(model => model.changedAppointments[i].Office)
                                            @Html.HiddenFor(model => model.changedAppointments[i].Office)
                                        </div>
                                        <div class="appDate9_a_ch" title=@( Model.changedAppointments[i].Reason != null ? Model.changedAppointments[i].Reason.Replace(" ",""):"")>
                                            @Html.DisplayFor(model => model.changedAppointments[i].Reason)
                                            @Html.HiddenFor(model => model.changedAppointments[i].Reason)
                                        </div>
                                        <div class="appDate10_a_ch" title=@( Model.changedAppointments[i].ReferralDoctor != null ? Model.changedAppointments[i].ReferralDoctor.Replace(" ",""):"")>
                                            @Html.DisplayFor(model => model.changedAppointments[i].ReferralDoctor)
                                            @Html.HiddenFor(model => model.changedAppointments[i].ReferralDoctor)
                                        </div>

                                    </div>
                                }
                            }
                        </div>
                        <!-- appointment changed e -->
                        <!-- appointment canceled b -->
                        <div class="repItemDivLetter_a_h_c">
                            <div class="appDate1_a_c">
                                <label>-</label>
                            </div>
                            <div class="appDate2_a_c">
                                <label>Date</label>
                            </div>
                            <div class="appDate3_a_c">
                                <label>Cancelled By</label>
                            </div>
                            <div class="appDate4_a_c">
                                <label>Cancelled At</label>
                            </div>
                            <div class="appDate5_a_c">
                                <label>Doctor</label>
                            </div>
                            <div class="appDate6_a_c">
                                <label>Office</label>
                            </div>
                            <div class="appDate7_a_c">
                                <label>Reason</label>
                            </div>
                        </div>
                        <div class="repItemDivLetter_a_c">
                            @if (Model.canceledAppointments != null)
                            {
                                for (int i = 0; i < Model.canceledAppointments.Count; i++)
                                {
                                    <div class="appDate1_a_c">
                                        <label runat="server" title="-" Mode="Encode">-</label>
                                    </div>
                                    <div class="appDate2_a_c">
                                        @Html.DisplayFor(model => model.canceledAppointments[i].appointmentDateNewStr)
                                        @Html.HiddenFor(model => model.canceledAppointments[i].appointmentDateNewStr)
                                    </div>
                                    <div class="appDate3_a_c">
                                        @Html.DisplayFor(model => model.canceledAppointments[i].CanceleddBy)
                                        @Html.HiddenFor(model => model.canceledAppointments[i].CanceleddBy)
                                    </div>
                                    <div class="appDate4_a_c">
                                        @Html.DisplayFor(model => model.canceledAppointments[i].CanceledAtStr)
                                        @Html.HiddenFor(model => model.canceledAppointments[i].CanceledAtStr)
                                    </div>
                                    <div class="appDate5_a_c">
                                        @Html.DisplayFor(model => model.canceledAppointments[i].Doctor)
                                        @Html.HiddenFor(model => model.canceledAppointments[i].Doctor)
                                    </div>
                                    <div class="appDate6_a_c" title=@( Model.canceledAppointments[i].Office != null ? Model.canceledAppointments[i].Office.Replace(" ",""):"")>
                                        @Html.DisplayFor(model => model.canceledAppointments[i].Office)
                                        @Html.HiddenFor(model => model.canceledAppointments[i].Office)
                                    </div>
                                    <div class="appDate7_a_c" title=@( Model.canceledAppointments[i].Reason != null ? Model.canceledAppointments[i].Reason.Replace(" ",""):"")>
                                        @Html.DisplayFor(model => model.canceledAppointments[i].Reason)
                                        @Html.HiddenFor(model => model.canceledAppointments[i].Reason)
                                    </div>
                                }
                            }
                        </div>
                        <!-- appointment canceled e -->
                    </div>
                    <!-- Appointment history e -->
                    <!-- Doctor's All Requisitions b -->
                    @*<div class="medClass">
                            <div class="checkLabel">
                            </div>
                            <div class="checkBoxDiv">
                                <label ID="Label53">All Requisitions</label>
                            </div>
                        </div>
                        <div id="docReqDiv" class="medDivClass showXScroll">
                            <div class="repItemReq_h">
                                <div class="reqDate1">
                                    <label>Date</label>
                                </div>
                                <div class="reqDate2">
                                    <label>Requisition</label>
                                </div>
                            </div>
                            <div class="repItemReq">
                                <div class="reqDate1">
                                    <label ID="reqDate_areq" title="ReqDate_str">10/10/2016</label>
                                </div>
                                <div class="reqDate2">
                                    <label ID="reqName_areq" title="ReqName_rp">ReqName_rp</label>
                                </div>
                            </div>
                            <div class="repItemReq">
                                <div class="reqDate1">
                                    <label ID="reqDate_areq" title="ReqDate_str">10/10/2016</label>
                                </div>
                                <div class="reqDate2">
                                    <label ID="reqName_areq" title="ReqName_rp">ReqName_rp</label>
                                </div>
                            </div>
                        </div>*@
                    <!-- Doctor's All Requisitions e -->
                    <!-- Doctor's notes b -->
                    @*<div class="medClass">
                            <div class="checkLabel">
                            </div>
                            <div class="checkBoxDiv">
                                <label ID="Label54">Doctor's Notes</label>
                            </div>
                        </div>
                        <div id="docNotesDiv" class="medDivClass showXScroll">
                            <div class="repItemNotes_a_h_d_n">
                                <div class="appDate1_d_n">
                                    <label>Doctor</label>
                                </div>
                                <div class="appDate2_d_n">
                                    <label>Date</label>
                                </div>
                                <div class="appDate3_d_n">
                                    <label>Comments</label>
                                </div>
                            </div>
                            <div class="repItemNotes_d_n">
                                @if (Model.doctorsNotes != null)
                                {
                                    for (int i = 0; i < Model.doctorsNotes.Count; i++)
                                    {
                                        <div style="position:relative;float:left">
                                            <div class="appDate1_d_n">
                                                @Html.DisplayFor(model => model.doctorsNotes[i].Doctor)
                                                @Html.HiddenFor(model => model.doctorsNotes[i].Doctor)
                                            </div>
                                            <div class="appDate2_d_n">
                                                @Html.DisplayFor(model => model.doctorsNotes[i].Date)
                                                @Html.HiddenFor(model => model.doctorsNotes[i].Date)
                                            </div>
                                            <div class="appDate3_d_n">
                                                @Html.DisplayFor(model => model.doctorsNotes[i].Comments)
                                                @Html.HiddenFor(model => model.doctorsNotes[i].Comments)
                                            </div>
                                        </div>
                                    }
                                }
                            </div>
                        </div>*@
                    <!-- Doctor's notes e -->
                    <!-- Medications b -->
                    <div id="medications" class="medClass">
                        <div id="medicationsLabel" class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label55">Medications</label>
                        </div>
                    </div>
                    <div id="medicationsDiv" class="medDivClass showXScroll">
                        <div id="repeaterDivMed">
                            @if (Model.medications != null)
                            {
                                for (int i = 0; i < Model.medications.Count; i++)
                                {
                                <div style="position:relative;float:left">
                                    <div class="repItemDivMed">
                                        <div class="repCheckBoxMed">
                                        </div>
                                        <div class="repNameMed">
                                            @Html.DisplayFor(model => model.medications[i])
                                            @Html.HiddenFor(model => model.medications[i])
                                        </div>
                                    </div>
                                </div>
                                }
                            }
                        </div>
                    </div>
                    <!-- Medications e -->
                    <!-- Allergies b -->
                    <div class="medClass">
                        <div class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label56">Allergies</label>
                        </div>
                    </div>
                    <div id="alergiesDiv" class="medDivClass showXScroll">
                        <div class="allgDiv_h">
                            <div class="allgDate1">
                                <label ID="reacType_allergy" title="ReactionType">ReactionType</label>
                            </div>
                            <div class="allgDate2">
                                <label ID="severity_allergy" title="Severity">Severity</label>
                            </div>
                            <div class="allgDate3">
                                <label ID="allergen_allergy" title="Allergen">Allergen</label>
                            </div>
                        </div>
                        <div class="allgDiv">
                            @if (Model.alergies != null)
                            {
                                for (int i = 0; i < Model.alergies.Count; i++)
                                {
                                <div style="position:relative;float:left">
                                    <div class="allgDate1">
                                        @*<label ID="reacType_allergy" title="ReactionType">ReactionType</label>*@
                                        @Html.DisplayFor(model => model.alergies[i].reactionType)
                                        @Html.HiddenFor(model => model.alergies[i].reactionType)
                                    </div>
                                    <div class="allgDate2">
                                        @*<label ID="severity_allergy" title="Severity">Severity</label>*@
                                        @Html.DisplayFor(model => model.alergies[i].severity)
                                        @Html.HiddenFor(model => model.alergies[i].severity)
                                    </div>
                                    <div class="allgDate3">
                                        @*<label ID="allergen_allergy" title="Allergen">Allergen</label>*@
                                        @Html.DisplayFor(model => model.alergies[i].allergen)
                                        @Html.HiddenFor(model => model.alergies[i].allergen)
                                    </div>
                                </div>
                                }
                            }
                        </div>
                    </div>
                    <!-- Allergies e -->
                    <!-- Problem List b -->
                    <div class="medClass">
                        <div class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label57">Problem List</label>
                        </div>
                    </div>
                    <div id="problemListDiv" class="medDivClass showXScroll">
                        <div class="probListDiv_h">
                            <div class="probListDate1">
                                <label>Onset Date</label>
                            </div>
                            <div class="probListDate2">
                                <label>Res.Date</label>
                            </div>
                            <div class="probListDate3">
                                <label>Sub.Date</label>
                            </div>
                            <div class="probListDate4">
                                <label>Life Stage</label>
                            </div>
                            <div class="probListDate5">
                                <label>Description</label>
                            </div>
                            <div class="probListDate6">
                                <label>Diagnosis</label>
                            </div>
                            <div class="probListDate7">
                                <label>Notes</label>
                            </div>
                        </div>
                        <div class="probListDiv">
                            @if (Model.problemlist != null)
                            {
                                for (int i = 0; i < Model.problemlist.Count; i++)
                                {
                                <div style="position:relative;float:left">
                                    <div class="probListDate1">
                                        @*@item.onsetDate*@
                                        @Html.DisplayFor(model => model.problemlist[i].onsetDate)
                                        @Html.HiddenFor(model => model.problemlist[i].onsetDate)
                                    </div>
                                    <div class="probListDate2">
                                        @*@item.resDate*@
                                        @Html.DisplayFor(model => model.problemlist[i].resDate)
                                        @Html.HiddenFor(model => model.problemlist[i].resDate)
                                    </div>
                                    <div class="probListDate3">
                                        @*@item.submitDateStr*@
                                        @Html.DisplayFor(model => model.problemlist[i].submitDateStr)
                                        @Html.HiddenFor(model => model.problemlist[i].submitDateStr)
                                    </div>
                                    <div class="probListDate4">
                                        @*@item.lifeStage*@
                                        @Html.DisplayFor(model => model.problemlist[i].lifeStage)
                                        @Html.HiddenFor(model => model.problemlist[i].lifeStage)
                                    </div>
                                    <div class="probListDate5 wrap_">
                                        @*@item.description*@
                                        @Html.DisplayFor(model => model.problemlist[i].description)
                                        @Html.HiddenFor(model => model.problemlist[i].description)
                                    </div>
                                    <div class="probListDate6">
                                        ---
                                    </div>
                                    <div class="probListDate7 label_ellipsis">
                                        @*@item.notes*@
                                        @Html.DisplayFor(model => model.problemlist[i].notes)
                                        @Html.HiddenFor(model => model.problemlist[i].notes)
                                    </div>
                                </div>
                                }
                            }
                        </div>
                    </div>
                    <!-- Problem List e -->
                    <!-- Family Medidal History b -->
                    <div class="medClass">
                        <div class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label59">Family History</label>
                        </div>
                    </div>
                    <div id="familyHistoryDiv" class="medDivClass showXScroll">
                        <div class="famHistDiv_h">
                            <div class="famHistDate1">
                                <label>Relationship</label>
                            </div>
                            <div class="famHistDate2">
                                <label>Age</label>
                            </div>
                            <div class="famHistDate3">
                                <label>Start Date</label>
                            </div>
                            <div class="famHistDate4">
                                <label>Life Stage</label>
                            </div>
                            <div class="famHistDate5">
                                <label>Description</label>
                            </div>
                            <div class="famHistDate6">
                                <label>Treatment</label>
                            </div>
                            <div class="famHistDate7">
                                <label>Notes</label>
                            </div>
                        </div>
                        <div class="famHistDiv">
                            @if (Model.familyhistories != null)
                                {
                                    for (int i = 0; i < Model.familyhistories.Count; i++)
                                    {
                                <div style="position:relative;float:left">
                                    <div class="famHistDate1">
                                        @*@item.Relationship*@
                                        @Html.DisplayFor(model => model.familyhistories[i].Relationship)
                                        @Html.HiddenFor(model => model.familyhistories[i].Relationship)
                                    </div>
                                    <div class="famHistDate2">
                                        @*@item.Age*@
                                        @Html.DisplayFor(model => model.familyhistories[i].Age)
                                        @Html.HiddenFor(model => model.familyhistories[i].Age)
                                    </div>
                                    <div class="famHistDate3">
                                        @*@item.startDate*@
                                        @Html.DisplayFor(model => model.familyhistories[i].startDate)
                                        @Html.HiddenFor(model => model.familyhistories[i].startDate)
                                    </div>
                                    <div class="famHistDate4 wrap_">
                                        @*@item.lifeStage*@
                                        @Html.DisplayFor(model => model.familyhistories[i].lifeStage)
                                        @Html.HiddenFor(model => model.familyhistories[i].lifeStage)
                                    </div>
                                    <div class="famHistDate5 wrap_">
                                        @*@item.description*@
                                        @Html.DisplayFor(model => model.familyhistories[i].description)
                                        @Html.HiddenFor(model => model.familyhistories[i].description)
                                    </div>
                                    <div class="famHistDate6 wrap_">
                                        @*@item.treatment*@
                                        @Html.DisplayFor(model => model.familyhistories[i].treatment)
                                        @Html.HiddenFor(model => model.familyhistories[i].treatment)
                                    </div>
                                    <div class="famHistDate7 wrap_">
                                        @*@item.notes*@
                                        @Html.DisplayFor(model => model.familyhistories[i].notes)
                                        @Html.HiddenFor(model => model.familyhistories[i].notes)
                                    </div>
                                </div>
                                    }
                                }
                        </div>
                    </div>
                    <!-- Family Medical History e -->
                    <!-- Past Medidal History b -->
                    <div class="medClass">
                        <div class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label61">Past Medical History</label>
                        </div>
                    </div>
                    <div id="medHistoryDiv" class="medDivClass showXScroll">
                        <div class="probListDiv_h">
                            <div class="probListDate1">
                                <label>Onset Date</label>
                            </div>
                            <div class="probListDate2">
                                <label>Res.Date</label>
                            </div>
                            <div class="probListDate3">
                                <label>Sub.Date</label>
                            </div>
                            <div class="probListDate4">
                                <label>Life Stage</label>
                            </div>
                            <div class="probListDate5">
                                <label>Description</label>
                            </div>
                            <div class="probListDate6">
                                <label>Diagnosis</label>
                            </div>
                            <div class="probListDate7">
                                <label>Notes</label>
                            </div>
                        </div>
                        <div class="probListDiv">
                            @if (Model.pastMedHistory != null)
                                {
                                    for (int i = 0; i < Model.pastMedHistory.Count; i++)
                                    {
                                <div style="position:relative;float:left">
                                    <div class="probListDate1">
                                        @*@item.onsetDate*@
                                        @Html.DisplayFor(model => model.pastMedHistory[i].onsetDate)
                                        @Html.HiddenFor(model => model.pastMedHistory[i].onsetDate)
                                    </div>
                                    <div class="probListDate2">
                                        @*@item.resDate*@
                                        @Html.DisplayFor(model => model.pastMedHistory[i].resDate)
                                        @Html.HiddenFor(model => model.pastMedHistory[i].resDate)
                                    </div>
                                    <div class="probListDate3">
                                        @*@item.submitDateStr*@
                                        @Html.DisplayFor(model => model.pastMedHistory[i].submitDateStr)
                                        @Html.HiddenFor(model => model.pastMedHistory[i].submitDateStr)
                                    </div>
                                    <div class="probListDate4">
                                        @*@item.lifeStage*@
                                        @Html.DisplayFor(model => model.pastMedHistory[i].lifeStage)
                                        @Html.HiddenFor(model => model.pastMedHistory[i].lifeStage)
                                    </div>
                                    <div class="probListDate5 wrap_">
                                        @*@item.description*@
                                        @Html.DisplayFor(model => model.pastMedHistory[i].description)
                                        @Html.HiddenFor(model => model.pastMedHistory[i].description)
                                    </div>
                                    <div class="probListDate6">
                                        ---
                                    </div>
                                    <div class="probListDate7 label_ellipsis">
                                        @*@item.notes*@
                                        @Html.DisplayFor(model => model.pastMedHistory[i].notes)
                                        @Html.HiddenFor(model => model.pastMedHistory[i].notes)
                                    </div>
                                </div>
                                    }
                                }
                        </div>
                    </div>
                    <!-- Past Medidal History e -->
                    <!-- Alerts And Needs b -->
                    <div class="medClass">
                        <div class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label63">Alerts And Needs</label>
                        </div>
                    </div>
                    <div id="alertsDiv" class="medDivClass showXScroll">
                        <div class="alerDiv_h">
                            <div class="alerDate1">
                                <label>Date Active</label>
                            </div>
                            <div class="alerDate2">
                                <label>End Date</label>
                            </div>
                            <div class="alerDate3">
                                <label>Description</label>
                            </div>
                            <div class="alerDate4">
                                <label>Notes</label>
                            </div>
                        </div>
                        <div class="alerDiv">
                            @if (Model.alertneeds != null)
                                {
                                    for (int i = 0; i < Model.alertneeds.Count; i++)
                                    {
                                <div style="position:relative;float:left">
                                    <div class="alerDate1">
                                        @*<label ID="activDate_alt" title="DateActive_str">@item.activDate</label>*@
                                        @Html.DisplayFor(model => model.alertneeds[i].activDate)
                                        @Html.HiddenFor(model => model.alertneeds[i].activDate)
                                    </div>
                                    <div class="alerDate2">
                                        @*<label ID="endDate_alt" title="EndDate_str">@item.endtDateStr</label>*@
                                        @Html.DisplayFor(model => model.alertneeds[i].endtDateStr)
                                        @Html.HiddenFor(model => model.alertneeds[i].endtDateStr)
                                    </div>
                                    <div class="alerDate3 wrap_">
                                        @*@item.description*@
                                        @Html.DisplayFor(model => model.alertneeds[i].description)
                                        @Html.HiddenFor(model => model.alertneeds[i].description)
                                    </div>
                                    <div class="alerDate4 wrap_">
                                        @*@item.notes*@
                                        @Html.DisplayFor(model => model.alertneeds[i].notes)
                                        @Html.HiddenFor(model => model.alertneeds[i].notes)
                                    </div>
                                </div>
                                    }
                                }
                        </div>
                    </div>
                    <!-- Alerts And Needs e -->
                    <!-- Immunizations Summary b -->
                    <div class="medClass">
                        <div class="checkLabel">
                            <div class="timePeriodDiv">
                                @Html.HiddenFor(model => model.hiddenImmun)
                                @Html.EditorFor(model => model.endImmun, new { htmlAttributes = new { @class = "DateCss" } })
                                <label class="marginTop_1">To:</label>
                                @Html.EditorFor(model => model.startImmun, new { htmlAttributes = new { @class = "DateCss" } })
                                <label class="marginTop_1">From:</label>
                                @Html.EditorFor(model => model.immunChB_pch)
                            </div>
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label64">Immunization Summary</label>
                        </div>
                    </div>
                    <div id="immunizationDiv" class="medDivClass showXScroll">
                        <div class="imm_Div_h">
                            <div class="immDate1">
                                <label>Name</label>
                            </div>
                            <div class="immDate2">
                                <label>Indicator</label>
                            </div>
                            <div class="immDate3">
                                <label>Date</label>
                            </div>
                            <div class="immDate4">
                                <label>Refused Date</label>
                            </div>
                            <div class="immDate5">
                                <label>Next Imm. Date</label>
                            </div>
                            <div class="immDate6">
                                <label>Doctor</label>
                            </div>
                        </div>
                        <div class="imm_Div">
                            @if (Model.immunizations != null)
                                {
                                    for (int i = 0; i < Model.immunizations.Count; i++)
                                    {
                                <div style="position:relative;float:left">
                                    <div class="immDate1">
                                        @*<label ID="name_imm" title="Name">Name</label>*@
                                        @Html.DisplayFor(model => model.immunizations[i].Name)
                                        @Html.HiddenFor(model => model.immunizations[i].Name)
                                    </div>
                                    <div class="immDate2">
                                        @*<label ID="indic_imm" title="Indicator">Indicator</label>*@
                                        @Html.DisplayFor(model => model.immunizations[i].Indicator)
                                        @Html.HiddenFor(model => model.immunizations[i].Indicator)
                                    </div>
                                    <div class="immDate3">
                                        @*<label ID="immDate_imm" title="ImmunizationDate_str">10/10/2016</label>*@
                                        @Html.DisplayFor(model => model.immunizations[i].ImmunizationDate_str)
                                        @Html.HiddenFor(model => model.immunizations[i].ImmunizationDate_str)
                                        @Html.HiddenFor(model => model.immunizations[i].ImmunizationDate)
                                    </div>
                                    <div class="immDate4">
                                        @*<label ID="refsDate_imm" title="RefusedDate_str">10/10/2016</label>*@
                                        @Html.DisplayFor(model => model.immunizations[i].RefusedDate_str)
                                        @Html.HiddenFor(model => model.immunizations[i].RefusedDate_str)
                                    </div>
                                    <div class="immDate5">
                                        @*<label ID="nextDate_imm" title="NextImmunDate_str">10/10/2016</label>*@
                                        @Html.DisplayFor(model => model.immunizations[i].NextImmunDate_str)
                                        @Html.HiddenFor(model => model.immunizations[i].NextImmunDate_str)
                                    </div>
                                    <div class="immDate6">
                                        @*<label ID="doctor_imm" title="Doctor">Doctor</label>*@
                                        @Html.DisplayFor(model => model.immunizations[i].Doctor)
                                        @Html.HiddenFor(model => model.immunizations[i].Doctor)
                                    </div>
                                </div>
                                    }
                                }
                        </div>

                    </div>
                    <!-- Immunizations Summary e -->
                    <!-- Risk Factors b -->
                    <div class="medClass">
                        <div class="checkLabel">
                        </div>
                        <div class="checkBoxDiv">
                            <label ID="Label65">Risk Factors</label>
                        </div>
                    </div>
                    <div id="riskDiv" class="medDivClass showXScroll">
                        <div class="risk_Div_h">
                            <div class="riskDate1">
                                <label>Risk Factor</label>
                            </div>
                            <div class="riskDate2">
                                <label>Age</label>
                            </div>
                            <div class="riskDate3">
                                <label>St.Date</label>
                            </div>
                            <div class="riskDate4">
                                <label>Life Stage</label>
                            </div>
                            <div class="riskDate5">
                                <label>Sub.Date</label>
                            </div>
                            <div class="riskDate6">
                                <label>Exposure Details</label>
                            </div>
                            <div class="riskDate7">
                                <label>Notes</label>
                            </div>
                        </div>
                        <div class="risk_Div">
                            @if (Model.riskfactors != null)
                                {
                                    for (int i = 0; i < Model.riskfactors.Count; i++)
                                    {
                                <div style="position:relative;float:left">
                                    <div class="riskDate1">
                                        @Html.DisplayFor(model => model.riskfactors[i].riskfactor)
                                        @Html.HiddenFor(model => model.riskfactors[i].riskfactor)
                                    </div>
                                    <div class="riskDate2">
                                        @Html.DisplayFor(model => model.riskfactors[i].onsetAge)
                                        @Html.HiddenFor(model => model.riskfactors[i].onsetAge)
                                    </div>
                                    <div class="riskDate3">
                                        @Html.DisplayFor(model => model.riskfactors[i].startDate)
                                        @Html.HiddenFor(model => model.riskfactors[i].startDate)
                                    </div>
                                    <div class="riskDate4">
                                        @Html.DisplayFor(model => model.riskfactors[i].lifeStage)
                                        @Html.HiddenFor(model => model.riskfactors[i].lifeStage)
                                    </div>
                                    <div class="riskDate5">
                                        @Html.DisplayFor(model => model.riskfactors[i].submitDateStr)
                                        @Html.HiddenFor(model => model.riskfactors[i].submitDateStr)
                                    </div>
                                    <div class="riskDate6 wrap_">
                                        @Html.DisplayFor(model => model.riskfactors[i].exposureDetails)
                                        @Html.HiddenFor(model => model.riskfactors[i].exposureDetails)
                                    </div>
                                    <div class="riskDate7 wrap_">
                                        @Html.DisplayFor(model => model.riskfactors[i].notes)
                                        @Html.HiddenFor(model => model.riskfactors[i].notes)
                                    </div>
                                </div>
                                    }
                                }
                        </div>
                    </div>
                    <!-- Risk Factors e -->
                    <!-- Labs Documents b -->
                    <div id="Div1" class="medClass">
                        <div id="Div2" class="checkLabel">
                        </div>
                        <div id="extDocPlug">
                            <label ID="Label65">Labs</label>
                        </div>
                    </div>
                    <div id="extDocDiv" class="medDivClass showXScroll">
                        <div class="docDiv_h">
                            <div class="docDate1">
                                <label>Labs Name</label>
                            </div>
                            <div class="docDate2">
                                <label>Date</label>
                            </div>
                            <div class="docDate3">
                                <label>Type</label>
                            </div>
                        </div>
                        <div class="docDiv">
                            @if (Model.labs != null)
                                {
                                    for (int i = 0; i < Model.labs.Count; i++)
                                    {
                                <div style="position:relative;float:left">
                                    <div class="docDate1">
                                        @*@Html.DisplayFor(model => model.labs[i].name)*@
                                        @Html.DisplayFor(model => model.labs[i].hl7Name)
                                        @*@Html.HiddenFor(model => model.labs[i].name)*@
                                        @Html.HiddenFor(model => model.labs[i].hl7Name)
                                    </div>
                                    <div class="docDate2">
                                        @Html.DisplayFor(model => model.labs[i].date)
                                        @Html.HiddenFor(model => model.labs[i].date)
                                    </div>
                                    <div class="docDate3">
                                        @Html.DisplayFor(model => model.labs[i].type)
                                        @Html.HiddenFor(model => model.labs[i].type)
                                    </div>
                                </div>
                                    }
                                }
                        </div>
                    </div>
                    <!-- Labs Documents e -->
                    <!-- External Documents b -->
                    <div id="Div1" class="medClass">
                        <div id="Div2" class="checkLabel">
                        </div>
                        <div id="extDocPlug">
                            <label ID="Label65">External Documents</label>
                        </div>
                    </div>
                    <div id="extDocDiv" class="medDivClass showXScroll">
                        <div class="docDiv_h">
                            <div class="docDate1">
                                <label>Document Name</label>
                            </div>
                            <div class="docDate2">
                                <label>Date</label>
                            </div>
                            <div class="docDate3">
                                <label>Document Type</label>
                            </div>
                        </div>
                        <div class="docDiv">
                            @if (Model.externalDocuments != null)
                                {
                                    for (int i = 0; i < Model.externalDocuments.Count; i++)
                                    {
                                <div style="position:relative;float:left">
                                    <div class="docDate1" title=@( Model.externalDocuments[i].name != null ? Model.externalDocuments[i].name.Replace(" ",""):"")>
                                        @Html.DisplayFor(model => model.externalDocuments[i].name)
                                        @Html.HiddenFor(model => model.externalDocuments[i].name)
                                        @Html.HiddenFor(model => model.externalDocuments[i].url)
                                        @Html.HiddenFor(model => model.externalDocuments[i].officeId)
                                    </div>
                                    <div class="docDate2">
                                        @Html.DisplayFor(model => model.externalDocuments[i].date)
                                        @Html.HiddenFor(model => model.externalDocuments[i].date)
                                    </div>
                                    <div class="docDate3">
                                        @Html.DisplayFor(model => model.externalDocuments[i].type)
                                        @Html.HiddenFor(model => model.externalDocuments[i].type)
                                    </div>
                                </div>
                                    }
                                }
                        </div>
                    </div>
                    <!-- External Documents e -->
                </div>
            </div>


            <div id="footer" class="clearRight">
                <div id="buttonsDiv">
                    <button ID="letters_print" class="subButton aaa"
                            name="Letters Print" value="Letters Print">
                        Letters Print
                    </button>
                    <button ID="immunization_print" class="subButton"
                            name="Immunization Summary Print" value="Immunization Summary Print">
                        Immunization Summary Print
                    </button>
                    <button ID="cpp_print" class="subButton" name="CPP Print"
                            value="CPP Print">
                        CPP Print
                    </button>
                    <button ID="submit_print" class="subButton submit_print_class"
                            name="Patient Data Chart Print" value="Patient Data Chart Print">
                        Patient Data Chart Print
                    </button>
                </div>
            </div>
        </div>
    }
</body>