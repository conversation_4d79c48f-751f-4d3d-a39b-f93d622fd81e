﻿@model Cerebrum.ViewModels.Inventory.VMInventoryTestDevice

@using (Html.BeginForm("AssignTestDevice", "StoreInventory", new { area = "AdminUser" }, FormMethod.Post, true, new { @id = "frm-patient-test-device" }))
{
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
        <h4 class="modal-title">Device for: @Model.PatientFullName</h4>
    </div>
    <div class="modal-body">
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => model.PatientEquipmentId)
        @Html.HiddenFor(model => model.DeviceNumberId)
        @Html.HiddenFor(model => model.PracticeId)
        @Html.HiddenFor(model => model.OfficeId)
        @Html.HiddenFor(model => model.PatientId)
        @Html.HiddenFor(model => model.PatientFullName)
        @Html.HiddenFor(model => model.AppointmentDate)
        @Html.HiddenFor(model => model.AppointmentId)
        @Html.HiddenFor(model => model.AppointmentTestId)
        @Html.HiddenFor(model => model.TestId)
        @Html.HiddenFor(model => model.TestName)
        @Html.HiddenFor(model => model.HasDevice)

        @if (Model.HasDevice)
        {
            <div class="alert alert-warning" role="alert">Patient already has a device</div>
        }
        <div class="form-horizontal">
            @if (!String.IsNullOrWhiteSpace(Model.TestName))
            {
                <div class="form-group form-group-sm">
                    @Html.LabelFor(model => model.TestName, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-4">
                        <p class="form-control-static">@Html.DisplayFor(model => model.TestName)</p>
                    </div>
                </div>
            }
            <div class="form-group">
                @Html.LabelFor(model => model.DeviceNumber, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.DeviceNumber, new { htmlAttributes = new { @class = "form-control device-auto-fill" } })
                    @Html.ValidationMessageFor(model => model.DeviceNumber, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.OfficeTechId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @{
                        var techs = (SelectList)ViewBag.OfficeTechs;
                    }
                    @Html.DropDownListFor(model => model.OfficeTechId, techs, new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.OfficeTechId, "", new { @class = "text-danger" })
                </div>
            </div>

            @if (Model.HasDevice)
            {
                <div class="form-group form-group-sm">

                    <div class="col-md-offset-2 col-md-4">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.MarkAsReturned) <span class="checkbox-text">@Html.DisplayNameFor(model => model.MarkAsReturned)</span>
                            </label>
                            @Html.ValidationMessageFor(model => model.MarkAsReturned, "", new { @class = "text-danger" })
                        </div>
                    </div>
                </div>
            }
            <div class="form-group">
                @Html.LabelFor(model => model.Notes, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.TextAreaFor(model => model.Notes, new { @class = "form-control count-text", @rows = 4, data_max_length = 500 })
                    @Html.ValidationMessageFor(model => model.Notes, "", new { @class = "text-danger" })
                </div>
            </div>


        </div>

    </div>

    <div class="modal-footer">

        <button type="submit" data-modal-url="@Url.Action("", "", new { area = ""})"
                class="btn btn-sm btn-primary modal-submit-btn">
            Save
        </button>
        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>

    </div>
}


