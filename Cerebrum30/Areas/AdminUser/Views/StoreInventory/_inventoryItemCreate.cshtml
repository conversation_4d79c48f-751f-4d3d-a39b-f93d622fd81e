﻿@model Cerebrum.ViewModels.Inventory.VMInventoryItem

@using (Html.BeginForm("CreateInventoryItem", "StoreInventory", new { area = "adminuser" }, FormMethod.Post, true, new { @id = "frm-inventory-item-create", @class = "inventory-form" }))
{
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
        <h4 class="modal-title">Add New Item</h4>
    </div>
    <div class="modal-body">
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => model.InventoryId)
        @Html.HiddenFor(model => model.StatusId)

        <div class="form-horizontal">

            @Html.ValidationSummary(true, "", new { @class = "text-danger" })

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.OfficeId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.OfficeId, new SelectList(ViewBag.Offices, "Value", "Text", Model.OfficeId), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.DeviceNumber, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.EditorFor(model => model.DeviceNumber, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.DeviceNumber, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.DeviceTypeId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.DeviceTypeId, new SelectList(ViewBag.DeviceTypes, "Value", "Text", Model.DeviceTypeId), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.DeviceTypeId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Notes, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.TextAreaFor(model => model.Notes, new { @class = "form-control count-text", @rows = 4, data_max_length = 500 })
                    @Html.ValidationMessageFor(model => model.Notes, "", new { @class = "text-danger" })
                </div>
            </div>            
        </div>
    </div>

    <div class="modal-footer">

        <button type="submit" class="btn btn-sm btn-primary modal-submit-btn">
            Add New Item
        </button>
        <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>

    </div>
}

