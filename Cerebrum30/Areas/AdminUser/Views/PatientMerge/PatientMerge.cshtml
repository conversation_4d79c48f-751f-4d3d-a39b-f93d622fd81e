@model Cerebrum30.Areas.AdminUser.Models.PatientMergeModel
@{
    ViewBag.Title = "PatientMerge";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .container-fluid {
        float: left;
    }

    .back_col1 {
        background-color: rgb(201, 207, 211);
    }

    .back_col3 {
        background-color: rgb(232, 238, 241);
    }

    .back_row {
        background-color: rgb(192, 197, 201);
    }

    .borderTop {
        border-top: 1px solid rgb(192, 197, 201) !important;
    }

    .borderBot {
        border-top: 1px solid rgb(192, 197, 201) !important;
    }

    .borderRight {
        border-top: 1px solid rgb(192, 197, 201) !important;
    }

    .subButton {
        -webkit-border-radius: 4px 4px 4px 4px;
        -moz-border-radius: 4px 4px 4px 4px;
        border-radius: 4px 4px 4px 4px;
        margin: 3px 0px 0px 5px;
        border: 1px solid white;
        position: relative;
        display: inline-block;
        float: left;
        background-color: rgb(192, 197, 201);
        margin-bottom: 2px;
    }

        .subButton:hover {
            background-color: rgb(201, 207, 211);
        }

    .padd_t_b_2 {
        padding-top: 2px;
        padding-bottom: 2px;
    }

    .margin_t_1 {
        margin-top: 2px;
    }

    .height_20 {
        height: 20px;
    }

    .input_st {
        background-color: rgb(232, 238, 241);
        padding: 0px;
    }
</style>

<script src="~/Scripts/PatientMerge.js"></script>
<head id="Head1">

</head>
<body>
    @using (Html.BeginForm("PatientMerge", "PatientMerge", FormMethod.Post, new { mergeModel = Model }))
    {
        @Html.AntiForgeryToken()
        <div class="container-fluid" style="width:1200px;">
            <div class="col-sm-12 text-center back_row"><h2>PatientMerge</h2></div>
            <div class="col-sm-12 back_col1" style="padding-left: 0px;">
                <div class="col-sm-2">
                    <span id="patNameSpan"></span>

                </div>
                <div class="col-sm-2">
                    <span id="patNameSpan">Last Name</span>

                </div>
                <div class="col-sm-2">
                    <span id="patNameSpan">First Name</span>

                </div>
                <div class="col-sm-1">
                    <span id="patNameSpan">HIN</span>

                </div>
                <div class="col-sm-1">
                    <span id="patNameSpan">Ver</span>

                </div>
                <div class="col-sm-2">
                    <span id="patNameSpan">DOB</span>

                </div>
                @*<div class="col-sm-1">
                    <span id="patNameSpan">Phone</span>

                </div>*@
                <div class="col-sm-1">
                    <span id="patNameSpan">PID</span>

                </div>
            </div>
            <div class="col-sm-12 borderTop borderRight back_col3" style="padding-left: 0px;">
                <div class="col-sm-2 back_col1 padd_t_b_2">
                    <span id="patNameSpan">Profile to Stay</span>

                </div>
                <div class="col-sm-2">
                    @*<span id="lastName_stay"></span>*@
                    @Html.EditorFor(model => model.patientStayText, new { htmlAttributes = new { @class = "form-control margin_t_1 height_20 input_st" } })
                    @Html.HiddenFor(m => m.patientStayId)
                </div>
                <div class="col-sm-2">
                    <span id="firstName_stay"></span>

                </div>
                <div class="col-sm-1">
                    <span id="ohip_stay"></span>

                </div>
                <div class="col-sm-1">
                    <span id="ver_stay"></span>

                </div>
                <div class="col-sm-2">
                    <span id="dob_stay"></span>

                </div>
                @*<div class="col-sm-1">
                    <span id="phone_stay"></span>

                </div>*@
                <div class="col-sm-1">
                    <span id="pid_stay"></span>

                </div>
            </div>
            <div class="col-sm-12 borderTop borderBot borderRight back_col3" style="padding-left: 0px;">
                <div class="col-sm-2 back_col1 padd_t_b_2">
                    <span id="patNameSpan">Profile to Go</span>

                </div>
                <div class="col-sm-2">
                    @Html.EditorFor(model => model.patientGoText, new { htmlAttributes = new { @class = "form-control margin_t_1 height_20 input_st" } })
                    @Html.HiddenFor(m => m.patientGoId)
                </div>
                <div class="col-sm-2">
                    <span id="firstName_go"></span>

                </div>
                <div class="col-sm-1">
                    <span id="ohip_go"></span>

                </div>
                <div class="col-sm-1">
                    <span id="ver_go"></span>

                </div>
                <div class="col-sm-2">
                    <span id="dob_go"></span>

                </div>
                @*<div class="col-sm-1">
                    <span id="phone_go"></span>

                </div>*@
                <div class="col-sm-1">
                    <span id="pid_go"></span>

                </div>
            </div>
            <div class="col-sm-12 back_col1 borderTop" style="padding-left: 0px;">
                <div class="col-sm-2" style="margin-left: 500px;">
                    <button ID="submit_merge" class="subButton"
                            name="submit_merge" value="submit_merge">
                        Join Profiles
                    </button>
                </div>
            </div>
            <div class="col-sm-12 back_col1 borderTop" style="padding-left: 0px;">
                <div class="col-sm-1">
                </div>
                <div class="col-sm-11 text-center">
                    @Html.LabelFor(m => m.message, @Model.message??"", new {})
                    @*@Html.LabelFor(model => model.message, new { htmlAttributes = new { @class = "" } }, new {})*@
                </div>
            </div>
        </div>
    }
</body>

