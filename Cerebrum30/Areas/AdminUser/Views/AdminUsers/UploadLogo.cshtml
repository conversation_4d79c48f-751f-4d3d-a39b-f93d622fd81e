@model Cerebrum30.Models.UploadSign
@{
    Layout = "~/Views/Shared/_LayoutDemographics.cshtml";
}

<!DOCTYPE html>

<html>
<head>
    <script src="~/Scripts/jquery-3.1.0.min.js"></script>
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <meta name="viewport" content="width=device-width" />
    <title>Upload</title>
    <script>
        $(document).ready(function () {
            $("#close").click(function () { window.close(); });
        });
    </script>
    <style>
        body {
            padding-top: 0px !important;
            padding-bottom: 0px !important;
            background-color: rgb(233, 238, 240) !important;
        }

        #mainDiv {
            width: 450px;
            height: 85px;
        }

            #mainDiv:after {
                content: " ";
                display: block;
                clear: both;
            }

        .headerSig {
            width: 450px;
            float: left;
        }

            .headerSig span {
                display: inline-block;
                float: left;
                margin-left: 150px;
                font-size: 15px;
                font-weight: 700;
            }

        .uploadDiv1 {
            width: 450px;
            float: left;
        }

        .uploadDiv1_1 {
            width: 350px;
            float: left;
        }

        .uploadDiv1_2 {
            width: 100px;
            float: left;
        }

            .uploadDiv1_2 input {
                width: 115px;
            }

        .closeDiv {
            width: 450px;
            float: left;
            /*margin-top: 15px;*/
        }

            .closeDiv a {
                background-color: rgb(207, 210, 212);
                height: 23px;
                margin-left: 75px;
                padding-top: 0px;
            }

        .green_ {
           color:#4b9e02 !important;
        }

        .select_ {
            height: 27px;
            padding-top: 3px;
            width: 120px;
        }
    </style>
</head>
<body>
    <div class="" id="mainDiv">
        @using (Html.BeginForm("UploadLogo", "AdminUsers", FormMethod.Post, new { enctype = "multipart/form-data" }))
        {
            @Html.AntiForgeryToken()
            @Html.HiddenFor(model => model.message)
            @Html.HiddenFor(model => model.uploadingUserId)
            <div class="headerSig">
                <span>Upload Logo</span>
            </div>

            <div class="form-group">
                <div class="col-sm-10  col-sm-offset-2">
                    @Html.TextBoxFor(model => model.Files, new { type = "file", name = "Files" })
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2">
                    Logo
                </div>
                <div class="col-sm-4">
                    @Html.EnumDropDownListFor(model => model.imgLocation, htmlAttributes: new { @class = "form-control select_" })
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2">
                </div>
                <div class="col-sm-4 uploadDiv1_2">
                    <input type="submit" value="Upload Logo" />
                </div>
            </div>       
             <div class="form-group">
                <div class="col-sm-2">
                </div>
                <div class="col-sm-4 closeDiv">
                    @Html.ActionLink("Close", null, null, null, new { @class = "btn btn-default", id = "close" })
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-12">
                    @Html.LabelFor(model => model.message, Model.message, new { @class = "green_" })
                </div>
            </div>



            @*<div class="uploadDiv1">
                    <div class="uploadDiv1_1">
                        @Html.TextBoxFor(model => model.Files, new { type = "file", name = "Files" })
                    </div>
                    <div class="closeDiv">
                        @Html.EnumDropDownListFor(model => model.imgLocation, htmlAttributes: new { @class = "form-control" })
                    </div>
                </div>*@
            @*<div class="uploadDiv1_2">
                    <input type="submit" value="Upload Logo" />
                </div>

                <div class="messageSig">
                    @Html.LabelFor(model => model.message, Model.message, new { @class = "green_" })
                </div>
                <div class="closeDiv">
                    @Html.ActionLink("Close", null, null, null, new { @class = "btn btn-default", id = "close" })
                </div>*@
        }
    </div>
</body>
</html>
@*@Html.EnumDropDownListFor(model => model.payment, htmlAttributes: new { @class = "form-control", tabindex = 29 })*@
