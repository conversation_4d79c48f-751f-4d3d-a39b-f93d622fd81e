@{
    ViewBag.Title = "Personnel Performance";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
    int count = (int)ViewBag.TestsCount;
    if (count > 15)
    {
        count = 15;
    }
}

@section customcss{

}

@section scripts{
    <script src="~/Areas/AdminUser/Scripts/personnel-performance.js"></script>
}

<style>
    table td, table th {
        padding: 5px; /* 'cellpadding' equivalent */
    }
</style>

<div class="form-horizontal" style="margin: 5px; padding: 5px;">
    <div class="row">
        <div class="col-sm-8" style="padding-bottom: 24px; padding: 5px; margin: 5px;">

            <fieldset>
                <legend>Show columns:</legend>
                <div class="col-sm-2">
                    <span><input id="chkGroupByOffice" name="chkGroupByOffice" type="checkbox" />&nbsp;&nbsp;Office</span>
                </div>
                <div class="col-sm-2">
                    <span><input id="chkGroupByTest" name="chkGroupByTest" type="checkbox" />&nbsp;&nbsp;Test</span>
                </div>
            </fieldset>
        </div>
    </div>
    <br />

    @using (Html.BeginForm("GetTechniciansPerformance", "PersonnelPerformance", FormMethod.Post, new { @id = "form-search-technicians" }))
    {
        <input type="hidden" id="groupByOffice" name="groupByOffice" />
        <input type="hidden" id="groupByTest" name="groupByTest" />
        <div class="row">
            <div class="form-group col-md-12" style="vertical-align: top; margin: 5px; padding:5px;">
                <div class="col-md-2">
                    @Html.Label("Office", "", new { @class = "control-label col-md-4  flex-md-nowrap" })
                    <div class="col-md-12">
                        @Html.DropDownList("officeId", new MultiSelectList(ViewBag.Offices, "Id", "Name"), new { @class = "form-control" })
                    </div>
                </div>
                <div class="col-md-2">
                    @Html.Label("Test", "", new { @class = "control-label col-md-4  flex-md-nowrap" })
                    <div class="col-md-12">
                        @Html.ListBox("tests", new MultiSelectList(ViewBag.Tests, "Value", "Text"), new { multiple = "multiple", @class = "form-control", @size = @count })
                    </div>
                </div>
                <div class="col-md-2">
                    @Html.Label("Technician", "", new { @class = "control-label col-md-4  flex-md-nowrap" })
                    <div class="col-md-12">
                        @Html.DropDownList("technicianId", new MultiSelectList(ViewBag.Techs, "Value", "Text"), new { @class = "form-control" })
                    </div>
                </div>
                <div class="col-md-6" style="margin-top:5px;">
                    <div class="form-group">
                        <div class="col-md-3">
                            @Html.Label("Start Date", "")
                            <input id="dateStart" name="dateStart" type="text" class="form-control datepicker" value='@DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd")' />
                        </div>
                        <div class="col-md-3">
                            @Html.Label("End Date", "")
                            <input id="dateEnd" name="dateEnd" type="text" class="form-control datepicker" value='@DateTime.Now.ToString("yyyy-MM-dd")' />
                        </div>
                        <div class="col-md-3" style="margin-top:19px;"><input type="button" id="btnSearchTech" class="btn btn-primary" value="Search" /></div>
                    </div>

                    <div class="col-md-12" id="tech-search-result">

                    </div>
                </div>

            </div>
        </div>
    }

</div>


