@model Cerebrum.ViewModels.AdminUsersUao.VMEconsultUserUao
@using Cerebrum.ViewModels.User;
@{
    Layout = null;
    VMUser user = (VMUser)ViewBag.UserInfo;
    var header = "Add new UAO for " + user.FullNameType;
    // for new - default checked
    string strChecked = "checked";

    if (Model?.UserUaoId > 0)
    {
        header = "Edit UAO for " + user.FullNameType;
        if (!Model.IsActive)
        {
            strChecked = string.Empty;
        }
    }

    var uaoList = (List<SelectListItem>)ViewBag.ActiveUaoList;
}

@Html.ModalHeader(header)
<div class="col-md-12">
    @Html.AntiForgeryToken()
    <input type="hidden" id="Id" name="Id" value="@Model.UserUaoId" />
    <table class="table table-condensed table-bordered table-responsive">
        <tr>
            <td>@Html.Label("UAO Name", "", new { @class = "control-label" })</td>
            <td>
                <div class="col-md-8">
                    <select class="form-control" id="EconsultUao" name="EconsultUao">
                        <option value="0">Choose One</option>
                        @foreach (var item in uaoList)
                        {
                            if (item.Value == Model.UaoId.ToString())
                            {
                                <option value="@item.Value" selected>@item.Text</option>
                            }
                            else
                            {
                                <option value="@item.Value">@item.Text</option>
                            }
                        }
                    </select>
                </div>
            </td>
        </tr>
        <tr>
            <td>@Html.LabelFor(model => model.IsActive)</td>
            <td><div class="col-md-8"><input type="checkbox" id="IsActive" name="IsActive" @strChecked></div></td>
        </tr>
    </table>
    <div class='text-danger' id="div-error-add-edit-user-uao-admin"></div>
</div>
<hr />
<br />
<div class="modal-footer">
    <button type="button" class="btn btn-default btn-sm" id="btn-save-user-uao-admin" 
            data-user-uao-id="@Model.UserUaoId" data-user-id="@Model.UserId">Save</button>
    <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Close</button>
</div>

