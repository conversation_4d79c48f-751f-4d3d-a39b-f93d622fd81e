﻿@model Cerebrum.ViewModels.CDS.CdsExportCon
@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<script src="~/Scripts/cds_js.js"></script>
<style>
    .forTextBox {
        height: 21px;
        margin-top: 7px;
        padding: 0px;
    }

    .forTextBoxE {
        height: 21px;
        margin-top: 1px;
        margin-bottom: 2px;
        padding: 0px;
    }

    .remLpadd {
        padding-left: 0px;
    }

    .remRpadd {
        padding-right: 0px;
    }

    .margTop3 {
        margin-top: 7px;
    }

    .disabledbutton {
        pointer-events: none;
        opacity: 0.4;
    }

    .subButton {
        -webkit-border-radius: 4px 4px 4px 4px;
        -moz-border-radius: 4px 4px 4px 4px;
        border-radius: 4px 4px 4px 4px;
        margin: 3px 0px 0px 5px;
        border: 1px solid white;
        position: relative;
        display: inline-block;
        float: left;
        background-color: rgb(192, 197, 201);
        margin-bottom: 2px;
    }

        .subButton:hover {
            background-color: rgb(201, 207, 211);
        }

    .forDiv {
        -webkit-border-radius: 4px 4px 4px 4px;
        -moz-border-radius: 4px 4px 4px 4px;
        border-radius: 4px 4px 4px 4px;
        /*margin: 3px 0px 0px 5px;*/
        border: 1px solid rgb(176, 181, 184);
        /*background-color: rgb(192, 197, 201);*/
        margin-bottom: 2px;
    }

    .marginBottom7 {
        margin-bottom: 7px;
    }

    #categoryList th, tr, td {
        padding: 0px !important;
    }

    .forTB input {
        margin-top: 0px !important;
    }

    .padding_l_r_0 {
        padding-left: 0px;
        padding-right: 0px;
    }
</style>
<div class="container-fluid">
    <div class="container pull-left" style="width:900px;">
        <div class="row text-center" style="background-color:rgb(206, 212, 216);"><h2>PATIENT DATA EXPORT</h2></div>
        @using (Html.BeginForm("ExportCDS", "ExportCDS", FormMethod.Post, new { expModel = Model }))
        {
            @Html.HiddenFor(t => t.Num_OfGroups_)
            @Html.HiddenFor(t => t.DirZ_Path)
            @Html.HiddenFor(t => t.isReadyForDownload)
            <div id="mainDiv" class="row" style="background-color:rgb(206, 212, 216);">
                <div id="leftDiv" class="col-md-6" style="">
                    <div class="row forDiv">
                        <div class="col-md-6" style="">
                            <div class="radio">
                                <label><input id="radioPatient" onchange="Setdata(0)" type="radio" name="optradio">Patient</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <input id="ptnText" class="form-control forTextBox" type="text" />
                            @Html.HiddenFor(m => m.patientRecordId)
                        </div>
                    </div>
                    <div class="row forDiv">
                        <div class="col-md-6" style="">
                            <div class="radio">
                                <label><input id="radioDoctor" type="radio" onchange="Setdata(1)" name="optradio">Doctor's All Patients</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            @Html.EditorFor(model => model.docText, new { htmlAttributes = new { @class = "form-control forTextBox" } })
                            @Html.HiddenFor(m => m.practicelDoctorId)
                        </div>
                        <div class="col-md-6" style="">
                        </div>
                        <div class="col-md-6">
                            <div class="col-md-2 remLpadd remRpadd">
                            </div>
                            <div class="col-md-3 remLpadd remRpadd">
                                @Html.HiddenFor(m => m.isPerDoctor)
                            </div>
                        </div>
                    </div>
                    <div class="row forDiv">
                        <div class="col-md-12">
                            <div class="col-md-1 padding_l_r_0" style="">
                                @Html.CheckBoxFor(m => m.createFinalReadMe)
                            </div>
                            <div class="col-md-11" style="">
                                @Html.DisplayNameFor(m => m.createFinalReadMe)
                            </div>
                        </div>

                    </div>
                    <div class="row forDiv">
                        <div class="col-md-12">
                            <div class="col-md-1 padding_l_r_0" style="">
                                @Html.CheckBoxFor(m => m.NormalAbnormalFlag_chb)
                            </div>
                            <div class="col-md-11" style="">
                                @Html.DisplayNameFor(m => m.NormalAbnormalFlag_chb)
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="col-md-1 padding_l_r_0" style="">
                                @Html.CheckBoxFor(m => m.Purpose_chb)
                            </div>
                            <div class="col-md-11" style="">
                                @Html.DisplayNameFor(m => m.Purpose_chb)
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="col-md-1 padding_l_r_0" style="">
                                @Html.CheckBoxFor(m => m.PersonStatusCode_chb)
                            </div>
                            <div class="col-md-11" style="">
                                @Html.DisplayNameFor(m => m.PersonStatusCode_chb)
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="col-md-1 padding_l_r_0" style="">
                                @Html.CheckBoxFor(m => m.Address_chb)
                            </div>
                            <div class="col-md-11" style="">
                                @Html.DisplayNameFor(m => m.Address_chb)
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="col-md-1 padding_l_r_0" style="">
                                @Html.CheckBoxFor(m => m.LaboratoryResultsReferenceRange_chb)
                            </div>
                            <div class="col-md-11" style="">
                                @Html.DisplayNameFor(m => m.LaboratoryResultsReferenceRange_chb)
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="col-md-1 padding_l_r_0" style="">
                                @Html.CheckBoxFor(m => m.ReportsSourceAuthorPhysician_chb)
                            </div>
                            <div class="col-md-11" style="">
                                @Html.DisplayNameFor(m => m.ReportsSourceAuthorPhysician_chb)
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="col-md-1 padding_l_r_0" style="">
                                @Html.CheckBoxFor(m => m.YNIndicator_chb)
                            </div>
                            <div class="col-md-11" style="">
                                @Html.DisplayNameFor(m => m.YNIndicator_chb)
                            </div>
                        </div>
                    </div>
                </div>
                <div id="rightDiv" class="col-md-6 forDiv" style="height: 394px;">
                    <table class="table" id="categoryList" name="categoryList" style="font-size: 14px;">
                        <thead>
                            <tr class="vertical-center">
                                <th></th>
                                <th>Select Category</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model != null && Model.categories != null && Model.categories.Count > 0)
                            {
                                for (int i = 0; i < Model.categories.Count; i++)
                                {
                                    <tr>
                                        <td>
                                            @Html.CheckBoxFor(model => model.categories[i].isChecked, new { id = @Model.categories[i].isChecked })
                                        </td>
                                        <td>
                                            @Html.DisplayFor(model => model.categories[i].category)
                                            @Html.HiddenFor(model => model.categories[i].category)
                                        </td>
                                        <td>
                                            @Html.HiddenFor(model => model.categories[i].value)
                                        </td>
                                    </tr>
                                }
                            }
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row" style="background-color:rgb(206, 212, 216);padding-top: 16px;">
                <div class="col-md-2">
                    Export file to:
                </div>
                <div class="col-md-10">
                    @if (Model.offices.Count() == 1)
                    {
                        @Html.DropDownList("officeId", new SelectList(Model.offices, "id", "name", Model.offices[0].id), htmlAttributes: new { @class = "form-control select-office-change" })
                    }
                    else
                    {
                        @Html.DropDownList("officeId", new SelectList(Model.offices, "id", "name"), "Please Select Office", htmlAttributes: new { @class = "form-control select-office-change" })
                    }
                </div>
            </div>
            <div class="row text-center" style="background-color:rgb(206, 212, 216);padding-top: 40px;">
                <button id="submitBtn" class="subButton subButton_" type="submit" value="submit" name="submit" style="height: 21px;width: 150px;margin-left: 15px;">Create Export Data</button>
            </div>
            <div class="row text-center" style="background-color:rgb(206, 212, 216);padding-top: 40px;">
                @Html.LabelFor(m => m.message, @Model.message ?? "")
            </div>
        }
    </div>
</div>
<script language="javascript">
    // remove it when find problem with cds_js.js
    function Setdata(id) {
        if (id == 0) {
            document.getElementById("isPerDoctor").value = false;
        }
        else {
            document.getElementById("isPerDoctor").value = true;
        }
    }
</script>
