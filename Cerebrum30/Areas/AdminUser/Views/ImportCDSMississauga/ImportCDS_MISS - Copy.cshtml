@model Cerebrum30.Areas.AdminUser.Models.CdsImport
@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!DOCTYPE html>

<html>
<head>
    @*<link href="~/Content/admin-user.css" rel="stylesheet" />*@
    <script src="~/Scripts/jquery-3.1.0.min.js"></script>
    <script src="~/Scripts/jquery.validate.min.js"></script>
    <script src="~/Scripts/jquery.validate.unobtrusive.min.js"></script>
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <meta name="viewport" content="width=device-width" />
    <title>Upload Miss</title>
    <script>
        $(document).ready(function () {
            $("#close").click(function () { window.close(); });
        });
    </script>
    <style>
        body {
            padding-top: 0px !important;
            padding-bottom: 0px !important;
            background-color: rgb(233, 238, 240) !important;
        }

        #mainDiv {
            width: 550px;
            background-color: rgb(200, 205, 207) !important;
            margin-top: 40px;
        }

        .headerS {
            font-size: 21px;
            font-weight: 700;
            background-color: rgb(168, 174, 176);
        }

        .rowsS {
            background-color: rgb(193, 198, 200);
        }

        .green_ {
            color: #4b9e02 !important;
        }

        .wholeWidth input {
            max-width: 500px;
            width: 100%;
        }

        .btnWidth {
            width: 180px;
        }

        .width100 {
            width: 100px;
        }

        .cl_bt a, input, select {
            height: 25px !important;
            padding-top: 2px !important;
        }

        .inpSt {
            display: inline-block;
            margin-left: 15px !important;
        }

        .abc_margin15 {
            margin-top: 10px;
            display: inline-block;
        }

        ._margin10 input {
            margin-top: 13px;
            padding-top: 2px !important;
        }
    </style>
</head>
<body>
    <div class="" id="mainDiv">
        @using (Html.BeginForm("ImportCDS_MISS", "ImportCDSMississauga", FormMethod.Post, new { enctype = "multipart/form-data" }))
        {
            @Html.HiddenFor(model => model.message)
            <div class="row headerS" style="">
                <div class="form-group">
                    <div class="col-md-12 text-center" style="">
                        <span>Import Cds Data ( V4 )</span>
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.isFilePath)
                    </div>
                    <div class="col-md-9 wholeWidth cl_bt">
                        @Html.TextBoxFor(model => model.folderPath, new { @class = "form-control", id = "folderPathId" })
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    <div class="col-md-5">
                        @Html.LabelFor(model => model.docBillingNum)
                    </div>
                    <div class="col-md-4 cl_bt">
                        @Html.TextBoxFor(model => model.docBillingNum, new { @class = "form-control", id = "docBillingNumId" })
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    <div class="col-md-5">
                        @Html.LabelFor(model => model.practiceId)
                    </div>
                    <div class="col-md-4 cl_bt">
                        @Html.TextBoxFor(model => model.practiceId, new { @class = "form-control", id = "practiceId__" })
                        @Html.ValidationMessageFor(model => model.practiceId, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    <div class="col-md-5">
                        @Html.LabelFor(model => model.officesList)
                    </div>
                    <div class="col-md-4 cl_bt">
                        @Html.DropDownListFor(model => model.officeId, Model.officesList, "", new { @class = "form-control selec_" })
                    </div>
                    <div class="col-md-3 cl_bt">
                        <input type="submit" name="update" value="Update" class="width100 btn btn-default" />
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    <div class="col-md-4  col-md-offset-4 cl_bt">
                        <input type="submit" name="importdata" value="Import Data" class="btn btn-default btnWidth" />
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    @*<div class="col-md-3 cl_bt">
                        <div class="radio">
                            <span style="display:inline-block;margin-top:7px;">CDS50: </span> @Html.RadioButtonFor(t => t.cds_version, "CDS50", new { @class = "inpSt" })
                        </div>
                    </div>
                    <div class="col-md-3 cl_bt">
                        <div class="radio">
                            <span style="display:inline-block;margin-top:7px;">CDS40: </span> @Html.RadioButtonFor(t => t.cds_version, "CDS40", new { @class = "inpSt", @checked = "checked" })
                        </div>
                    </div>*@
                    <div class="col-md-2 cl_bt" style="margin-top:7px;">
                        <span class="abc_margin15">@Html.DisplayNameFor(m => m.isC2C3)</span>
                    </div>
                    <div class="col-md-1 _margin10">
                        @Html.CheckBoxFor(m => m.isC2C3)
                    </div>
                    <div class="col-md-2 cl_bt " style="margin-top:7px;padding-right:0px;padding-left:0px;">
                        <span class="abc_margin15">@Html.DisplayNameFor(m => m.isPerPractice)</span>
                    </div>
                    <div class="col-md-1 _margin10">
                        @Html.CheckBoxFor(m => m.isPerPractice)
                    </div>
                </div>
            </div>
            <div class="row rowsS">
                <div class="form-group">
                    <div class="col-md-12 text-center" style="min-height:41px;">
                        @Html.LabelFor(model => model.message, Model.message, new { @class = "green_" })
                    </div>
                </div>
            </div>
            <div class="row rowsS">
                <div class="form-group">
                    <div class="col-md-12 text-center cl_bt" style="margin-bottom:5px;">
                        @Html.ActionLink("Close", null, null, null, new { @class = "btn btn-default", id = "close" })
                    </div>
                </div>
            </div>
        }
    </div>
</body>
</html>
