@model Cerebrum30.Areas.AdminUser.Models.CdsImport
@{
    ViewBag.ModuleName = "CDS Import V4";
    ViewBag.Title = "CDS Import V4";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss{

}

@section scripts{
    <script src="~/Scripts/cds_js.js"></script>
}

<script>

        $(document).ready(function () {
            $("#close").click(function () { window.close(); });

            $(document).on('click', '.btn-frm-submit-cds', function (e) {
                e.preventDefault();

                var btn = $(this);
                var btnValue = btn.data('submit-type');
                $('#frm-cds-import-data #importdatakey').val(btnValue);

                $('#ajax-loader').show();
                $('#frm-cds-import-data').submit();
            });
        });

</script>

<style>
    body {
        padding-top: 0px !important;
        padding-bottom: 0px !important;
        background-color: rgb(233, 238, 240) !important;
    }

    #mainDiv {
        width: 550px;
        background-color: rgb(200, 205, 207) !important;
        margin-top: 60px;
    }

    .headerS {
        font-size: 21px;
        font-weight: 700;
        background-color: rgb(168, 174, 176);
    }

    .rowsS {
        background-color: rgb(193, 198, 200);
    }

    .green_ {
        color: #4b9e02 !important;
    }

    .wholeWidth input {
        max-width: 500px;
        width: 100%;
    }

    .btnWidth {
        width: 180px;
    }

    .width100 {
        width: 100px;
    }

    .cl_bt a, input, select {
        height: 25px !important;
        padding-top: 2px !important;
    }

    .inpSt {
        display: inline-block;
        margin-left: 15px !important;
    }

    .abc_margin15 {
        margin-top: 10px;
        display: inline-block;
    }

    ._margin10 input {
        margin-top: 13px;
        padding-top: 2px !important;
    }
</style>

<div class="" id="mainDiv">
    @using (Html.BeginForm("ImportCDS_MISS", "ImportCDSMississauga", new { area = "adminuser" }, FormMethod.Post, true, new { enctype = "multipart/form-data", @id = "frm-cds-import-data" }))
    {
        @Html.HiddenFor(model => model.message)
        @Html.Hidden("importdatakey", "Import Data")
        <div class="row headerS" style="">
            <div class="form-group">
                <div class="col-md-12 text-center" style="">
                    <span>Import Cds Data ( V4 )</span>
                </div>
            </div>
        </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.isFilePath)
                    </div>
                    <div class="col-md-9 wholeWidth cl_bt">
                        @Html.TextBoxFor(model => model.folderPath, new { @class = "form-control", id = "folderPathId" })
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    <div class="col-md-5">
                        @Html.LabelFor(model => model.docBillingNum)
                    </div>
                    <div class="col-md-4 cl_bt">
                        @Html.TextBoxFor(model => model.docBillingNum, new { @class = "form-control", id = "docBillingNumId" })
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    <div class="col-md-5">
                        @Html.LabelFor(model => model.practiceId)
                    </div>
                    <div class="col-md-4 cl_bt">
                        @Html.TextBoxFor(model => model.practiceId, new { @class = "form-control", id = "practiceId__" })
                        @Html.ValidationMessageFor(model => model.practiceId, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    <div class="col-md-5">
                        @Html.LabelFor(model => model.officesList)
                    </div>
                    <div class="col-md-4 cl_bt">
                        @Html.DropDownListFor(model => model.officeId, Model.officesList, "", new { @class = "form-control selec_" })
                    </div>
                    <div class="col-md-3 cl_bt">
                        <button type="button" data-submit-type="Update" value="Update" class="width100 btn btn-default btn-frm-submit-cds">Update</button>
                        @*<input type="submit" name="update" value="Update" class="width100 btn btn-default" />*@
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    <div class="col-md-4  col-md-offset-4 cl_bt">
                        <button type="button" data-submit-type="Import Data" class="btn btn-default btnWidth btn-frm-submit-cds">Import Data</button>
                        @*<input type="submit" name="importdata" value="Import Data" class="btn btn-default btnWidth" />*@
                    </div>
                </div>
            </div>
            <div class="row rowsS" style="padding-top:5px;">
                <div class="form-group">
                    @*<div class="col-md-3 cl_bt">
                            <div class="radio">
                                <span style="display:inline-block;margin-top:7px;">CDS50: </span> @Html.RadioButtonFor(t => t.cds_version, "CDS50", new { @class = "inpSt" })
                            </div>
                        </div>
                        <div class="col-md-3 cl_bt">
                            <div class="radio">
                                <span style="display:inline-block;margin-top:7px;">CDS40: </span> @Html.RadioButtonFor(t => t.cds_version, "CDS40", new { @class = "inpSt", @checked = "checked" })
                            </div>
                        </div>*@
                    <div class="col-md-2 cl_bt" style="margin-top:7px;">
                        <span class="abc_margin15">@Html.DisplayNameFor(m => m.isC2C3)</span>
                    </div>
                    <div class="col-md-1 _margin10">
                        @Html.CheckBoxFor(m => m.isC2C3)
                    </div>
                    <div class="col-md-2 cl_bt " style="margin-top:7px;padding-right:0px;padding-left:0px;">
                        <span class="abc_margin15">@Html.DisplayNameFor(m => m.isPerPractice)</span>
                    </div>
                    <div class="col-md-1 _margin10">
                        @Html.CheckBoxFor(m => m.isPerPractice)
                    </div>
                </div>
            </div>
            <div class="row rowsS">
                <div class="form-group">
                    <div class="col-md-12 text-center" style="min-height:41px;">
                        @Html.LabelFor(model => model.message, Model.message, new { @class = "green_" })
                    </div>
                </div>
            </div>
            <div class="row rowsS">
                <div class="form-group">
                    <div class="col-md-12 text-center cl_bt" style="margin-bottom:5px;">
                        @Html.ActionLink("Close", null, null, null, new { @class = "btn btn-default", id = "close" })
                    </div>
                </div>
            </div>
    }
</div>


