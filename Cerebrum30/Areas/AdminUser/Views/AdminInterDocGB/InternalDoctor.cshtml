@model Cerebrum30.Areas.AdminUser.Models.IntrnalDoctorModel
@{
    Layout = "~/Views/Shared/_LayoutDemographics.cshtml";
}

<!DOCTYPE html>

<html>
<head>
    <link href="~/Content/admin-user.css" rel="stylesheet" />
    <script src="~/Scripts/jquery-3.1.0.min.js"></script>
    <script src="~/Scripts/jquery-ui-1.12.1.min.js"></script>
    <script src="~/Scripts/jquery.maskedinput.js"></script>
    <link href="~/Content/themes/base/jquery-ui.css" rel="stylesheet" />
    <link href="~/Content/themes/base/jquery.ui.theme.css" rel="stylesheet" />
    <script src="~/Scripts/cerebrum3-adminuser.js"></script>
    <meta name="viewport" content="width=device-width" />
    <title>InternalDoctor</title>
    <script>
        $(document).ready(function () {
            $('#cellNumber').mask("(*************");
            $('#faxNumber').mask("(*************");
        });

        function uploadButtonClick()
        {
            var url = $("#uploadUrl").val();
            window.open(url, target = '_blank', "width=530,height=130,left=20px,top=60px");
            return false;
        }



    </script>
</head>

<body>
    <div class="mainDocDiv">
        @using (Html.BeginForm("InternalDoctor", "AdminInterDocGB", FormMethod.Post, new { intrnalDoctorModel = Model }))
        {
            <div class="headerDiv">
                <span id="headerSpan">PRACTICE DOCTOR</span>
            </div>
            <div id="ex_d_dialog_edit_" title="" doctor_id="">
                <div class="form-group btn_height">
                    @*@Html.ActionLink("Upload Signature", "Upload", "AdminInterDocGB", new { target = "blank_",width="250px",height="65px" } )*@
                    <input type="button" value="Upload Signature" onclick="uploadButtonClick();" class="btn btn-upload" />
                    @Html.HiddenFor(model => model.uploadUrl)
                </div>
                <div class="form-group">
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.lastName)  
                        @Html.ValidationMessageFor(model => model.lastName, "", new { @class = "red_" })                      
                    </div>
                    <div class="col-md-5">
                        @Html.EditorFor(model => model.lastName, new { htmlAttributes = new { @class = "form-control", tabindex = 1, autocomplete = "off" } })                       
                    </div>
                    <div class="col-md-2">
                        @Html.LabelFor(model => model.initials)
                    </div>
                    <div class="col-md-2">
                        @Html.EditorFor(model => model.initials, new { htmlAttributes = new { @class = "form-control", tabindex = 2, autocomplete = "off" } })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.firstName)
                        @Html.ValidationMessageFor(model => model.firstName, "", new { @class = "red_" })
                        
                    </div>
                    <div class="col-md-5">
                        @Html.EditorFor(model => model.firstName, new { htmlAttributes = new { @class = "form-control", tabindex = 3, autocomplete = "off" } })
                    </div>
                    <div class="col-md-2">                       
                    </div>
                    <div class="col-md-2">
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3 rem_right_padd">
                        <input type="hidden" id="ex_doctorId" />
                        @Html.LabelFor(model => model.OHIPPhysicianId)
                        @Html.ValidationMessageFor(model => model.OHIPPhysicianId, "", new { @class = "red_" })
                        @*<span>OHIP</span>*@
                    </div>
                    <div class="col-md-3">
                        @Html.EditorFor(model => model.OHIPPhysicianId, new { htmlAttributes = new { @class = "form-control", tabindex = 4, autocomplete = "off" } })
                        @*<input type="text" class="form-control" id="ex_d_OHIP" name="ex_d_OHIP" tabindex="1000">*@
                    </div>
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.specialtyCode)
                    </div>
                    <div class="col-md-3">
                        <div class="col-md-4">
                            @Html.EnumDropDownListFor(model => model.specialtyCode, htmlAttributes: new { @class = "form-control salutation", tabindex = 5 })
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3 rem_right_padd">
                        <input type="hidden" id="ex_doctorId" />
                        @Html.LabelFor(model => model.CPSO)
                        @Html.ValidationMessageFor(model => model.CPSO,"",new { @class = "red_" })
                    </div>                 
                    <div class="col-md-2">
                        @Html.EditorFor(model => model.CPSO, new { htmlAttributes = new { @class = "form-control", tabindex = 6, autocomplete = "off" } })
                    </div>
                    <div class="col-md-3 rem_left_padd ">
                        <a href="#">CPSO Doctor Search</a>
                    </div>
                    <div class="col-md-4">
                        <button type="button" tabindex="1024" class="form-control clssButton">Check CPSO in HRM</button>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.cellNumber)
                    </div>
                    <div class="col-md-3">
                        @Html.EditorFor(model => model.cellNumber, new { htmlAttributes = new { @class = "form-control", tabindex = 7, autocomplete = "off" } })
                    </div>
                    <div class="col-md-1">
                    </div>
                    <div class="col-md-2">
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3">
                        <div class="col-md-9 rem_right_padd rem_left_padd">
                            @Html.LabelFor(model => model.email)
                        </div>
                    </div>
                    <div class="col-md-4">
                        @Html.EditorFor(model => model.email, new { htmlAttributes = new { @class = "form-control", tabindex = 8, autocomplete = "off" } })
                    </div>
                    <div class="col-md-2">
                        @Html.LabelFor(model => model.teamMembre)
                    </div>
                    <div class="col-md-3">
                        @Html.EnumDropDownListFor(model => model.teamMembre, htmlAttributes: new { @class = "form-control salutation", tabindex = 9 })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.HRMId)
                    </div>
                    <div class="col-md-3">
                        @Html.EditorFor(model => model.HRMId, new { htmlAttributes = new { @class = "form-control", tabindex = 10, autocomplete = "off" } })
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd">
                        @Html.LabelFor(model => model.active)
                    </div>
                    <div class="col-md-1 rem_right_padd">
                        @Html.EditorFor(x => x.active, new { htmlAttributes = new { tabindex = 11 } })
                    </div>
                    <div class="col-md-2 rem_right_padd rem_left_padd">
                        @Html.LabelFor(model => model.locked)
                    </div>
                    <div class="col-md-1 rem_right_padd">
                        @Html.EditorFor(x => x.locked, new { htmlAttributes = new { tabindex = 12 } })
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-group">
                        <div class="col-md-3">
                            @Html.LabelFor(model => model.letterHead)                            
                        </div>
                        <div class="col-md-9">
                            @Html.EditorFor(model => model.letterHead, new { htmlAttributes = new { @class = "form-control", tabindex = 13, autocomplete = "off" } })                           
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-group">
                        <div class="col-md-3">
                            @Html.LabelFor(model => model.diagnosticset)
                        </div>
                        <div class="col-md-3 rem_left_padd">
                            <div class="col-md-4">
                                @Html.EnumDropDownListFor(model => model.diagnosticset, htmlAttributes: new { @class = "form-control salutation", tabindex = 14})
                            </div>
                        </div>
                        <div class="col-md-2">
                            @Html.LabelFor(model => model.degrees)
                        </div>
                        <div class="col-md-4">
                            @Html.EditorFor(model => model.degrees, new { htmlAttributes = new { @class = "form-control", placeholder = "MD,", tabindex = 15, autocomplete = "off" } })
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3 rem_right_padd">
                        @Html.LabelFor(model => model.blueCrossNumber)
                    </div>
                    <div class="col-md-2">
                        @Html.EditorFor(model => model.blueCrossNumber, new { htmlAttributes = new { @class = "form-control", tabindex = 16, autocomplete = "off" } })
                    </div>
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.regionCode)
                    </div>
                    <div class="col-md-3">
                        @Html.EditorFor(model => model.regionCode, new { htmlAttributes = new { @class = "form-control", tabindex = 17, autocomplete = "off" } })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.m_mailBox)
                    </div>
                    <div class="col-md-3">
                        @Html.EditorFor(model => model.m_mailBox, new { htmlAttributes = new { @class = "form-control", tabindex = 18, autocomplete = "off" } })
                    </div>
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.letter_templ)
                    </div>
                    <div class="col-md-3">
                        @Html.EnumDropDownListFor(model => model.letter_templ, htmlAttributes: new { @class = "form-control salutation", tabindex = 21, autocomplete = "off" })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.m_password)
                    </div>
                    <div class="col-md-3">
                        @Html.EditorFor(model => model.m_password, new { htmlAttributes = new { @class = "form-control", tabindex = 19, autocomplete = "off" } })
                    </div>
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.report_templ)
                    </div>
                    <div class="col-md-3">
                        @Html.EnumDropDownListFor(model => model.report_templ, htmlAttributes: new { @class = "form-control salutation", tabindex = 22 })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.m_id)
                    </div>
                    <div class="col-md-3">
                        @Html.EditorFor(model => model.m_id, new { htmlAttributes = new { @class = "form-control", tabindex = 20, autocomplete = "off" } })
                    </div>
                    <div class="col-md-3">
                        @Html.LabelFor(model => model.hos_billing)
                    </div>
                    <div class="col-md-3">
                        @Html.EnumDropDownListFor(model => model.hos_billing, htmlAttributes: new { @class = "form-control salutation", tabindex = 23 })
                    </div>
                </div>
                <div class="form-group mainBackgroungColor">
                    <div class="col-md-12 ">
                        @Html.LabelFor(model => model.message, Model.message, new { @class = "green_" })
                    </div>
                </div>
                <div class="panel-group" id="accordion" style="margin-bottom:0px !important;">
                    <div class="panel panel-default">
                        <div class="panel-heading mainBackgroungColor">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" href="#collapseOne">Address</a>
                            </h4>
                        </div>
                        <div id="collapseOne" class="panel-collapse collapse">
                            <div class="panel-body mainBackgroungColor">
                                <div class="form-group">
                                    <div class="col-md-3">
                                        @Html.LabelFor(model => model.address)
                                    </div>
                                    <div class="col-md-7">
                                        @Html.EditorFor(model => model.address, new { htmlAttributes = new { @class = "form-control", tabindex = 23 } })
                                    </div>
                                    <div class="col-md-2 rem_right_padd rem_left_padd">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-md-3">
                                        @Html.LabelFor(model => model.city)
                                    </div>
                                    <div class="col-md-3">
                                        @Html.EditorFor(model => model.city, new { htmlAttributes = new { @class = "form-control", tabindex = 24 } })
                                    </div>
                                    <div class="col-md-6 rem_left_padd rem_right_padd">
                                        <div class="form-group rem_left_padd rem_right_padd">
                                            <div class="col-md-2 rem_left_padd rem_right_padd">
                                                @Html.LabelFor(model => model.province)
                                            </div>
                                            <div class="col-md-2 rem_left_padd rem_right_padd select_width">
                                                @Html.EnumDropDownListFor(model => model.province, htmlAttributes: new { @class = "form-control salutation", tabindex = 25 })
                                            </div>
                                            <div class="col-md-3 rem_left_padd rem_right_padd margin_l_12">
                                                @Html.LabelFor(model => model.country)
                                            </div>
                                            <div class="col-md-3 rem_left_padd rem_right_padd ">
                                                @Html.EnumDropDownListFor(model => model.country, htmlAttributes: new { @class = "form-control width_100", tabindex = 26 })
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-md-3">
                                        @Html.LabelFor(model => model.postalcode)
                                    </div>
                                    <div class="col-md-3">
                                        @Html.EditorFor(model => model.postalcode, new { htmlAttributes = new { @class = "form-control", tabindex = 27 } })
                                    </div>
                                    <div class="col-md-1">
                                        @Html.LabelFor(model => model.faxNumber)
                                    </div>
                                    <div class="col-md-4">
                                        @Html.EditorFor(model => model.faxNumber, new { htmlAttributes = new { @class = "form-control", tabindex = 28 } })
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-md-3">
                                        @Html.LabelFor(model => model.comment)
                                    </div>
                                    <div class="col-md-9">
                                        @Html.TextAreaFor(x => x.comment, 20, 15, new { @class = "form-control text-box multy-line txtHeight", tabindex = 29 })
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group headerDiv btn_height">
                    @if (Model.isPracticeDoctorRegistered)
                    {
                        <input type="submit" value="Save" class="btn btn-save" disabled />
                    }
                    else
                    {
                        <input type="submit" value="Save" class="btn btn-save" />
                    }
                </div>
            </div>
            @Html.HiddenFor(model => model.practiceId)
            @Html.HiddenFor(model => model.MasterId)
            @Html.HiddenFor(model => model.userId)
            @Html.HiddenFor(model => model.firstName)
        }
    </div>
</body>
</html>
