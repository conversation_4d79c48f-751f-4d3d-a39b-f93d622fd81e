﻿@model Cerebrum.ViewModels.Cohort.CohortResponse

<style type="text/css">
    .cohort-div {
        text-overflow: ellipsis;
        border: 1px solid white;
        margin: 5px 5px 5px 5px;
    }

    .txtHeight {
        height: 25px !important;
    }
</style>

<script type="text/javascript" src="~/Areas/AdminUser/Scripts/cohort.js"></script>

<div style="width:500px; font-size: 32px; color: white; background-color: rgb(189, 195, 200); margin-top: 15px; padding-top: 2px; padding-left: 8px;">Cohort</div>
<div style="width:500px; background-color: rgb(239, 245, 251);">
    <div style="background-color: rgb(201, 207, 211); height: 40px;" class="text-center">
        <span style="display: inline-block; margin-top: 10px; font-weight: 700;">Add New Cohort</span>
    </div>
    <div class="cohort-div">
        <div class="form-group row" style="margin-top: 16px;">
            <div class="col-md-4 text-right">
                <span>Name:</span>
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control txtHeight text-box single-line" id="cohortDescriptionAdd">
            </div>
        </div>
        <div class="form-group row">
            <div class="col-md-4 text-right">
                <span>Class:</span>
            </div>
            <div class="col-md-6 scrollPadding_0">
                @if (Model.CohortClasses.Count() == 1)
                {
                    @Html.DropDownList("cohortClassIdAddNew", new SelectList(Model.CohortClasses, "value", "text"), htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortDoctorAdd();" })
                }
                else
                {
                    @Html.DropDownList("cohortClassIdAddNew", new SelectList(Model.CohortClasses, "value", "text"), "Select Class", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortDoctorAdd();" })
                }
            </div>
        </div>
        <div class="form-group row">
            <div class="col-md-4 text-right">
                <span>Doctor:</span>
            </div>
            <div class="col-md-6 scrollPadding_0">
                @if (Model.CohortClasses.Count() == 1)
                {
                    @Html.DropDownList("practiceDoctorIdAddNew", new SelectList(Model.PracticeDoctors, "value", "text"), htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;" })
                }
                else
                {
                    @Html.DropDownList("practiceDoctorIdAddNew", new SelectList(Model.PracticeDoctors, "value", "text"), "All Doctors", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;" })
                }
            </div>
        </div>
        <div class="form-group row">
            <div class="col-md-4"></div>
            <div class="col-md-6">
                <input style="width:175px;" type="button" value="Add Cohort" onclick="addCohort()" />
            </div>
        </div>
    </div>
    <br />
    <div style="background-color: rgb(201, 207, 211); height: 40px;" class="text-center">
        <span style="display: inline-block; margin-top: 10px; font-weight: 700;">Update Cohort</span>
    </div>
    <div class="cohort-div">
            <div class="form-group row" style="margin-top: 16px;">
                <div class="col-md-2">
                    <span style="padding-left: 8px;">Old Data</span>
                </div>
                <div class="col-md-2 text-right">
                    <span>Class:</span>
                </div>
                <div class="col-md-6 scrollPadding_0">
                    @if (Model.CohortClasses.Count() == 1)
                    {
                        @Html.DropDownList("cohortClassIdUpdateOld", new SelectList(Model.CohortClasses, "value", "text"), htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortDoctorUpdate(0);" })
                    }
                    else
                    {
                        @Html.DropDownList("cohortClassIdUpdateOld", new SelectList(Model.CohortClasses, "value", "text"), "Select Class", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortDoctorUpdate(0);" })
                    }
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-4 text-right">
                    <span>Doctor:</span>
                </div>
                <div class="col-md-6 scrollPadding_0">
                    @if (Model.CohortClasses.Count() == 1)
                    {
                        @Html.DropDownList("practiceDoctorIdUpdateOld", new SelectList(Model.PracticeDoctors, "value", "text"), htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortUpdate(0);" })
                    }
                    else
                    {
                        @Html.DropDownList("practiceDoctorIdUpdateOld", new SelectList(Model.PracticeDoctors, "value", "text"), "All Doctors", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortUpdate(0);" })
                    }
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-4 text-right">
                    <span>Name:</span>
                </div>
                <div class="col-md-6">
                    @if (Model.CohortClasses.Count() == 1)
                    {
                        @Html.DropDownList("cohortDescriptionUpdateOld", new SelectList(Model.PracticeDoctors, "value", "text"), htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortDescriptionUpdate();" })
                    }
                    else
                    {
                        @Html.DropDownList("cohortDescriptionUpdateOld", new SelectList(Model.PracticeDoctors, "value", "text"), "Select Cohort", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortDescriptionUpdate();" })
                    }
                </div>
            </div>
            <hr />
            <div class="form-group row" style="margin-top: 16px;">
                <div class="col-md-2">
                    <span style="padding-left: 8px;">New Data</span>
                </div>
                <div class="col-md-2 text-right">
                    <span>Class:</span>
                </div>
                <div class="col-md-6 scrollPadding_0">
                    @if (Model.CohortClasses.Count() == 1)
                    {
                        @Html.DropDownList("cohortClassIdUpdateNew", new SelectList(Model.CohortClasses, "value", "text"), htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortDoctorUpdate(1);" })
                    }
                    else
                    {
                        @Html.DropDownList("cohortClassIdUpdateNew", new SelectList(Model.CohortClasses, "value", "text"), "Select Class", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortDoctorUpdate(1);" })
                    }
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-4 text-right">
                    <span>Doctor:</span>
                </div>
                <div class="col-md-6 scrollPadding_0">
                    @if (Model.CohortClasses.Count() == 1)
                    {
                        @Html.DropDownList("practiceDoctorIdUpdateNew", new SelectList(Model.PracticeDoctors, "value", "text"), htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;" })
                    }
                    else
                    {
                        @Html.DropDownList("practiceDoctorIdUpdateNew", new SelectList(Model.PracticeDoctors, "value", "text"), "All Doctors", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;" })
                    }
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-4 text-right">
                    <span>Name:</span>
                </div>
                <div class="col-md-6">
                    <input type="text" class="form-control txtHeight text-box single-line" id="cohortDescriptionUpdateNew">
                </div>
            </div>
            <div class="form-group row">
                <div class="col-md-4"></div>
                <div class="col-md-6">
                    <input style="width:175px;" type="button" value="Update Cohort" onclick="updateCohort()" />
                </div>
            </div>
    </div>
    <br />
</div>
