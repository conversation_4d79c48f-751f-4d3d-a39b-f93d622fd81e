﻿@model Cerebrum.ViewModels.Cohort.CohortClassResponse

<style type="text/css">
    .cohort-div {
        text-overflow: ellipsis;
        border: 1px solid white;
        margin: 5px 5px 5px 5px;
    }

    .txtHeight {
        height: 25px !important;
    }
</style>

<script type="text/javascript" src="~/Areas/AdminUser/Scripts/cohortClass.js"></script>

<div style="width:500px; font-size: 32px; color: white; background-color: rgb(189, 195, 200); margin-top: 15px; padding-top: 2px; padding-left: 8px;">Cohort Class</div>
<div style="width:500px; background-color: rgb(239, 245, 251);">
    <div style="background-color: rgb(201, 207, 211); height: 40px;" class="text-center">
        <span style="display: inline-block; margin-top: 10px; font-weight: 700;">Add New Cohort Class</span>
    </div>
    <div class="cohort-div">
        <div class="form-group row" style="margin-top: 16px;">
            <div class="col-md-4 text-right">
                <span>Name:</span>
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control txtHeight text-box single-line" id="descriptionAddNew">
            </div>
        </div>
        <div class="form-group row">
            <div class="col-md-4 text-right">
                <span>Doctor:</span>
            </div>
            <div class="col-md-6 scrollPadding_0">
               @Html.DropDownList("practiceDoctorIdAddNew", new SelectList(Model.PracticeDoctors, "value", "text"), "All Doctors", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;" })
            </div>
        </div>
        <div class="form-group row">
            <div class="col-md-4"></div>
            <div class="col-md-6">
                <input style="width:175px;" type="button" value="Add Class" onclick="addClass()" />
            </div>
        </div>
    </div>
    <br />
    <div style="background-color: rgb(201, 207, 211); height: 40px;" class="text-center">
        <span style="display: inline-block; margin-top: 10px; font-weight: 700;">Update Cohort Class</span>
    </div>
    <div class="cohort-div">
        <div class="form-group row" style="margin-top: 16px;">
            <div class="col-md-2">
                <span style="padding-left: 8px;">Old Data</span>
            </div>
            <div class="col-md-2 text-right">
                <span>Name:</span>
            </div>
            <div class="col-md-6 scrollPadding_0">
                @if (Model.CohortClasses.Count() == 1)
                {
                    @Html.DropDownList("cohortClassIdOld", new SelectList(Model.CohortClasses, "value", "text"), htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortClass();" })
                }
                else
                {
                    @Html.DropDownList("cohortClassIdOld", new SelectList(Model.CohortClasses, "value", "text"), "Select Class", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @onchange = "getCohortClass();" })
                }
            </div>
        </div>
        <div class="form-group row">
            <div class="col-md-4 text-right">
                <span>Doctor:</span>
            </div>
            <div class="col-md-6 scrollPadding_0">
                @Html.DropDownList("practiceDoctorIdUpdateOld", new SelectList(Model.PracticeDoctors, "value", "text"), "All Doctors", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;", @disabled = "disabled" })
            </div>
        </div>
        <hr />
        <div class="form-group row">
            <div class="col-md-2">
                <span style="padding-left: 8px;">New Data</span>
            </div>
            <div class="col-md-2 text-right">
                <span>Name:</span>
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control txtHeight text-box single-line" id="descriptionUpdateNew">
            </div>
        </div>
        <div class="form-group row">
            <div class="col-md-4 text-right">
                <span>Doctor:</span>
            </div>
            <div class="col-md-6 scrollPadding_0">
                @Html.DropDownList("practiceDoctorIdUpdateNew", new SelectList(Model.PracticeDoctors, "value", "text"), "All Doctors", htmlAttributes: new { @class = "form-control", @style = "height: 25px; padding-top: 0; padding-bottom: 0;" })
            </div>
        </div>
        <div class="form-group row">
            <div class="col-md-4"></div>
            <div class="col-md-6">
                <input style="width:175px;" type="button" value="Update Class" onclick="updateClass()" />
            </div>
        </div>
    </div>
    <br />
</div>
