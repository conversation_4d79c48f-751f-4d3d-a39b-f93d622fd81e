@model IEnumerable<Cerebrum30.Areas.OLIS.Models.ViewModels.OLISTestReportCategoryVM>

@{
    ViewBag.Title = "OLISTestReportCategory";
}

<h2>OLIS Test Report Category
    @if(Model!=null && Model.Count() > 0)
    {
        var cnt="["+ Model.Count()+"]";
        @cnt;
    }
    </h2>
<br />

<p>
    <div id="div-category">
        @Html.Action("Create", "OLISTestReportCategory")
    </div>
</p>
<br />
<table class="table">
    <tr>
        <th>
            @Html.ActionLink("categoryName", "Index", new { sortOrder = ViewBag.CategoryNameSortParm })
        </th>
        <th>
            @Html.DisplayNameFor(model => model.createdOn)
        </th>
        <th>
            @Html.ActionLink("Update Date", "Index", new { sortOrder = ViewBag.DateSortParm })
            
        </th>
        <th>
            @Html.DisplayNameFor(model => model.isActive)
        </th>
        <th></th>
    </tr>
@if (Model != null && Model.Count() > 0)
{
    foreach (var item in Model)
    {
    <tr>
        <td>
            @item.categoryName
        </td>
        <td>
            @item.createdOn
        </td>
        <td>
            @item.updatedOn
        </td>
        <td>
            @item.isActive
        </td>
        <td>
            <button onclick="LoadForm('/olis/OLISTestReportCategory/Edit/@item.Id','#div-category')">Edit </button>
            <button onclick="LoadDialogForm('/olis/OLISTestReportCategory/Delete/@item.Id')">Delete </button>
        </td>
    </tr>
    }
}
</table>
