@model IEnumerable<Cerebrum30.Areas.OLIS.Models.ViewModels.OLISTestRequestNomenclatureVM>

@{
    ViewBag.Title = "OLIS Test Request Nomenclatures";
}

<h2>OLIS Test Request Nomenclatures
    @if (Model != null && Model.Count() > 0)
    {
        var cnt = "[" + Model.Count() + "]";
        @cnt;
    }
    </h2>

<p>
    @Html.ActionLink("Create New", "Create")
</p>
<table class="table">
    <tr>
        
        <th>
            @Html.DisplayNameFor(model => model.OLISTestRequestCode)
        </th>
        <th>
            @Html.ActionLink("testRequestName", "Index", new { sortOrder = ViewBag.testNameSortParm })
            
        </th>
        <th>
            @Html.DisplayNameFor(model => model.requestAlternateName1)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.requestAlternateName2)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.requestAlternateName3)
        </th>
        
        <th>
            @Html.DisplayNameFor(model => model.OLISTestRequestCategory.categoryName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.OLISTestRequestSubCategory.categoryName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.comment)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.sortKey)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.OLISTestReportCategory.categoryName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.createdOn)
        </th>
        <th>
            @Html.ActionLink("Update Date", "Index", new { sortOrder = ViewBag.DateSortParm })
        </th>
        <th>
            @Html.DisplayNameFor(model => model.isActive)
        </th>
        <th></th>
    </tr>

@foreach (var item in Model) {
    <tr>
        
        <td>
            @item.OLISTestRequestCode
        </td>
        <td>
            @item.testRequestName
        </td>
        <td>
            @item.requestAlternateName1
        </td>
        <td>
            @item.requestAlternateName2
        </td>
        <td>
            @item.requestAlternateName3
        </td>
        
        <td>
            @item.OLISTestRequestCategory.categoryName
        </td>
        <td>
            @item.OLISTestRequestSubCategory.categoryName
        </td>
        <td>
            @item.comment
        </td>
        <td>
            @item.sortKey
        </td>
        <td>
            @item.OLISTestReportCategory.categoryName
        </td>
        <td>
            @item.createdOn
        </td>
        <td>
            @item.updatedOn
        </td>
        <td>
            @item.isActive
        </td>
        <td>
            @Html.ActionLink("Edit", "Edit", new { id=item.Id })             
        </td>
    </tr>
}

</table>
