@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISTestResultCategory>

@{
    ViewBag.Title = "OLISTestResultCategory";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

<h2 id="cat-top">OLIS Test Result Category
    @if (Model!=null && Model.Count() > 0)
    {
        var cnt="["+ Model.Count()+"]";
        @cnt;
    }
    </h2>
<br />

<p>
    <div id="add-update-olis-result-cat-div">
        @(await Html.PartialAsync("Create", new { area = "OLIS" }))
    </div>
</p>
<br />
@{ int count = 0;}
<table class="table">
    <tr>
        <th>#</th>
        <th>
            @Html.ActionLink("categoryName", "Index", new { sortOrder = ViewBag.CategoryNameSortParm })
        </th>
        <th>
            @Html.DisplayNameFor(model => model.createdOn)
        </th>
        <th>
            @Html.ActionLink("Update Date", "Index", new { sortOrder = ViewBag.DateSortParm })
            
        </th>
        <th>
            @Html.DisplayNameFor(model => model.isActive)
        </th>
        <th></th>
    </tr>
@if (Model != null && Model.Count() > 0)
{
    foreach (var item in Model)
    {
    <tr>
        <td>@(++count)</td>
        <td>
            @item.categoryName
        </td>
        <td>
            @item.createdOn
        </td>
        <td>
            @item.updatedOn
        </td>
        <td>
            <label class="checkbox-inline">
                <input id=@item.Id type="checkbox" class="olis-result-cat-active" value="@item.isActive" @(item.isActive  ? "checked" : "") data-cat="@item.Id" />
            </label>
        </td>
        <td>
            <button class="btn btn-olis-result-cat-edit" data-id="@item.Id" onclick="window.location.hash='cat-top'";>Edit </button>
            <button class="btn btn-olis-result-cat-delete" data-category-name="@item.categoryName" data-category-id="@item.Id" data-toggle="modal" data-target="#olis-res-cat-modal" data-url="@Url.Action("Delete", "OLISTestResultCategory", new { area = "OLIS" })">Delete </button>
        </td>
    </tr>
    }
}
</table>
<br />
<br />

<div class="modal fade" id="olis-res-cat-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">Confirm Delete</h4>
            </div>
            <div class="modal-body">
                <p class="alert alert-warning"> You are about to delete 'OLIS Test Result category': <span class="del-category-name"></span></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <a class="btn btn-danger btn-ok-delete-category">Delete</a>
            </div>
        </div>
    </div>
</div>
<script src="~/Areas/OLIS/Scripts/c3-olis.js"></script>