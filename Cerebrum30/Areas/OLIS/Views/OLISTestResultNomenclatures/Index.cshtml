@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISTestResultNomenclature>
@{
    ViewBag.Title = "Index";
}

<h2>OLIS Test Result Nomenclature
    @if (Model != null && Model.Count() > 0)
    {
        var cnt = "[" + Model.Count() + "]";
        @cnt;
    }
    </h2>

<p>
    @Html.ActionLink("Create New", "Create")
</p>
<table class="table">
    <tr>
        
        <th>
            @Html.DisplayNameFor(model => model.LOINCCode)
        </th>
        <th>
            @Html.ActionLink("LOINCComponent", "Index", new { sortOrder = ViewBag.resultNameSortParm })
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LOINCProperty)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Units)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LOINCTime)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LOINCSystem)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LOINCMethod)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LOINCShortName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LOINCFullySpecifiedName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.resultAlternateName1)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.resultAlternateName2)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.resultAlternateName3)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.OLISTestResultCategory)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LOINCAnswerList)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.effectiveDate)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.expiredDate)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.sortKey)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.createdOn)
        </th>
        <th>
            @Html.ActionLink("Update Date", "Index", new { sortOrder = ViewBag.DateSortParm })
        </th>
        <th>
            @Html.DisplayNameFor(model => model.isActive)
        </th>
        <th></th>
    </tr>

@foreach (var item in Model) {
    <tr>
       
        <td>
            @item.LOINCCode
        </td>
        <td>
            @item.LOINCComponent
        </td>
        <td>
            @item.LOINCProperty
        </td>
        <td>
            @item.Units
        </td>
        <td>
            @item.LOINCTime
        </td>
        <td>
            @item.LOINCSystem
        </td>
        <td>
            @item.LOINCMethod
        </td>
        <td>
            @item.LOINCShortName
        </td>
        <td>
            @item.LOINCFullySpecifiedName
        </td>
        <td>
            @item.resultAlternateName1
        </td>
        <td>
            @item.resultAlternateName2
        </td>
        <td>
            @item.resultAlternateName3
        </td>
        <td>
            @item.OLISTestResultCategory
        </td>
        <td>
            @item.LOINCAnswerList
        </td>
        <td>
            @item.effectiveDate
        </td>
        <td>
            @item.expiredDate
        </td>
        <td>
            @item.sortKey
        </td>
        <td>
            @item.createdOn
        </td>
        <td>
            @item.updatedOn
        </td>
        <td>
            @item.isActive
        </td>
        <td>
           @Html.ActionLink("Edit", "Edit", new { id=item.Id })           
        </td>
    </tr>
}

</table>
