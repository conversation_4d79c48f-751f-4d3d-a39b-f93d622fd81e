@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISDoctorReportInbox>

@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}

<h2>OLIS Inbox</h2>

@if (Model != null && Model.Count() > 0)
{
<table class="table">
    <tr>
      
        <th>
            @Html.DisplayNameFor(model => model.HealthCardNumber)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.PatientName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.AccessionNumber)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Status)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.DoctorName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.CreatedDateTime)
        </th>
        <th></th>
    </tr>

@foreach (var item in Model)
{
    <tr>
       
        <td>
            @item.HealthCardNumber
        </td>
        <td>
            @item.PatientName
        </td>
        <td>
            @item.AccessionNumber
        </td>
        <td>
            @item.Status
        </td>
        <td>
            @item.DoctorName
        </td>
        
        <td>
            @item.CreatedDateTime
        </td>
        <td>
            @Html.ActionLink("Preview Report", "ReportPreview", new { Area="Labs",Controller="OLIS", report=item.OLISReceivedReportId }) |
            @Html.ActionLink("Save Report", "Details", new { /* id=item.PrimaryKey */ }) 
            @if(item.PatientRecordId==null) { 
                @Html.ActionLink("Match Patient", "Match patient", new { /* id=item.PrimaryKey */ })
            }
        </td>
    </tr>
}

</table>
}