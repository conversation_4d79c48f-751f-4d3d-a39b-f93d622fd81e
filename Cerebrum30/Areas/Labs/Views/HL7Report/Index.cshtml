@model IEnumerable<Cerebrum30.Areas.Labs.Models.ViewModels.VMHL7File>

@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Index</h2>

<p>
    @Html.ActionLink("Create New", "Create")
</p>
<table class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.patientName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.doctorName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.filePath)
        </th>
        <th></th>
    </tr>

@foreach (var item in Model) {
    <tr>
        <td>
            @item.patientName
        </td>
        <td>
            @item.doctorName
        </td>
        <td>
            @item.filePath
        </td>
        <td>
            @Html.ActionLink("Edit", "Edit", new { /* id=item.PrimaryKey */ }) |
            @Html.ActionLink("Details", "Details", new { /* id=item.PrimaryKey */ }) |
            @Html.ActionLink("Delete", "Delete", new { /* id=item.PrimaryKey */ })
        </td>
    </tr>
}

</table>
