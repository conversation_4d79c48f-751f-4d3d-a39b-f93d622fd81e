﻿@model Cerebrum.ViewModels.OLIS.VMOLISSearch

@{
    ViewBag.Title = "OLISSearch";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>OLISSearch</h2>

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

    <div class="form-horizontal row">
        <h4>Search Laboratory Result in OLIS</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        <div class="form-group row">
            @Html.LabelFor(model => model.fromDate, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.fromDate, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.fromDate, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.toDate, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.toDate, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.toDate, "", new { @class = "text-danger" })
            </div>
        </div>
        
        <div class="form-group">
            @Html.LabelFor(model => model.OBRfromDate, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.OBRfromDate, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.OBRfromDate, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.OBRtoDate, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.OBRtoDate, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.OBRtoDate, "", new { @class = "text-danger" })
            </div>
        </div>
        <hr />

        <div class="row">
            <div class="form-group">
                @Html.LabelFor(model => model.quantityLimit, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.quantityLimit, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.quantityLimit, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                @Html.LabelFor(model => model.consentToViewBlockedInfo, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.TextBoxFor(model => model.consentToViewBlockedInfo, htmlAttributes: new { @class = "form-control text-box single-inline" })
                    @Html.ValidationMessageFor(model => model.consentToViewBlockedInfo, "", new { @class = "text-danger" })
                </div>
            </div>
            <br />
            <div class="form-group">
                @Html.LabelFor(model => model.consentAutorizedBy, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextBoxFor(model => model.consentAutorizedBy, htmlAttributes: new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.consentAutorizedBy, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">

                <div class="col-md-1">
                    <div class="checkbox">
                        @Html.EditorFor(model => model.enablePatientConsentBlockAllIndicator)
                        @Html.ValidationMessageFor(model => model.enablePatientConsentBlockAllIndicator, "", new { @class = "text-danger" })
                    </div>
                </div>
                @Html.LabelFor(model => model.enablePatientConsentBlockAllIndicator, htmlAttributes: new { @class = "control-label col-md-15" })
            </div>
            <br />
            <div class="form-group">
                @Html.LabelFor(model => model.specimenCollector, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.specimenCollector, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.specimenCollector, "", new { @class = "text-danger" })
                </div>
            </div>
            <br />
            <div class="form-group">
                @Html.LabelFor(model => model.performingLaboratory, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.performingLaboratory, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.performingLaboratory, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.excludePerformingLaboratory, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.excludePerformingLaboratory, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.excludePerformingLaboratory, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.reportingLaboratory, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.reportingLaboratory, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.reportingLaboratory, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.excludeReportingLaboratory, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.excludeReportingLaboratory, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.excludeReportingLaboratory, "", new { @class = "text-danger" })
                </div>
            </div>
            <hr />
            <div class="form-group">
                @Html.LabelFor(model => model.patient, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.patient, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.patient, "", new { @class = "text-danger" })
                </div>
            </div>
            <hr />
            <div class="form-group">
                @Html.LabelFor(model => model.orderingPractitioner, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.orderingPractitioner, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.orderingPractitioner, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.copiedToPractitioner, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.copiedToPractitioner, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.copiedToPractitioner, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.attendingPratitioner, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.attendingPratitioner, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.attendingPratitioner, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.admittingPractitioner, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.admittingPractitioner, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.admittingPractitioner, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.testRequestPlacer, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.testRequestPlacer, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.testRequestPlacer, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.testRequestStatus, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.testRequestStatus, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.testRequestStatus, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.testResultCode, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.testResultCode, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.testResultCode, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.testRequestCode, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.testRequestCode, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.testRequestCode, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-offset-2 col-md-10">
                    <input type="submit" value="Save" class="btn btn-default" />
                </div>
            </div>
        </div>
    </div>
}


