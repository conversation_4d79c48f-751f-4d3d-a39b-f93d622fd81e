﻿@model Cerebrum.ViewModels.HL7.HL7ResultVM
@using Cerebrum30.Utility;
@if (Model != null)
{
    var flg = (Model.abnormalFlag != null && Model.abnormalFlag.Trim() != "" ? (Model.abnormalFlag.Trim().Equals("N") ? "" : Model.abnormalFlag) : "");
        
            <td id="<EMAIL>">
               @Html.Raw(Model.TestName)
            </td>
            <td id="<EMAIL>">
               @flg
            </td>
                var length = string.IsNullOrWhiteSpace(Model.testResult) ? 0 : Model.testResult.Length;
                var lengthClass = length > 20 ? "dont-break-out fixedWidthfont" : "";
            <td id="<EMAIL>" class="@lengthClass">
                <div id="<EMAIL>">
                    @{ 
                        var result = Model.testResult.FormatHL7Report();
                    }
                    @Html.Raw(result)
                </div>
            </td>
            <td id="<EMAIL>">
                @Html.DisplayFor(modelModel => Model.refRange)
            </td>
            <td id="<EMAIL>">
                @Html.DisplayFor(modelModel => Model.units)
            </td>
            <td id="<EMAIL>">
                 @Html.DisplayFor(modelModel => Model.resultStatus)
            </td>
            <td id="<EMAIL>">
                <button id="@Model.Id" class="btn-HL7-Result-Note btn btn-default btn-xs" data-value="@Model.Id"> Add Note</button>
            </td>
            <td>
                <a href="#" data-resultid="@Model.Id" data-URL="@Url.Action("HL7SensitiveResultAccessedLog", new { controller = "HL7Report", area = "Labs" })" class="btn-sensitive-accessed-info">  <span class="glyphicon glyphicon-eye-open"></span></a>
            </td>
        

                }

<script>
    $(function () {
        $(".btn-sensitive-accessed-info").on("click", function (e) {
            e.preventDefault();

            var URL = $(this).attr("data-URL");
            //alert($(this).data());
            var vals = $(this).data("resultid");
            var modalID = '#hl7CommentModal';
            console.log("sensitive infor Clicked: " + URL);
            $('#ajax-loader').show();
            $.ajax({
                url: URL,
                type: 'GET',
                cache: false,
                data: { id: vals }
            }).done(function (data) {
                $('#ajax-loader').hide();
                loadModal(modalID, "Sensitive Accessed Log", data, "modal-md")

            }).fail(function (jqXHR, textStatus, errorThrown) {
                $('#ajax-loader').hide();
                checkAjaxError(jqXHR, null);
            });

        });
    });
</script>