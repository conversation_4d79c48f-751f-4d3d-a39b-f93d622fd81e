@model IEnumerable<Cerebrum.ViewModels.HL7.HL7CodingVM>

@{
    ViewBag.Title = "HL7 Lab Code";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>HL7 Lab Code [@(Model != null ?Model.Count():0)]</h2>
@{ 
    int count = 0;
}
<table class="table">
    <tr>
        <th></th>
        <th>
            @Html.DisplayNameFor(model => model.labName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.labCode)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.LOINC)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.description)
        </th>

        <th>
            @Html.DisplayNameFor(model => model.updatedDate)
        </th>
        <th></th>
    </tr>

@foreach (var item in Model) {
    <tr>
        <td>@(++count)</td>
        <td>
            @item.labName
        </td>
        <td>
            @item.labCode
        </td>
        <td>
            @item.LOINC
        </td>
        <td>
            @item.description
        </td>
      
        <td>
            @item.updatedDate
        </td>
        <td>
            @Html.ActionLink("Edit", "Edit", new { id=item.Id }) |
            @Html.ActionLink("Details", "Details", new { id=item.Id }) |
            @Html.ActionLink("Delete", "Delete", new { id=item.Id })
        </td>
    </tr>
}

</table>
