﻿@model Cerebrum.ViewModels.OLIS.VMPractitioner
<div>
    <dl class="dl-horizontal">
        <dt class="olis-heading">Order By</dt>
        <dd class="dont-break-out">@Html.DisplayFor(model => model.FullName)</dd>

        <dt class="olis-heading">Licence #</dt>
        <dd class="dont-break-out">@Html.DisplayFor(model => model.IDNumber1)</dd>

        <dt class="olis-heading">&nbsp;</dt>
        <dd class="dont-break-out">
            @{
                string jurisdiction = string.Empty;
            }
            @if (!string.IsNullOrWhiteSpace(Model.jurisdiction))
            {
                jurisdiction = ", " + Model.jurisdiction;

            }
            @Html.Raw(Model.identifierTypeCode7 + jurisdiction)
        </dd>
    </dl>
</div>

