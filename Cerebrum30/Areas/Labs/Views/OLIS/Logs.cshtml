﻿@model Cerebrum.ViewModels.OLIS.VMOLISRequestLogKeys
@{
    ViewBag.Title = "OLIS Communication Logs";
    ViewBag.ModuleName = "OLIS Communication Logs";
    //Layout = "~/Views/Shared/_Layout.cshtml";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<script src="~/Scripts/olis-log.js"></script>
@using (Html.BeginForm("GetLogs", "OLIS", null, FormMethod.Get, true, new { @class = " olis-report-form", role = "form" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(t => t.UserId)
    @Html.HiddenFor(t => t.DoctorId)
    @Html.HiddenFor(t => t.PatientRecordId)


<div id="olis-log-container" style="padding-top: 15px">
    <div id="wrapper7567243">

        <div class="row">
            <div class="form-group form-group-sm col-sm-1">
                @*@Html.LabelFor(m => m.DateFrom, new { @class = "truncate" })*@
                <label class="control-label truncate" title="From">From</label>                    
                    @Html.EditorFor(m => m.DateFrom, new { htmlAttributes = new { @class = "form-control olisLogDate" } })
</div>

            <div class="form-group form-group-sm col-sm-1">
                @*@Html.LabelFor(m => m.DateTo, new { @class = "truncate" })*@
                <label class="control-label truncate" title="To">To</label>   
                @Html.EditorFor(m => m.DateTo, new { htmlAttributes = new { @class = "form-control olisLogDate" } })
            </div>

            <div class="form-group form-group-sm col-sm-1">
                @*@Html.LabelFor(m => m.responseStatusId, new { @class = "truncate" })*@
                <label class="control-label truncate" title="Response Status">Response Status</label>   
                @Html.DropDownListFor(model => model.responseStatusId, new SelectList(Model.ResponseStatusList, "Value", "Text", Model.responseStatusId), new { @class = "form-control" })
            </div>

            <div class="form-group form-group-sm col-sm-1">
                @*@Html.LabelFor(m => m.messageStatusId, new { @class = "truncate" })*@
                <label class="control-label truncate" title="Message Status">Message Status</label>   
                @Html.DropDownListFor(model => model.messageStatusId, new SelectList(Model.MessageStatusList, "Value", "Text", Model.messageStatusId), new { @class = "form-control" })
            </div>

            <div class="form-group form-group-sm col-sm-1">
                @*@Html.LabelFor(m => m.User, new { @class = "truncate" })*@       
                <label class="control-label truncate" title="User">User</label>           
                @Html.TextBoxFor(m => m.User, new { @class = "form-control" })               
            </div>

            <div class="form-group form-group-sm col-sm-1">
                @*@Html.LabelFor(m => m.Doctor, new { @class = "truncate" })*@  
                <label class="control-label truncate" title="Doctor">Doctor</label>               
                @Html.TextBoxFor(m => m.Doctor, new { @class = "form-control" })
            </div>

            <div class="form-group form-group-sm col-sm-1">
                @*@Html.LabelFor(m => m.EmrTransactionId, new { @class = "truncate" })*@              
                <label class="control-label truncate" title="EMR Transaction ID">EMR Transaction ID</label>  
                @Html.TextBoxFor(m => m.EmrTransactionId, new { @class = "form-control" })
            </div>

            <div class="form-group form-group-sm col-sm-1">
                @*@Html.LabelFor(m => m.OlisTransactionId, new { @class = "truncate" })*@
                <label class="control-label truncate" title="OLIS Transaction ID">OLIS Transaction ID</label>  
                @Html.TextBoxFor(m => m.OlisTransactionId, new { @class = "form-control" })
            </div>

            <div class="form-group form-group-sm col-sm-1">
                @*@Html.LabelFor(m => m.olisQueryTypeId, new { @class = "truncate" })*@
                <label class="control-label truncate" title="OLIS Query Type">OLIS Query Type</label>  
                @Html.DropDownListFor(model => model.olisQueryTypeId, new SelectList(Model.OlisQueryTypeList, "Value", "Text", Model.olisQueryTypeId), new { @class = "form-control" })
            </div>

            <div class="form-group form-group-sm col-sm-1">
                <label class="control-label hdr-lbl" title="">&nbsp;</label>
                <input type="submit" value="Get Report" class="btn btn-default btn-sm btn-olis-report btn-primary" style="margin-top: -3px;"/>
            </div>

            <div class="form-group form-group-sm col-sm-1">
            </div>

            <div class="form-group form-group-sm col-sm-1">
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="form-group form-group-sm col-sm-12">
        <br /><br />
        <div>  <span class="heading ">Results</span>  </div>
        <table id="table-OLIS-logs" class="table">
            <thead>
                <tr id="table-Olis-logs-header">
                    <th>ID</th>
                    <th>OLIS Request Date @*OLISRequestDateStr*@</th>
                    <th>User</th>
                    <th>Doctor</th>
                    <th>Response Status</th>
                    <th>Error</th>
                    <th>EMR Transaction ID</th>
                    <th>OLIS Transaction ID</th>
                    <th>EMR Query Type</th>
                    <th>Report Status</th>
                    <th>Removal Date @*RemovalDateStr*@</th>
                    <th>ORC4</th>
                    <th>OBR4</th>
                    <th>Doctor ID</th>
                    <th>User ID</th>
                </tr>
            </thead>
            <tbody id="olis-tb">
                <tr class="tr-row repeater">
                    <td colspan="15">&nbsp;</td> 
                </tr>
            </tbody>
        </table>
    </div>
</div>
}


