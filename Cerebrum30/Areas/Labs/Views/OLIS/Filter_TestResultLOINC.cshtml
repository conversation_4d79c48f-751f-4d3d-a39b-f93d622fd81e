﻿@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISResult>
@using Cerebrum30.Utility;
@{ 
    var resultGroups = (from r in Model
                   group r by r.LOINC into lg
                   select new { lg.Key, alluniq = lg.Select(s => s).ToList() }).ToList();
}
<div class="panel panel-default clearBoth">
    <div class="panel-heading report-header">Test Result Name</div>
    <div class="panel-body">
       @foreach (var rg in resultGroups)
       {
           var item = rg.alluniq.FirstOrDefault();
        <div class="col-md-10 data-filter-test-result" data-filter-test-result-LOINC="@item.LOINC" data-filter-target="data-filter-resultLOINC">
            @Html.CheckBox("LOINC", new { @checked = "checked", @class= "cl-test-result-loinc-filter", data_filter_test_result_LOINC = item.LOINC, data_filter_target = "data-filter-resultLOINC" }) @item.TestName
        </div>
       }
    </div>
    <div class="panel-footer"></div>
</div>

