﻿@model Cerebrum.ViewModels.OLIS.VMOLISConsentReinstatement
@using Cerebrum30.Utility
<div class="row">
    <div class="col-sm-2">
        <div>
            <div>
                <span>STOP</span>
                <hr />
                <span>PATIENT APPROVAL REQUIRED</span>
            </div>
        </div>
    </div>
    <div class="col-sm-6">
        <p class="">Consent to access laboratory information has been withdrawn by patient</p>
        <p class="">Select <strong>Cancel</strong> if you do not have consent to view this patient's blocked laboratory results.</p>
        <p class="">Patients whose consent is reinstated at the point of care will receive notice of access to their laboratory results. </p>
        <p class=""> <strong>Access will be enabled for 4 hrs for anyone under the same authority</strong> </p>
        <p class="">If you have the patient's or substitute decision maker's (SDM) consent to view this patient's <br /> blocked laboratory results, enter or select the appropriate choices below and click <strong></strong></p>
    </div>
</div>
<hr />
<div class="row">

    @using (Html.BeginForm("Index", "OLIS",new {Area ="Labs"},FormMethod.Post,true,new { @id="consent_form" }))
    {
        @Html.AntiForgeryToken()

        <div class="form-horizontal">
            <h4>Patient Lab Result Temporary Consent Reinstatement</h4>
            <hr />
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            <div class="form-group">
                @Html.LabelFor(model => model.PatientConsent, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div class="checkbox">
                        @Html.EditorFor(model => model.PatientConsent)
                        @Html.ValidationMessageFor(model => model.PatientConsent, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.HealthNumber, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.DisplayFor(model=>model.HealthNumber)
                    @Html.ValidationMessageFor(model => model.HealthNumber, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.SDMConsent, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div class="checkbox">
                        @Html.EditorFor(model => model.SDMConsent)
                        @Html.ValidationMessageFor(model => model.SDMConsent, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.SDMGivenName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.SDMGivenName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.SDMGivenName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.SDMFamilyName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.SDMFamilyName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.SDMFamilyName, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.ReplationshipToPatient, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @{ 
                        var em = Model.ReplationshipToPatient.ToSelectList();
                    }
                    @Html.DropDownList("ReplationshipToPatient",em, "Select...", new { @id = "ddlMessageType", htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.ReplationshipToPatient, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.LabelFor(model => model.Comment, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Comment, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Comment, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group">
                @Html.HiddenFor(model => model.PatientRecordId)
                @Html.HiddenFor(model => model.HealthNumber)
                @Html.HiddenFor(model => model.OrderId)
                @Html.HiddenFor(model => model.RequestingHIC)
                @Html.HiddenFor(model => model.PracticeId)
            </div>
           
        </div>
        <div class="form-group modal-footer">
            <div class="col-md-10">
                <button type="submit" id="submitForm" class="btn btn-success" value="Submit">Submit</button>
                <button type="submit" class="btn btn-warning" data-dismiss="modal" value="Cancel">Cancel</button>
            </div>
        </div>
}

</div>
<script>
    $(document).ready(function () {
        $("#consent_form").on("submit", function (e) {
        var postData = $(this).serializeArray();
        var formURL = $(this).attr("action");
        var orderId = $(this).find('#OrderId');
        var modalId = '#olisReportModal';
        var consentModalId = '#model-consent';
        var modalheader = '<div class="modal-header">' +
        '<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>' +
        '<h4 class="modal-title">Blocked Results</h4></div>';

        
        var modalfooter = '<div class="modal-footer"><button class="btn btn-default btn-sm" data-dismiss="modal">Close</button></div>';
        $.ajax({
            url: formURL,
            type: "POST",
            data: postData,
            success: function(data, textStatus, jqXHR) {
                //$('#model-consent .modal-header .modal-title').html("Result");
                //$('#model-consent .modal-body').html(data);
                //$("#submitForm").remove();
                $(consentModalId).modal('hide');

                var testData = '<div>testing data</div>';
                var modalbody = '<div class="modal-body">' + data + '</div>';
                var modalData = modalheader + modalbody + modalfooter;
                loadCBModal(modalId,modalData);
            },
            error: function(jqXHR, status, error) {
                console.log(status + ": " + error);
            }
        });
        e.preventDefault();
    });

        $("#submitForm").on('click', function (e) {
            e.preventDefault();
            $("#consent_form").submit();
        });
    });
    

</script>