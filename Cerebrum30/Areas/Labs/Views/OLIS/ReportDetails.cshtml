﻿@model Cerebrum.ViewModels.OLIS.VMOlisReportDetails

<div>
    @{
        string reportstatus = string.IsNullOrWhiteSpace(Model.ReportStatus) ? string.Empty : "<span class='hdr-lbl-nl red'>" + (Model.ReportStatus) + "</span>";
    }
    <div class="hdr-lbl-hg">Report Details @Html.Raw(reportstatus) </div>
    <hr />
    <dl class="dl-horizontal">
        <dt class="olis-heading">@Html.DisplayNameFor(model => model.OrderDateTime)</dt>
        <dd class="dont-break-out">@Html.DisplayFor(model => model.OrderDateTime)</dd>
        
        <dt class="olis-heading">Order ID</dt>
        <dd class="dont-break-out">@Html.Raw(Model.ORC4Order.FullInfo)</dd>
    </dl>
    @if (Model != null)
    {
        @(await Html.PartialAsync("OLISLaboratory", Model.PrimaryPerformingLab))
        @(await Html.PartialAsync("OLISLaboratory", Model.PrimaryReportingLab))
    }
    <dl class="dl-horizontal">
        <dt class="olis-heading">@Html.DisplayNameFor(model => model.SpecimenReceived)</dt>
        <dd class="dont-break-out">@Html.DisplayFor(model => model.SpecimenReceived)</dd>

        <dt class="olis-heading">@Html.DisplayNameFor(model => model.LastUpdatedInOLIS)</dt>
        <dd class="dont-break-out">@Html.DisplayFor(model => model.LastUpdatedInOLIS)</dd>
    </dl>
</div>
