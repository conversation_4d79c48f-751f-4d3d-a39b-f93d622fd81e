@model IEnumerable<Cerebrum.ViewModels.OLIS.VMResponseReport>

<div id="OLIS-347589345" @*style="padding-top: 15px"*@>
    <div class="row">
        <div class="form-group form-group-sm col-sm-12 ">@Html.DisplayNameFor(model => model.ResponseMessage)</div>
    </div>
    @{
        var rsprpt = (from m in Model
                      group m by m.MSH10ClientTransactionID into grp
                      select new { grp }).ToList();
    }
    @foreach (var item in rsprpt)
    {
        var PIDExists = item.grp.Any(p => p.PIDExists);
        var Error = item.grp.Any(p => p.Error);
        var rpt = item.grp.FirstOrDefault();
        if (PIDExists && Error)
        {

            <p>
                <h2 class="alert-danger">@rpt.ErrorMessage</h2>
            </p>
            <div class="row">
                <div class="form-group form-group-sm col-sm-12 ">
                    @Html.ActionLink("Report", "ReportPreview", "OLIS", new { area = "Labs", report = @rpt.ReportReceivedId }, htmlAttributes: new { target = "_blank", @class = "olis-report olis-report-preview btn btn-primary" })
                    @Html.ActionLink("Summary", "ReportTestsSummary", "OLIS", new { area = "Labs", report = @rpt.ReportReceivedId }, htmlAttributes: new { target = "_blank", @class = "olis-report btn btn-primary" })
                    @Html.ActionLink("Graph/Tabular", "ReportTabularSummary", "OLIS", new { area = "Labs", report = @rpt.ReportReceivedId }, htmlAttributes: new { target = "_blank", @class = "olis-report btn btn-primary" })
                    @*<a class="olis-report btn btn-primary" data-report-url="@rpt.ReportReceivedId" href="/labs/OLIS/ReportPreview/">Report</a>
                    <a class="olis-report btn btn-primary" data-report="@rpt.ReportReceivedId" href="/labs/OLIS/ReportTestsSummary/">Summary</a>
                    <a class="olis-report btn btn-primary" data-report="@rpt.ReportReceivedId" href="/labs/OLIS/ReportTabularSummary/">Graph/Tabular</a>*@
                </div>
            </div>
        }
        else if (Error)
        {
            <p>
                <h2 class="alert-danger">@rpt.ErrorMessage</h2>
            </p>
        }
        else if (rpt.ReportReceivedId > 0)
        {
            if (PIDExists == false)
            {
                <p>
                    <h2 class="alert-danger">No results have been found in OLIS.</h2>
                </p>
            }
            <div class="row">
                <div class="form-group form-group-sm col-sm-12 ">
                    @Html.ActionLink("Report", "ReportPreview", "OLIS", new { area = "Labs", report = @rpt.ReportReceivedId }, htmlAttributes: new { target = "_blank", @class = "olis-report olis-report-preview btn btn-primary" })
                    @Html.ActionLink("Summary", "ReportTestsSummary", "OLIS", new { area = "Labs", report = @rpt.ReportReceivedId }, htmlAttributes: new { target = "_blank", @class = "olis-report btn btn-primary" })
                    @Html.ActionLink("Graph/Tabular", "ReportTabularSummary", "OLIS", new { area = "Labs", report = @rpt.ReportReceivedId }, htmlAttributes: new { target = "_blank", @class = "olis-report btn btn-primary" })
                    @*<a class="olis-report btn btn-primary" data-report="@rpt.ReportReceivedId" href="/labs/OLIS/ReportPreview/">Report</a>
                    <a class="olis-report btn btn-primary" data-report="@rpt.ReportReceivedId" href="/labs/OLIS/ReportTestsSummary/">Summary</a>
                    <a class="olis-report btn btn-primary" data-report="@rpt.ReportReceivedId" href="/labs/OLIS/ReportTabularSummary/">Graph/Tabular</a>*@
                </div>
            </div>
        }
       
    }
</div>
<br />

