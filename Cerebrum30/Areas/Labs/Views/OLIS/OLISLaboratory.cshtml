﻿@model Cerebrum.ViewModels.OLIS.VMLaboratory
<div>
    <dl class="dl-horizontal">
        <dt class="olis-heading">@Model.LabFullLabel</dt>
        <dd class="dont-break-out"><strong> @Html.DisplayFor(model => model.LabName)</strong> <span style="color:darkgrey"> (@Html.DisplayFor(model => model.FacilityName) @Html.DisplayFor(model => model.LabLicenseNo))</span></dd>
    </dl>
    @if (Model.Address != null)
    {
     @(await Html.PartialAsync("OLISAddress", Model.Address))
    }
</div>

