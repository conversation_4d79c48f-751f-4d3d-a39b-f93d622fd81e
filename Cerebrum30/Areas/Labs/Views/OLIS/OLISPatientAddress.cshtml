﻿@model IEnumerable<Cerebrum.ViewModels.OLIS.VMAddress>
@if (Model != null && Model.Count() > 0)
{
    foreach (var item in Model)
    {
<div>    
    <dl class="dl-horizontal">
        <dt class="olis-heading">Address (@Html.DisplayFor(modelitem => item.addressTypeString))</dt>
        <dd>
            @Html.DisplayFor(modelitem => item.streetAddress1)
        </dd>
        <dd>
            @{ var addline2 = $"{item.city3},{item.stateOrProvince4}, {item.zipOrPostalCode5}";}
            @addline2
        </dd>
    </dl>
</div>
    }
}