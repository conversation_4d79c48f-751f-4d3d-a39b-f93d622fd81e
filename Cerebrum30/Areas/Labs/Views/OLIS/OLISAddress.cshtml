﻿@model Cerebrum.ViewModels.OLIS.VMAddress

<div>
    @*<h4>Address @Html.DisplayFor(model => model.addressTypeString)</h4>
    <hr />*@
    <dl class="dl-horizontal">
        <dd class="dont-break-out">@Html.DisplayFor(model=>model.streetAddress1)</dd>
        <dd class="dont-break-out">@{ var addline2 = $"{Model.city3},{Model.stateOrProvince4}, {Model.zipOrPostalCode5}";}  @addline2</dd>
    </dl>
</div>
