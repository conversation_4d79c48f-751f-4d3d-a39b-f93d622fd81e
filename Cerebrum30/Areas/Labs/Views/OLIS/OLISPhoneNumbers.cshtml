﻿@model IEnumerable<Cerebrum.ViewModels.OLIS.VMPhoneNumber>
@if (Model != null && Model.Count() > 0)
{
    <div>
        <dl class="dl-horizontal">
            @foreach (var item in Model)
            {
                <dt class="olis-heading">@item.telecomEquipmentType3 (@item.PhoneType)</dt>
                <dd class="dont-break-out">@Html.DisplayFor(modelitem => item.areaOrCityCode6)@Html.DisplayFor(modelitem => item.localNumber7)</dd>
            }
        </dl>
    </div>
}