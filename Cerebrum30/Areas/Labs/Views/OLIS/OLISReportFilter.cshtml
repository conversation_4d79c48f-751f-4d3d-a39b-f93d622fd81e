﻿@model Cerebrum.ViewModels.OLIS.VMOLISReportFilter
@using Cerebrum30.Utility;
<div style="margin-top: 10px;" class="panel-group">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" href="#collapse_report_filters">Report Filter</a>
            </h4>
        </div>
        <div id="collapse_report_filters" class="panel-collapse collapse">
            <div class="panel-body">
                @using (Html.BeginForm("ReportPreview", "OLIS",new { area="Labs"},FormMethod.Post, true, new { @id = "frm-olis-rpt-filter" } ))
                {
                @Html.AntiForgeryToken()

                <div class="form-horizontal">

                    @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                    <div class="form-group">
                        @Html.LabelFor(model => model.HealthCardNumber, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.HealthCardNumber, new SelectList(Model.HealthCardNumbers.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.HealthCardNumbers.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.HealthCardNumbers[i].Value)
                                @Html.HiddenFor(x => Model.HealthCardNumbers[i].Text)
                            }
                            @*@Html.EditorFor(model => model.OrderingPractitioner, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.ValidationMessageFor(model => model.HealthCardNumber, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.LastName, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.LastName, new SelectList(Model.PatientLastNames.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.PatientLastNames.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.PatientLastNames[i].Value)
                                @Html.HiddenFor(x => Model.PatientLastNames[i].Text)
                            }
                            @*@Html.EditorFor(model => model.OrderingPractitioner, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.FirstName, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.FirstName, new SelectList(Model.PatientFirstNames.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.PatientFirstNames.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.PatientFirstNames[i].Value)
                                @Html.HiddenFor(x => Model.PatientFirstNames[i].Text)
                            }
                            @*@Html.EditorFor(model => model.OrderingPractitioner, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.OrderingPractitioner, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.OrderingPractitioner, new SelectList(Model.OrderingPractitioners.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.OrderingPractitioners.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.OrderingPractitioners[i].Value)
                                @Html.HiddenFor(x => Model.OrderingPractitioners[i].Text)
                            }
                            @*@Html.EditorFor(model => model.OrderingPractitioner, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.ValidationMessageFor(model => model.OrderingPractitioner, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.AdmittingPractioner, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @*@Html.EditorFor(model => model.AdmittingPractiioner, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.DropDownListFor(model => model.AdmittingPractioner, new SelectList(Model.AdmittingPractioners.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.AdmittingPractioners.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.AdmittingPractioners[i].Value)
                                @Html.HiddenFor(x => Model.AdmittingPractioners[i].Text)
                            }
                            @Html.ValidationMessageFor(model => model.AdmittingPractioner, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.AttendingPractitioner, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @*@Html.EditorFor(model => model.AttendingPractiioner, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.DropDownListFor(model => model.AttendingPractitioner, new SelectList(Model.AttendingPractitioners.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.AttendingPractitioners.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.AttendingPractitioners[i].Value)
                                @Html.HiddenFor(x => Model.AttendingPractitioners[i].Text)
                            }
                            @Html.ValidationMessageFor(model => model.AttendingPractitioner, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.CCedPractioner, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @*@Html.EditorFor(model => model.AttendingPractiioner, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.DropDownListFor(model => model.CCedPractioner, new SelectList(Model.CCedPractioners.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.CCedPractioners.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.CCedPractioners[i].Value)
                                @Html.HiddenFor(x => Model.CCedPractioners[i].Text)
                            }
                            @Html.ValidationMessageFor(model => model.CCedPractioners, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.LabName, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.LabLicense, new SelectList(Model.Labs.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @*@Html.EditorFor(model => model.LabName, new { htmlAttributes = new { @class = "form-control" } })*@
                            @for (int i = 0; i < Model.Labs.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.Labs[i].Value)
                                @Html.HiddenFor(x => Model.Labs[i].Text)
                            }

                            @Html.ValidationMessageFor(model => model.LabName, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.CollectionDateTime, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.CollectionDateTime, new SelectList(Model.CollectionDateTimes.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.CollectionDateTimes.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.CollectionDateTimes[i].Value)
                                @Html.HiddenFor(x => Model.CollectionDateTimes[i].Text)
                            }
                            @Html.ValidationMessageFor(model => model.CollectionDateTime, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.TestCategory, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.TestCategory, new SelectList(Model.TestCategories.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @*@Html.EditorFor(model => model.TestCategory, new { htmlAttributes = new { @class = "form-control" } })*@
                            @for (int i = 0; i < Model.TestCategories.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.TestCategories[i].Value)
                                @Html.HiddenFor(x => Model.TestCategories[i].Text)
                            }


                            @Html.ValidationMessageFor(model => model.TestCategory, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.TestRequestName, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.TestRequestCode, new SelectList(Model.TestRequestCodes.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @*@Html.EditorFor(model => model.TestRequestName, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.HiddenFor(model => model.TestRequestCode)
                            @Html.ValidationMessageFor(model => model.TestRequestName, "", new { @class = "text-danger" })
                            @for (int i = 0; i < Model.TestRequestCodes.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.TestRequestCodes[i].Value)
                                @Html.HiddenFor(x => Model.TestRequestCodes[i].Text)
                            }
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.TestRequestStatus, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.TestRequestStatus, new SelectList(Model.TestRequestStatuses.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.TestRequestStatuses.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.TestRequestStatuses[i].Value)
                                @Html.HiddenFor(x => Model.TestRequestStatuses[i].Text)
                            }
                            @Html.ValidationMessageFor(model => model.TestRequestStatus, "", new { @class = "text-danger" })
                        </div>
                    </div>
                   
                    <div class="form-group">
                        @Html.LabelFor(model => model.TestResultName, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.TestResultLOINC, new SelectList(Model.TestResultNames.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.TestResultNames.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.TestResultNames[i].Value)
                                @Html.HiddenFor(x => Model.TestResultNames[i].Text)
                            }
                            @*@Html.EditorFor(model => model.TestResultName, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.HiddenFor(model => model.TestResultLOINC)
                            @Html.ValidationMessageFor(model => model.TestResultName, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.TestResultValue, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.TestResultValue, new SelectList(Model.TestResultValues.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.TestResultValues.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.TestResultValues[i].Value)
                                @Html.HiddenFor(x => Model.TestResultValues[i].Text)
                            }
                            @Html.ValidationMessageFor(model => model.TestResultValue, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.Units, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @*@Html.EditorFor(model => model.Units, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.DropDownListFor(model => model.Unit, new SelectList(Model.Units.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.Units.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.Units[i].Value)
                                @Html.HiddenFor(x => Model.Units[i].Text)
                            }
                            @Html.ValidationMessageFor(model => model.Units, "", new { @class = "text-danger" })
                        </div>
                    </div>

                    <div class="form-group">
                        @Html.LabelFor(model => model.AbnormalFlag, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @*@Html.EditorFor(model => model.AbnormalFlag, new { htmlAttributes = new { @class = "form-control" } })*@
                            @Html.DropDownListFor(model => model.AbnormalFlag, new SelectList(Model.AbnormalFlags.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.AbnormalFlags.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.AbnormalFlags[i].Value)
                                @Html.HiddenFor(x => Model.AbnormalFlags[i].Text)
                            }
                            @Html.ValidationMessageFor(model => model.AbnormalFlag, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        @Html.LabelFor(model => model.TestResultStatus, htmlAttributes: new { @class = "control-label col-md-2" })
                        <div class="col-md-10">
                            @Html.DropDownListFor(model => model.TestResultStatus, new SelectList(Model.TestResultStatuses.Select(x => new SelectListItem { Text = x.Text, Value = x.Value }).ToList(), "Value", "Text", -1), "Select...", new { @class = "form-control" })
                            @for (int i = 0; i < Model.TestResultStatuses.Count; i++)
                            {
                                @Html.HiddenFor(x => Model.TestResultStatuses[i].Value)
                                @Html.HiddenFor(x => Model.TestResultStatuses[i].Text)
                            }
                            @Html.ValidationMessageFor(model => model.TestResultStatus, "", new { @class = "text-danger" })
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-md-offset-2 col-md-10">
                            @Html.HiddenFor(model => model.ReportId)
                            @Html.HiddenFor(model => model.PracticeId)
                            <input type="submit" value="Apply Filter" class="btn btn-default" id="btn-olis-rpt-filter" />
                        </div>
                    </div>
                </div>
            }
        </div>
        <div class="panel-footer">Report Filter</div>
    </div>
</div>
</div>


