﻿@model Cerebrum.ViewModels.OLIS.VMPractitioner

<div class="sort-provider-cced">
 
    <dl class="dl-horizontal">
        <dt>
           Name
        </dt>

        <dd>
            @Html.DisplayFor(model => model.FullName)
        </dd>
        <dt>
            Licence #           
        </dt>

        <dd>
            @Html.Raw(Model.IDNumber1)
            @{ 
                string jurisdiction = string.Empty;
            }
            @if (!string.IsNullOrWhiteSpace(Model.jurisdiction))
            {
                jurisdiction =", "+ Model.jurisdiction;
                
            }
            @Html.Raw(Model.identifierTypeCode7+ jurisdiction)
            
        </dd>

    </dl>
    
</div>

