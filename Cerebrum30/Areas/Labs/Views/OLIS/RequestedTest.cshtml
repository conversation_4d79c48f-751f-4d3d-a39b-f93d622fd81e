﻿@model Cerebrum.ViewModels.OLIS.VMOLISRequestedTest

<div class="row">
    <div class="col-md-5">
        <dl class="dl-horizontal">
            <dt>
                @Html.DisplayNameFor(model => model.SpecimenTypeOBR15)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.SpecimenTypeOBR15)
            </dd>
            <dt>
                @Html.DisplayNameFor(model => model.SiteModifier)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.SiteModifier)
            </dd>
           
            <dt>
                @Html.DisplayNameFor(model => model.CollectionVolumnOBR9)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.CollectionVolumnOBR9)
            </dd>
            <dt>
                @Html.DisplayNameFor(model => model.TestGroupOBR41)
            </dt>
            <dd>
                @Html.DisplayFor(model => model.TestGroupOBR41)
            </dd>



            <dt>
                @Html.DisplayNameFor(model => model.SpecimenCollectedBy)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.SpecimenCollectedBy)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.CollectionDateTimeOBR7)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.CollectionDateTimeOBR7)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.ObservationDateTimeOBR8)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.ObservationDateTimeOBR8)
            </dd>

           



            <dt>
                @Html.DisplayNameFor(model => model.SpecimenReceivedDateTime)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.SpecimenReceivedDateTime)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.SpecimenComment)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.SpecimenComment)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.pointOfCareTestIdentifierOBR30)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.pointOfCareTestIdentifierOBR30)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.ParentResult)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.ParentResult)
            </dd>

        </dl>
    </div>
    <div class="col-md-5">
        <dl class="dl-horizontal">
            <dt>
                @Html.DisplayNameFor(model => model.CollectionDateTime)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.CollectionDateTime)
            </dd>

            <dt>
                @Html.DisplayNameFor(model => model.NumberOfSampleContainersOBR37)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.NumberOfSampleContainersOBR37)
            </dd>
        </dl>
    </div>
    <div class="col-md-5">
        <dl class="dl-horizontal">
            <dt>
                @Html.DisplayNameFor(model => model.SpecimenCollectedBy)
            </dt>

            <dd>
                @Html.DisplayFor(model => model.SpecimenCollectedBy)
            </dd>
           
        </dl>
    </div>
</div>

