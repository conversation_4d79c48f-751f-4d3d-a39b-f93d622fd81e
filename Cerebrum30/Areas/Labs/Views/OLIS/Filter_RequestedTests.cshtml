﻿@model IEnumerable<Cerebrum.ViewModels.OLIS.VMOLISRequestedTest>
@using Cerebrum30.Utility;

@{ 
    var testgroup = (from n in Model
                     group n by new { n.TestGroupOBR41,n.TestIdentifier } into tgb
                     select new { tgb.Key, results = tgb.SelectMany(mr => mr.Results).ToList() }).ToList();
}

@foreach (var item in testgroup)
{
    var results = (from r in item.results
                   group r by r.LOINC into lg
                   select new { lg.Key, alluniq = lg.Select(s => s).ToList() }).ToList();
    if (results.Any())
    {
    <div class="panel panel-default clearBoth" data-test-request-filter-id="@item.Key.TestIdentifier">
        <div class="panel-heading report-header">@Html.CheckBox("TestGroupOBR41", new { @checked = "checked", @class = "cl-test-request-filter", data_test_request_filter_id = item.Key.TestIdentifier }) @item.Key.TestGroupOBR41</div>
        <div class="panel-body">
            <div class="col-md-10 data-filter-requested-test-testgroup" >
                <div class="col-md-10">
                    @(await Html.PartialAsync("Filter_TestResultLOINC", results.SelectMany(al => al.alluniq)).ToList())
                </div>
            </div>
        </div>
        <div class="panel-footer"></div>
    </div>
    }
}

