﻿@model Cerebrum.ViewModels.Patient.VMPatientMedicationChart

@{
    ViewBag.Title = "HL7 Result Graph";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<link href="~/Areas/Labs/CSS/c3.css" rel="stylesheet" />

<script src="~/Areas/Labs/Scripts/d3.v3.min.js"></script>
<script src="~/Areas/Labs/Scripts/c3.js"></script>
<style type="text/css">
    @@media print {

        #div-chart {
            margin-left: 0px !important;
            padding: 0px !important;
            transition: none;
        }

        #div-medication {
            display: none;
        }
        #go-back a{
           display: none !important;
        }
    }
     @@page 
    {
        size: auto;   /* auto is the initial value */
        margin: 0mm;  /* this affects the margin in the printer settings */
    }
</style>
<script type="text/javascript">

    $(function () {
        $('.list-group li').click(function (e) {
            e.preventDefault()

            $that = $(this);

            $('.list-group').find('li').removeClass('active');
            $that.addClass('active');
        });
    })
    $(document).ready(function () {
        DrawGraph();


    });
    $(function () {
        $("#btnSave").click(function (e) {
            e.preventDefault()
            window.print();
        });
    });

    /*
        please refer to  http://c3js.org for more information about this graphic chart library
    */
    function GraphValues() {
        //var graphval = '@Json.Serialize(@Model.codeGraph.graphdata)';
        //$('#chart').html(graphval);

        var result = [];
        @foreach (var v in Model.codeGraph.graphdata)
        {
            @:result.push(@Html.Raw(v));
       }
        return result;
    }
    function DrawGraph() {
        var graphValues = GraphValues();

        var chart = c3.generate({
            data: {
                x: 'x',
                //        xFormat: '%Y%m%d', // 'xFormat' can be used as custom format of 'x'
                columns: graphValues,
                colors: {
                    UpperRange: d3.rgb('#ff0000').darker(1),
                    LowerRange: d3.rgb('#ff0000').darker(1),
                }

            },
            axis: {
                x: {

                    type: 'timeseries',
                    tick: {
                        format: '%Y-%m-%d'
                    }
                },
            },
        });
        setTimeout(function () {
            chart.transform('spline', 'LowerRange');
        }, 1000);
        setTimeout(function () {
            chart.transform('spline', 'UpperRange');
        }, 1000);
    }
    function DrawGraphWithMedication(mstartdate, menddate) {
        //console.log(mstartdate + ' ' + menddate);
        var graphValues = GraphValues();

        var chart = c3.generate({
            data: {
                x: 'x',
                columns: graphValues,
                colors: {
                    UpperRange: d3.rgb('#ff0000').darker(1),
                    LowerRange: d3.rgb('#ff0000').darker(1),
                }
            },
            axis: {
                x: {
                    type: 'timeseries',
                    tick: {
                        format: '%Y-%m-%d'
                    }
                },
            },
            regions: [{ start: mstartdate, end: menddate }, ]
        });
        setTimeout(function () {
            chart.transform('spline', 'LowerRange');
        }, 1000);
        setTimeout(function () {
            chart.transform('spline', 'UpperRange');
        }, 1000);
    }
</script>
<div>
    <h2>@Model.codeGraph.testCode - Graph</h2>
    <h4>@Html.DisplayFor(model => model.OHIP) @Html.DisplayFor(model => model.FullName)</h4>

    <hr />
</div>
<div class="container-fluid">
    <div class="row-fluid">
        <div id="div-medication" class="col-lg-3">
            <div class="panel panel-success">
                <div class="panel-heading">Medications <span class="badge cbadge">@Model.medications.Count()</span></div>
                <div class="panel-body" style="max-height:500px;overflow-y: scroll;">
                    <ul class="list-group">
                        @if (Model.medications.Count() > 0)
                        {
                            foreach (var med in Model.medications)
                            {
                                var startdt = (med.DateStarted != null ? med.DateStarted.ToString("yyyy-MM-dd") : "");
                                var enddt = (med.DateDiscontinued != null ? DateTime.Parse(med.DateDiscontinued.ToString()).ToString("yyyy-MM-dd") : DateTime.Today.ToString("yyyy-MM-dd"));
                                var dat ="From: " +startdt + " To: " + enddt;
                                <li type="button" onclick="DrawGraphWithMedication('@startdt','@enddt')" class="list-group-item" data-toggle="tootip" data-placement="right" title="@dat" >@med.MedicationName</li>
                            }
                        }
                    </ul>

                </div>
            </div>
        </div>
        <div id="div-chart" class="col-lg-6">
            <div class="panel panel-success">
                @{
                    string units = string.Empty;
                    if (Model.codeGraph.testCodeResult.Count() > 0)
                    {
                        units = Model.codeGraph.testCodeResult.FirstOrDefault().Units;
                    }
                }
                <div class="panel-heading">@Model.codeGraph.testCode ( @units ) <span class="text-right pull-right"><a  id="btnSave" href="#"> Print </a></span></div>
                <div class="panel-body">
                    <div id="chart"></div>
                </div>
                    
            </div>
            @if (Model.codeGraph.testCodeResult.Count() > 0)
            {
                <div class="panel panel-success">
                    <div class="panel-heading">@Model.codeGraph.testCode Result(s)</div>
                    <div class="panel-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Result</th>
                                    <th>Reference Range</th>
                                    <th>Units</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var r in Model.codeGraph.testCodeResult.OrderBy(o=>o.resultedDateTime))
                                {
                                    <tr>
                                        <td>
                                            @if (r.resultedDateTime != null)
                                            {
                                                @Convert.ToDateTime(r.resultedDateTime).ToString("yyyy-MM-dd")
                                            }

                                        </td>
                                        <td>
                                            @Html.DisplayFor(item => r.testResult) 
                                        </td>
                                        <td>
                                            @r.referenceRange
                                        </td>
                                        <td>@r.Units</td>
                                    </tr>
                                }
                            </tbody>
                        </table>

                    </div>
                </div>
            }
            <div id="go-back">
                <h4>
                    <a href="@Model.returnUrl">Go Back</a>
                </h4>
            </div>
        </div>
    </div>

</div>
