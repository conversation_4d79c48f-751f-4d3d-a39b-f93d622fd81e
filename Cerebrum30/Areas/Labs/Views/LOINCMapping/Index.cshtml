﻿
@{
    ViewBag.Title = "HL7 Code Mapping";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<style>
    .tableFixHead {
        overflow: auto;
        height: 100px;
    }

        .tableFixHead thead tr {
            position: sticky;
            top: 0;
            z-index: 1;
        }

    /* Just common table stuff. Really. */
    table {
        border-collapse: collapse;
        width: 100%;
    }

    th, td {
        padding: 8px 16px;
    }

    th {
        background: #eee;
    }
</style>
<script src="~/Areas/Labs/Scripts/LOINCMapping.js"></script>
<h2>HL7 Code Mapping</h2>
<div class="panel panel-default">
    <div class="panel-heading">Map Code</div>
    <div id="mapping-form" class="panel-body">
        @(await Html.PartialAsync("_FormMapCode", null))       
    </div>
</div>
<div class="row">
    <div class="col-lg-5">
        <div class="panel panel-default">
            <div class="panel-heading">Search Code</div>
            <div class="panel-body">
                @using (Html.BeginForm("SearchUnmappedTest", "LOINCMapping", FormMethod.Post, new { id = "frm_srch_code" }))
                {
                    @Html.AntiForgeryToken()
                <div class="form-horizontal">
                    <div class="form-group">
                        <label id="labName" class="control-label col-md-2">Lab Name</label>
                        <div class="col-md-9">
                            @Html.Editor("LabName", new { htmlAttributes = new { @class = "form-control" } })
                        </div>
                    </div>
                    <div class="form-group">
                        <label id="labCode" class="control-label col-md-2">Lab Code</label>
                        <div class="col-md-9">
                            @Html.Editor("LabCode", new { htmlAttributes = new { @class = "form-control" } })
                        </div>
                    </div>
                    <div class="form-group">
                        <label id="testName" class="control-label col-md-2">Test Name</label>
                        <div class="col-md-9">
                            @Html.Editor("TestName", new { htmlAttributes = new { @class = "form-control" } })
                        </div>
                    </div>
                    <div class="form-group">
                        <label id="testName" class="control-label col-md-2">Type</label>
                        <div class="col-md-9">
                            @Html.RadioButton("SearchType", "1",false, new { htmlAttributes = new { @class = "form-control" } }) Mapped
                            @Html.RadioButton("SearchType", "2",false, new { htmlAttributes = new { @class = "form-control" } }) Unmapped
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-md-offset-2 col-md-10">
                            <input type="submit" value="Search Code" class="btn btn-default btn-search" />
                        </div>
                    </div>
                </div>
                }
            </div>
        </div>

        <ul class="nav nav-tabs">
            <li class="nav active"><a data-toggle="tab" href="#unmapped">Search Result</a></li>
        </ul>
        <div class="tab-content">
            <div id="unmapped" class="tab-pane fade in active">
                <p>please search code....</p>
            </div>
            <div id="mapped" class="tab-pane">
                <p>Loading....</p>
            </div>
        </div>
    </div>
    <div class="col-md-7">
        <div class="panel panel-default">
            <div class="panel-heading">OLIS LOINC Codes</div>
            <div class="panel-body">
                <div id="olis-search" class="row">
                    <p style="padding:15px;">Loading....</p>
                </div>
                <div class="row" id="div-found-code" style="margin:auto">

                </div>
            </div>
        </div>

    </div>
</div>

<div id="confirm" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Modal Header</h4>
            </div>
            <div class="modal-body">
                <p> Are you sure?</p>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-danger" id="delete">Remove</button>
                <button type="button" data-dismiss="modal" class="btn">Cancel</button>
            </div>
        </div>

    </div>
</div>
<br/>
<br />