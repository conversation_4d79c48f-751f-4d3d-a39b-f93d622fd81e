﻿@model Cerebrum.ViewModels.HL7.MapCode

@using (Html.BeginForm("SaveMapping", "LOINCMapping", FormMethod.Post, new { id = "frm_map_code" }))
{
    @Html.AntiForgeryToken()
    <span id="SpamSavedMessage"></span>
    <div class="form-inline">
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group">
            @Html.LabelFor(model => model.labName, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.labName, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.labName, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.labCode, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.labCode, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.labCode, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.labTestName, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.labTestName, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.labTestName, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.LOINC, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.LOINC, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.LOINC, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.OLISTestName, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.OLISTestName, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.OLISTestName, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                @Html.HiddenFor(model => model.Id)
                <input type="submit" value="Map Code" class="btn btn-default btn-map-save" />
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <span id="SavedMessage"></span>
            </div>
        </div>
    </div>
}


<script type="text/javascript">
    $(document).ready(function () {
        $("#frm_map_code").submit(function (event) {
            event.preventDefault();
            var form = $(this);
            var urlaction = form.attr('action');
            var data = $(this).serialize();
            $.post(urlaction, data).done(function (r) {
                if (r.success == true) {
                    showNotificationMessage('success', r.message);
                    $("#mapping-form").load("/Labs/LoincMapping/FormMapCode");
                    $(".remove-when-done").remove();
                } else {
                    $("#mapping-form").load("/Labs/LoincMapping/FormMapCode");
                    showNotificationMessage('warning', r.message);
                }

            });
        });
    });

</script>