@model Cerebrum.ViewModels.HL7.HL7ResultNoteVM
@using Cerebrum30.Helpers

@using (Html.BeginForm("AddComment", "hl7", new { area = "labs" }, FormMethod.Post, true, new { @id = "frm-add-hl7-comment" }))
{
    @Html.Mo<PERSON>eader("Add Comment")
    <div class="modal-body">
        @Html.AntiForgeryToken()

        <div class="form-horizontal">
      
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })

            <div class="form-group">
                @Html.LabelFor(model => model.comments, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.comments, new { @class = "form-control count-text", @rows = 4, data_max_length = 3000 })
                    @*@Html.TextAreaFor(model => model.comments, new { htmlAttributes = new { @class = "form-control", @rows = 4 } })*@
                    @Html.ValidationMessageFor(model => model.comments, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-offset-2 col-md-10">
                    @Html.HiddenFor(model => model.HL7ResultId)
                    @Html.HiddenFor(model => model.setId)
                    @Html.HiddenFor(model => model.createdDate)
                </div>
            </div>
        </div>
    </div>

    @Html.ModalFooter("Save", "blue")
}

