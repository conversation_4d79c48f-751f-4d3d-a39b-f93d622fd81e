@model Cerebrum.ViewModels.Schedule.VMOfficeSchedule

@{
    var on = (Model != null && Model.officeId > 0 ? " - " + Model.officeName : "");
    ViewBag.Title = "Office Schedule";
    ViewBag.ModuleName = "Office Schedule" + on;
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

<h2 id="h2-office-schedule">
    Office Schedule
    @( Model != null && Model.officeId > 0 ? " - " + Model.officeName : "")
</h2>

<div class="form-inline">
    <div class="form-group">
        <label for="officeId">Office</label>
        @Html.DropDownList("officeId", new SelectList(Model.Offices, "Id", "Name", Model.officeId), "Select Office...", new { @class = "form-control btn btn-default" })
    </div>
    <div class="form-group">
        <label for="currentdate">Date</label>
        @*@Html.TextBox("currentdate", Model.date != null ? DateTime.Parse(Model.date.ToString()).ToString("MM-dd-yyyy") : "", new { @class = "form-control", @placeholder = "Date" })*@

        @Html.TextBox("currentdate", Model.date != null ? DateTime.Parse(Model.date.ToString()).ToString("MM/dd/yyyy") : "", new { @class = "form-control", @placeholder = "Date" })
    </div>
    <hr />
    <div class="form-group">
        <label>Office Note</label>
        <input type="hidden" id="NoteId" value="" />
        <textarea maxlength="150" id="sch-note" class="form-control" oninput="updateCount('sch-note-char-count', this.value)"></textarea>
        <span id="sch-note-char-count"></span>
    </div>
    <div class="form-group">
        <button id="btn-sch-note" class="btn btn-primary">Add Note</button>
        <button id="btn-sch-note-delete" class="btn btn-danger">Delete Note</button>
    </div>
    <hr />
    @if (Model.date != null)
    {
        var dow = (DateTime)Model.date;
    <div class="form-group">
        <button id="btn-sch-off" class="btn btn-primary">Set @dow.ToLongDateString() Off for All staff</button>
    </div>
    }
</div>
<br />
<div id="office-staff-sch">
    @(await Html.PartialAsync("_OfficeStaffSchedule", Model))
</div>

<div class="modal fade" id="sch-alert-modal" role="dialog">
    <div class="modal-dialog">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h2 class="modal-title"><span class=""></span>Schedule Change Warning</h2>
            </div>
            <div class="modal-body">
                <blockquote>
                    <p class="modal-body-text">
                    </p>
                    <p class="modal-body-ds"></p>
                </blockquote>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger confirmclosed" data-dismiss="modal">Close</button>
                <span style="display: none; padding-top: 1px;" id="span-loading-set-dayoff"><img src="~/Content/Images/loading.gif" style="width:20px;" /></span>
            </div>
        </div>
    </div>
</div>

<script src="~/Areas/Schedule/Scripts/staffSchedule.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $("#currentdate").datepicker();

        $("#officeId, #currentdate").change(function () {
            var officeid = $("#officeId").val();
            var officeName = $('#officeId').find(":selected").text();
            var dt = $("#currentdate").val();
            if (officeid != '') {
                var urlpath = '@Url.Action("Index")' + '?officeId=' + $("#officeId").val() + "&currentDate=" + $("#currentdate").val();
                window.history.pushState({ urlPath: urlpath }, "", urlpath);
                $('#h2-office-schedule').text('Office Schedule - ' + officeName);
                LoadStaffSchedule(officeid, dt);
                LoadOfficeNote(officeid, dt);
            }
            else {
                $('#h2-office-schedule').text('Office Schedule');
            }
            var da = moment($('#currentdate').val(), 'MM/DD/YYYY').format('dddd, MMMM D, YYYY');
            $('#btn-sch-off').html('Set ' + da + ' Off for All staff');
        });

        $("#btn-sch-note").click(function () {
            var savenote = $("#sch-note").val();

            $.post({
                url: '/Schedule/ScheduleNote/SaveNote',
                data: { OfficeId: $("#officeId").val(), dt: $("#currentdate").val(), note: savenote }
            }).done(function (data) {
                console.log(data);

                showNotificationMessage("success", "Note Saved");

            });;
        });
        $("#btn-sch-note-delete").click(function () {
            var savenote = $("#sch-note").val();
            $.post({
                url: '/Schedule/ScheduleNote/DeleteNote',
                data: { NoteId: $("#NoteId").val() }
            }).done(function (data) {
                console.log(data);

                showNotificationMessage("success", "Note Saved");
                LoadOfficeSchedulePage();
            });;
        });

        $("#btn-sch-off").click(function () {
            var newbtn = '<button type="button" id="sch-confirm-save" class="btn btn-primary confirmSave" >Yes</button>';
            $(".confirmSave").remove();
            $('.modal-title').text('');
            $('.modal-body-ds').text('');
            if ($("#officeId").val() == '') {
                alert('Please select Office!');
                return;
            }
            $.get("/schedule/schedulenote/AnyAppointments", { officeid: $("#officeId").val(), dt: $("#currentdate").val() }, function (rsp) {
                if(rsp==='')
                {
                    $('.modal-body-text').text('Office:' + $("#officeId option:selected").text() + ' Date:' + $("#currentdate").val() + '\nSet Schedule Off for all Staff');
                    $('.modal-title').text("PROCEED WITH CAUTION");
                    $('.modal-footer').append(newbtn);
                    
                    $('#sch-confirm-save').click(function () { //Waring Modal Confirm Close Button
                        console.log('Confirmed clicked');
                        $(this).css("display", "none");
                        $('#span-loading-set-dayoff').css("display", "inline-block");
                        $.post('/schedule/office/ScheduledayOffForAll/', { OfficeId: $("#officeId").val(), date: $("#currentdate").val() }).done(function (data) {
                            if (data.success) {
                                $('#Warning').modal('hide'); //Hide Warning Modal
                                $('#Form').modal('hide'); //Hide Form Modal
                                $('#sch-alert-modal').modal('toggle');
                                $('#sch-confirm-save').css("display", "inline-block");
                                $('#span-loading-set-dayoff').css("display", "none");
                                setTimeout(function () {
                                    window.location.href = '@Url.Action("Index")' + '?officeId=' + $("#officeId").val() + "&currentDate=" + $("#currentdate").val();
                                }, 3000);
                            }
                            else {
                                showHideBtnConfirmSave();
                            }
                        }).fail(function () {
                            showHideBtnConfirmSave();
                        });
                    });
                }
                else {
                    if (rsp.indexOf('Error getting appointments for') > -1) {
                        $('.modal-title').text("Error");
                        $('.modal-body-ds').text(rsp);
                    }
                    else {
                        $('.modal-title').text("Appointments Found on this day.");
                        $('.modal-body-ds').text(rsp);
                    }
                }
            });

            $('#sch-alert-modal').modal('show').on('show.bs.modal', function () {

                // = window.location.href;
                $('.confirmclosed').click(function () { //Waring Modal Confirm Close Button
                    $('#Warning').modal('hide'); //Hide Warning Modal
                    $('#Form').modal('hide'); //Hide Form Modal
                });
            });
        });
        var officeIdurl = getUrlParameter('officeId');
        var currentDateurl = getUrlParameter('currentDate');
        LoadOfficeNote(officeIdurl, currentDateurl);
    });

    function updateCount(id, val) {
        var cs = val.length + '/150';
        $('#' + id).text(cs);
    }

    function showHideBtnConfirmSave() {
        $('#sch-confirm-save').css("display", "inline-block");
        $('#span-loading-set-dayoff').css("display", "none");
        $('.modal-title').text("Error");
        $('.modal-body-ds').text('Error when Set Schedule Off for all Staff');
    }
    function LoadStaffSchedule(officeid,currentDate){
        $("#office-staff-sch").html('<img src="../../Content/fancybox_loading.gif" />');
        $.ajax({
            type:'GET',
            url: '/Schedule/office/GetAllStaffSchedule',
            data: { OfficeId: officeid, currentdate: currentDate },
            success: function (resp) {
                $("#office-staff-sch").html(resp);
            }
        });
    }

    function LoadOfficeNote(officeid,currentDate)
    {
        if (officeid && currentDate) {
            console.log("Loading Schedule Note ");
            $.get("/Schedule/ScheduleNote/", { OfficeId: officeid, dt: currentDate }).done(
                function (note) {
                    $("#sch-note").val(note.note);
                    $("#NoteId").val(note.NoteId);
                    console.log("Data Loaded: " + note.note);
                }).always(function () {
                    console.log("Schedule Note Loaded ");
                });
        }
        //$.ajax({
        //    url: '/Schedule/ScheduleNote/',
        //    data: { OfficeId: officeid, dt: currentDate }}).done(
        //    function (note) {
        //        $("#sch-note").val(note.note);
        //        $("#NoteId").val(note.NoteId);
        //    }
        //);
    }

    function LoadOfficeSchedulePage() {
        var officeid = $("#officeId").val();
        var dt = $("#currentdate").val();
        if (officeid != '') {
            var urlpath = '@Url.Action("Index")' + '?officeId=' + $("#officeId").val() + "&currentDate=" + $("#currentdate").val();
            window.history.pushState({ urlPath: urlpath }, "", urlpath);
            //window.location.href = '@Url.Action("Index")' + '?officeId=' + $("#officeId").val() + "&currentDate=" + $("#currentdate").val();
            LoadStaffSchedule(officeid, dt);
            LoadOfficeNote(officeid, dt);
        }
    }
    var getUrlParameter = function getUrlParameter(sParam) {
        var sPageURL = decodeURIComponent(window.location.search.substring(1)),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');

            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : sParameterName[1];
            }
        }
    };
</script>