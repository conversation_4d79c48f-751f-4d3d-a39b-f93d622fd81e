﻿@model Cerebrum.ViewModels.Schedule.VMAppointmentOption

<div class="row app-option">
    @Html.HiddenFor(model => model.OptionNumber)
    @Html.HiddenFor(model => model.PracticeId)
    @Html.HiddenFor(model => model.OfficeId)
    @Html.HiddenFor(model => model.PatientId)
    @Html.HiddenFor(model => model.ReferralDoctorId)
    @Html.HiddenFor(model => model.AppointmentTypeId)
    @Html.HiddenFor(model => model.AppointmentTypeName)
    @Html.HiddenFor(model => model.RequestedDate)
    @Html.HiddenFor(model => model.StartTime)
    @Html.HiddenFor(model => model.EndTime)
    @Html.HiddenFor(model => model.UseForm)

    @if (Model.UseForm)
    {
        @Html.AntiForgeryToken()
    }
    @{ string collapseId = "collapseOption-" + Model.OptionNumber;}
    <div class="col-md-12">
        <div class="row">
            <div class="col-md-4">@Html.DisplayTextFor(model => model.StartTime) - @Html.DisplayTextFor(model => model.EndTime)</div>
            <div class="col-md-4">@Html.DisplayTextFor(model => model.TotalDuration)</div>
            <div class="col-md-4 text-right">
                @if (Model.UseForm)
                {
                    <button type="submit" class="btn btn-primary btn-xs modal-submit-btn">Select</button>
                }
                else
                {                    
                    <label class="btn btn-primary btn-xs">
                        @Html.CheckBoxFor(model => model.Selected, new { @class = "app-opt-select" }) 
                    </label>                    
                }
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <a data-toggle="collapse" href="#@collapseId">View Details</a>
                <div class="collapse" id="@collapseId">
                    <ul class="list-group">
                        @Html.EditorFor(model => model.SelectedTests)
                    </ul>
                </div>
            </div>
        </div>
        <hr />
    </div>
</div>