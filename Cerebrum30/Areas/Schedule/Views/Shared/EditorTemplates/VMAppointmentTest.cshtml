﻿@model Cerebrum.ViewModels.Schedule.VMAppointmentTest

<li>
    @Html.HiddenFor(model => model.AppointmentTestId)
    @Html.HiddenFor(model=> model.TestId)    
    @Html.HiddenFor(model => model.Name)
    @Html.HiddenFor(model => model.TestDate)
    @Html.HiddenFor(model => model.TestStartTime)
    @Html.HiddenFor(model => model.TestEndTime)
    @Html.HiddenFor(model => model.TestDuration)
    @Html.HiddenFor(model => model.IsRadiology)
    @Html.HiddenFor(model => model.Modalities)
    @Html.HiddenFor(model => model.ModalitiesCount)
    @Html.HiddenFor(model => model.Category)
    @Html.HiddenFor(model => model.CategoryId)
    @Html.HiddenFor(model => model.DoctorsCount)

    @for (int i = 0; i < Model.TestResources.Count(); i++)
    {
    @Html.EditorFor(model => Model.TestResources[i], "VMHiddenAppTestResource", String.Format("{0}[{1}]", "TestResources", i))
    }
    <div class="row">
        <div class="col-lg-1 text-right">
            <strong>@Html.DisplayTextFor(model => model.Name)</strong>
        </div>
        <div class="pull-left">
            @Html.DisplayTextFor(model => model.TestStartTime) - @Html.DisplayTextFor(model => model.TestEndTime)
        </div>
        <div style="padding-left:5px;" class="pull-left italic">
            @Html.DisplayNameFor(model => model.TestDuration): @Html.DisplayTextFor(model => model.TestDuration)
        </div>
        <div style="padding-left:5px;" class="pull-left italic">
            @Html.EditorFor(model => model.TestResources, "VMTestResources")
        </div>
    </div>
</li>