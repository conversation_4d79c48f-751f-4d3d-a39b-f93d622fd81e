﻿@model Cerebrum.ViewModels.Schedule.VMAppointmentOption

@{ 
    var totalDuration = TimeSpan.FromMinutes(Model.TotalDuration);
    var hours = totalDuration.Hours;
    var minutes = totalDuration.Minutes; 
}

@Html.HiddenFor(model => model.OptionNumber)
@Html.HiddenFor(model => model.PracticeId)
@Html.HiddenFor(model => model.OfficeId)
@Html.HiddenFor(model => model.RequestedDate)
@Html.HiddenFor(model => model.StartTime)
@Html.HiddenFor(model => model.EndTime)
@Html.HiddenFor(model => model.TotalDuration)

<div class="row app-option">      
    <div class="col-md-12">
        <hr />
        <div class="row">
            <div class="col-md-8">
                <span><strong>@Model.OptionNumber. </strong></span>
                <span><strong>@Html.DisplayName(Model.RequestedDate.ToString("MMM-dd-yyyy")) @Html.DisplayTextFor(model => model.StartTime) - @Html.DisplayTextFor(model => model.EndTime)</strong> </span> 
                <span style="padding-left:20px;" class="italic"><strong>@Html.DisplayNameFor(model => model.TotalDuration):</strong> </span>
                @if (hours > 0)
                { <span>@hours Hours</span>  }

                @if (hours > 0 && minutes > 0)
                { <span> and </span>  }

                @if (minutes > 0)
                {<span>@minutes Minutes</span> }

                @*<span>@Html.DisplayTextFor(model => model.TotalDuration) min</span>*@               
            </div>
            <div style="padding-right:25px;" class="col-md-4 text-right option-select-container">
                @Html.HiddenFor(model => model.Selected, new { @class = "app-opt-select" })
                <button type="button" class="btn btn-primary btn-xs modal-submit-btn btn-app-option-select">Select</button>
            </div>            
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="app-option-test-container">                    
                    <ul class="list-group">
                        @Html.EditorFor(model => model.SelectedTests)
                    </ul>
                </div>
            </div>
        </div>        
    </div>
</div>



