﻿@model Cerebrum.ViewModels.Schedule.VMAppointmentTest

@Html.HiddenFor(model => model.TestId)
@Html.HiddenFor(model => model.TestOrder)
@Html.HiddenFor(model => model.TestDuration)
@Html.HiddenFor(model => model.ReferralDoctorId)
@Html.HiddenFor(model => model.AppointmentTestId)
@Html.HiddenFor(model => model.Name) 
@Html.HiddenFor(model => model.TestFullName) 
@Html.HiddenFor(model => model.Selected)
@Html.HiddenFor(model => model.IsRadiology)
@Html.HiddenFor(model => model.Modalities)
@Html.HiddenFor(model => model.ModalitiesCount)
@Html.HiddenFor(model => model.Category)
@Html.HiddenFor(model => model.CategoryId)
@Html.HiddenFor(model => model.DoctorsCount)
@for (int i = 0; i < Model.TestResources.Count(); i++)
{
    @Html.EditorFor(model => Model.TestResources[i], "VMHiddenAppTestResource", String.Format("{0}[{1}]", "TestResources", i))
}