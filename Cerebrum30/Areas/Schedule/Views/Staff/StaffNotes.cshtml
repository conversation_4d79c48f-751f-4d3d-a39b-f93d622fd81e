﻿@model Cerebrum.ViewModels.Schedule.VMStaffNoteMain
@using Cerebrum30.Utility;
@{
    ViewBag.Title = "Staff Notes";
    ViewBag.ModuleName = "Staff Notes (Schedule)";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}

<script src="~/Areas/Schedule/Scripts/staffSchedule.js"></script>
<div style="margin-top:15px;"></div>

@using (Html.BeginForm("GetStaffNotesList", "staff", new { area = "schedule" }, FormMethod.Get, true, new { @class = "form-inline", @id = "frm-staffnote-load" }))
{
    <div class="form-group form-group-sm">
        <label class="control-label" for="OfficeId">Office</label>
        @Html.DropDownList("OfficeId", new SelectList(ViewBag.Offices, "Id", "Name", Model.OfficeId), "Choose One", new { @class = "form-control" })
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.Date, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.Date, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.UserTypeId, htmlAttributes: new { @class = "control-label" })
        @Html.DropDownListFor(model => model.UserTypeId, new SelectList(AwareMD.Cerebrum.Shared.Enums.UserTypeEnum.GetValues(typeof(AwareMD.Cerebrum.Shared.Enums.UserTypeEnum)).Cast<AwareMD.Cerebrum.Shared.Enums.UserTypeEnum>().Select(x => new SelectListItem { Text = x.DisplayName(), Value = ((int) x).ToString() }).OrderBy(o=>o.Text).ToList(), "Value", "Text",Model.UserTypeId), "Select...", new { @class = "form-control" })
    </div>

    <button id="btn-view-staff" type="button" style="margin-right:5px;" class="btn btn-default btn-xs">View Staff</button>    
    <span id="msg-staff-load" class="text-primary"></span>   
}

<div>
    @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
</div>

<div style="margin-top:15px;"></div>

<div id="staff-note-list" data-url="@Url.Action("GetStaffNotesList","Staff",new { area="schedule" })">
    @{ await Html.RenderPartialAsync("_staffNoteList", Model.StaffNotes);}
</div>
<br />
<br />
