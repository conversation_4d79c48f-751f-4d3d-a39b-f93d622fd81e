﻿@model IEnumerable<Cerebrum.ViewModels.Schedule.VMStaffNote>

@{ 
    var totalItems = Model.Count();
    var items = Model.ToList();
}

<style>
    .txt-staff-note
    {
        width:300px;
    }
</style>

@using (Html.BeginForm("SaveAllStaffNotes", "staff", new { area = "schedule" }, FormMethod.Post, true, new { @id = "frm-staff-notes-all", @class = "" }))
{
    @Html.AntiForgeryToken();
<div class="panel panel-default">
    <div class="panel-heading">Staff (@totalItems)</div>
  
    <div class="panel-body">
        <table class="table table-bordered table-condensed table-hover">
            <tr>
                <td>#</td>
                <th>
                    @Html.DisplayNameFor(model => model.LastName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.FirstName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Date)
                </th>
                <th style="width:40%;">
                    @Html.DisplayNameFor(model => model.Notes)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.UserType)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.OfficeRoomId)
                </th>
                <th style="width:25px;">
                    @if (totalItems > 0)
                {
                        <button id="btn-save-all-staff-notes" type="submit" class="btn btn-default btn-xs">Save All</button>
                }
                </th>
            </tr>

            @for (int i = 0; i < totalItems; i++)
        {
            var item = items[i];
                <tr id="<EMAIL>" class="<EMAIL>" data-userType="@item.UserType">
                    <td>@(i+1)</td>
                    @Html.EditorFor(model => items[i], "VMStaffNoteTemplate", String.Format("{0}[{1}]", "staffNotes", i))
                </tr>
        }

        </table>
    </div>
</div>
}