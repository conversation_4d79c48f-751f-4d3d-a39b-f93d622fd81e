﻿@model Cerebrum.ViewModels.Schedule.VMScheduleWeekDay
@if (Model != null)
{
    @Html.AntiForgeryToken()

        <div class="form-inline">

            <hr />
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })

            <div class="form-group">
                @Html.LabelFor(model => model.OfficeId, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-10">
                    @Html.DropDownListFor(model => model.OfficeId, new SelectList(Model.Offices, "Id", "Name"), "Office...", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.startTime, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.startTime, htmlAttributes: new { @class = "control-label col-xs-3" })
                <div class="col-md-10">
                    @Html.DropDownListFor(model => model.startTime, new SelectList(Model.startEndTimes, Model.startTime), "time...", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.startTime, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.finishTime, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-10">
                    @Html.DropDownListFor(model => model.finishTime, new SelectList(Model.startEndTimes, Model.finishTime), "time...", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.finishTime, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.absentVM, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-10">

                    @Html.ListBoxFor(model => model.absentTimes, new MultiSelectList(Model.breakTimes, Model.absentTimes), new { multiple = "multiple", @class = "form-control" })

                    @Html.ValidationMessageFor(model => model.absentTime, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.reservedVM, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-10">
                    @Html.ListBoxFor(model => model.reservedTimes, new MultiSelectList(Model.breakTimes , Model.reservedTimes), new { multiple = "multiple", @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.reservedTime, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.comment, htmlAttributes: new { @class = "control-label col-md-3" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.comment, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.comment, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                <div class="col-md-offset-2 col-md-10">
                    @Html.HiddenFor(model => model.Id)
                    @Html.HiddenFor(model => model.ScheduleUserId)
                    @Html.HiddenFor(model => model.OfficeId)
                    @Html.HiddenFor(model => model.dayOfWeek,Model.dayOfWeek)
                    @Html.HiddenFor(model => model.createdDate)
                    <input type="submit" value="Save" class="btn btn-default" />
                </div>
            </div>
        </div>

}
