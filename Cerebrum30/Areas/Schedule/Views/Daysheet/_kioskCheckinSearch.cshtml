﻿@model Cerebrum.ViewModels.Kiosk.VMKioskCheckinSearch


@using (Html.BeginForm("kioskcheckins", "daysheet", new { area = "schedule" }, FormMethod.Get, true, new { @class = "form-inline", @id = "frm-kiosk-checkins" }))
{   
    @Html.HiddenFor(model => model.OfficeId)
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.CheckinDate, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.CheckinDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>  

    <button id="btn-search-kiosk-checkins" type="submit" style="margin-right:5px;" class="btn btn-default btn-xs">Search</button>       

}