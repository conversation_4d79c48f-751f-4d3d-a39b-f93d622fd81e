@model Cerebrum.ViewModels.Schedule.VMDaysheetBase
@if (Model.Office != null)
{
    var urlFormBase = Url.Action("index", "daysheet", new { area = "schedule" });

    var urlToday =
        Url.Action("index", "daysheet", new
        {
            area = "schedule",
            OfficeId = CerebrumUser.OfficeId,
            Date = System.DateTime.Now.ToShortDateString(),
            //TestGroupIds = CerebrumUser.DSFilterTestGroups,           
            AppointmentStatusId = CerebrumUser.DSFilterAppointmentStatusId,
            Expected = CerebrumUser.DSFilterExpected,
            ExcludeTestOnly = CerebrumUser.DSFilterExcludeTestOnly,
            ExcludeCancelled = CerebrumUser.DSFilterExcludeCancelled,
            OnlyActionOnAbnormal = CerebrumUser.DSFilterOnlyActionOnAbnormal,
            ShowOrders = CerebrumUser.DSFilterShowOrders
        });
    var officeDesc = "Daysheet for " + Model.Office.Name + " Office";

@Html.Hidden("page-refresh","no")
<div class="row row-top-padding">
    <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4" data="1">
        <div class="form-inline">
            <form id="frm-office-date" action="@urlFormBase" method="get" class="__887034">
                @{
                    var officeId = Model.Office != null ? Model.Office.Id : 0;
                                <div class="form-group form-group-sm">
                                    <label class="control-label" for="OfficeId">Office</label>
                                    <select class="form-control" name="OfficeId" id="OfficeId">
                                        @for (int i = 0; i < Model.Offices.Count; i++)
                                        {
                                            var selected = "";
                                            if (Model.Offices[i].Id == officeId)
                                            {
                                                selected = "selected='selected'";
                                            }
                                            <option value="@Model.Offices[i].Id" selected="@(selected == "selected")">@Model.Offices[i].Name</option>
                                        }
                                    </select>
                                </div>
                    <div class="form-group form-group-sm">
                        <div>                            
                            @*@Html.Hidden("TestGroupIds", CerebrumUser.DSFilterTestGroups)*@                            
                            @Html.Hidden("AppointmentStatusId", CerebrumUser.DSFilterAppointmentStatusId)
                            @Html.Hidden("Expected", CerebrumUser.DSFilterExpected)
                            @Html.Hidden("ExcludeTestOnly", CerebrumUser.DSFilterExcludeTestOnly)
                            @Html.Hidden("ExcludeCancelled", CerebrumUser.DSFilterExcludeCancelled)
                            @Html.Hidden("OnlyActionOnAbnormal", CerebrumUser.DSFilterOnlyActionOnAbnormal)
                            @Html.Hidden("ShowOrders", CerebrumUser.DSFilterShowOrders)
                            
                        </div>
                        <label class="control-label" for="Date">Date</label>
                        @Html.TextBox("Date", String.Format("{0:MM/dd/yyyy}", Model.SelectedDate), new { @class = "form-control date-picker input-sm" })
                    </div>
                    <div class="form-group form-group-sm">
                        <a href="@urlToday" class="btn btn-default btn-xs">Today</a>
                    </div>
                }
            </form>
        </div>
    </div>
    
    <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4" data="2">
        <div style="padding-top:6px;padding-right:3px;" class="pull-left">Schedule</div>
        <div class="btn-group btn-spacing pull-left" role="group">            
            @foreach (var item in Model.ScheduleTypes)
            {
                var urlSchType = "";
                urlSchType = Url.Action("index", "scheduleview", new { area = "schedule", OfficeId = Model.Office.Id, ScheduleTypeId = item.Id, Date = String.Format("{0:MM/dd/yyyy}", Model.SelectedDate) });

                <a data-form-url="@urlFormBase" class="btn btn-default btn-sm office-schedule"
                   href="@urlSchType">
                    @item.Name
                </a>                
            }
        </div>

        @if (Model.Office.HasSchedule)
        {
            <script type="text/javascript">
                $(function () {
                    loadAppScheduleModal(@Model.Office.PracticeId,@Model.Office.Id);                   
                });
            </script>
            <div class="pull-left">
                <a data-modal-url="@Url.Action("create", "appointments", new { area = "schedule" })"
                   data-practice-id="@Model.Office.PracticeId"
                   data-office-id="@Model.Office.Id"
                   data-appointment-day="@Model.SelectedDate.ToShortDateString()"                                     
                   data-book-type="@Cerebrum.ViewModels.Schedule.BookRequestType.Normal"
                   class="btn-sch-new-appointment btn btn-sm btn-default btn-spacing" href="#">
                    <span class="glyphicon glyphicon-calendar text-primary"></span> New Appointment
                </a>
            </div>           

        }
        else
        {
            <div class="pull-left text-danger">
                Please contact Admin for Office Schedule
            </div>
        }      
    </div>


    <div class="col-xs-12 col-sm-12 col-md-4 col-lg-4" data="3">
        <div class="text-right">               
            <a id="btn-show-overdue-devices" class="btn btn-default btn-sm" data-office-id="@Model.Office.Id" data-selected-date="@Model.SelectedDate.ToShortDateString()" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="View Over Due Devices" data-url="@Url.Action("GetOverDueDevices", "StoreInventory", new { area = "AdminUser" })"><span class="glyphicon glyphicon-phone default-text-color"> </span> Devices</a> 
            <a class="btn btn-default btn-sm btn-add-patient"><span class="glyphicon glyphicon-user default-text-color"> </span> Add Patient</a>                
            <a class="btn btn-default btn-sm btn-schedule-print" data-modal-url="@Url.Action("GetSchedulePrint", "scheduleview", new { area = "schedule", Date = Model.SelectedDate.ToShortDateString(), OfficeId = Model.Office.Id })"><span class="glyphicon glyphicon-print default-text-color"> </span> Print</a>
            <a class="btn btn-default btn-sm btn-view-swipehealthcard" data-modal-url="@Url.Action("GetSwipeHealthcard", "Appointments", new { area = "schedule" })"><span class="glyphicon glyphicon-credit-card default-text-color"> </span> Swipe Card</a>

            @if (CerebrumUser.HasPermission("UploadReports"))
            {
                <a class="btn btn-default btn-sm" href="@Url.Action("assignment", "externaldocument", new { area = "externaldocument", externaldocument = "fax", officeId = Model.Office.Id, practiceId = Model.Office.PracticeId })">
                <span class="glyphicon glyphicon-folder-open default-text-color"> </span>&nbsp; Upload Reports</a>
            }
            @if (CerebrumUser.HasPermission("Billing,BillingAdmin"))
            {
                string unbillButtonDisplay = "none";
                if (Model.DaySheetBilled)
                {
                    unbillButtonDisplay = "inline-block";
                }
                <a class="btn btn-default btn-sm btn-view-prebill-office" data-bill-date="@Model.SelectedDate.ToString("MM/dd/yyyy", System.Globalization.CultureInfo.InvariantCulture)" data-bill-office-id="@Model.Office.Id"><span class="glyphicon glyphicon-usd default-text-color"> </span> Bill</a>
                <a class="btn btn-default btn-sm btn-view-unbill-office" style="display: @unbillButtonDisplay;" data-bill-date="@Model.SelectedDate.ToString("MM/dd/yyyy", System.Globalization.CultureInfo.InvariantCulture)" data-bill-office-id="@Model.Office.Id"><span class="glyphicon glyphicon-usd default-text-color"> </span> UnBill</a>
            }
        </div>

    </div>
</div>

    <div class="row">
        <div style="display:none; background-color:#ff8800;height: 20px;" class="col-xs-12 col-sm-12 col-md-12 col-lg-12" id="scheduleNotes" data-office-id="@Model.Office.Id" data-date="@String.Format("{0:MM/dd/yyyy}", Model.SelectedDate)" data-url="@Url.Action("GetScheduleNotes","scheduleview", new { area="schedule" })">&nbsp;</div>
    </div>
    <script type="text/javascript">
        $(function () {
            setTimeout(function () { getScheduleNotes(); }, 0);
        });
    </script>



 }

