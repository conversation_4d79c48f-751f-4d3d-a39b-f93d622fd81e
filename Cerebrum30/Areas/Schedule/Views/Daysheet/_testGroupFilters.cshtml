@model IEnumerable<Cerebrum.ViewModels.Common.VMLookupItem>

@{ 
    var testGroups = Model.ToList();
    var selectedTestGroups = testGroups.Where(x => x.Selected == true).ToList();
    var totalSelectedGroups = selectedTestGroups.Count();
    var listName = "testGroups";
    var allChecked = testGroups.Count() == totalSelectedGroups ? "checked" : "";


}

<div id="ds-test-group-btn-wrapper">
    <button id="btn-ds-test-group-filter" type="button" class="btn btn-white-box btn-xs">Test Groups <span id="ds-filter-count-testgroups">(@totalSelectedGroups)</span></button>

    <div id="btn-ft-container-test-groups" class="btn-popover-container" style="clear:both; display:block">
        <div class="btn-popover-title">Test Groups</div>
        <div class="btn-popover-content">
            <div class="test-group-content-wrapper">
                <div style="padding-bottom:5px;margin-bottom:5px;border-bottom: 1px solid #c8c5c5;">
                    <input name="selectAllTestGroups" id="selectAllTestGroups" type="checkbox" @allChecked /> All
                </div>
                
                <form class="frm-ds-test-group-filters" method="post" role="form" action="@Url.Action("SaveTestGroupFilters", "daysheet", new { area = "schedule" })">
                    @Html.Hidden("allChecked",false)
                    <div class="form-horizontal">
                        <div class="form-group form-group-sm">
                            <div class="col-md-12">
                                <div class="content-height300">
                                    <div id="ds-test-group-filters">

                                        <ul id="ds-test-group-ul">
                                            @for (int i = 0; i < testGroups.Count(); i++)
                                    {
                                        var item = testGroups[i];
                                        var inputNameBase = listName + "[" + i + "].";
                                        var inputIdBase = listName + "_" + i + "__";
                                                <li>
                                                    @Html.Hidden(inputNameBase + "Text", item.Text, new { @id = inputIdBase + "Text" })
                                                    @Html.Hidden(inputNameBase + "Value", item.Value, new { @id = inputIdBase + "Value" })
                                                    @Html.Hidden(inputNameBase + "Code", item.Code, new { @id = inputIdBase + "Code" })
                                                    @Html.Hidden(inputNameBase + "DisplayOrder", item.DisplayOrder, new { @id = inputIdBase + "DisplayOrder" })
                                                    <label class="">
                                                        @Html.CheckBox(inputNameBase + "Selected", item.Selected, new
                                                   {
                                                       @autocomplete = "off",
                                                       @class = "cb-test-group-filter",
                                                       data_code = item.Code,
                                                       data_item_value = item.Value,
                                                       data_item_text = item.Text,
                                                       data_item_displayorder = item.DisplayOrder
                                                   })
                                                        @item.Text
                                                    </label>
                                                </li>
                                    }
                                        </ul>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary btn-xs modal-submit-btn">Save</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

</div>

