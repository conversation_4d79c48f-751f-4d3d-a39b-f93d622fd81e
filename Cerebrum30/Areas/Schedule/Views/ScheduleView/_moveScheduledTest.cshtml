@model Cerebrum.ViewModels.Schedule.VMMoveRequest

@using (Html.BeginForm("SaveMoveScheduledTest", "scheduleview", new { area = "schedule" }, FormMethod.Post, true, new { @id = "frm-move-scheduled-test", @class = "" }))
{
    @Html.ModalHeader("Reassign Test: "+Model.TestName)
    <div class="modal-body">
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => model.PracticeId)
        @Html.HiddenFor(model => model.OfficeId)
        @Html.HiddenFor(model => model.TestId)
        @Html.HiddenFor(model => model.TestName)
        @Html.HiddenFor(model => model.PatientId)
        @Html.HiddenFor(model => model.PatientFullName)
        @Html.HiddenFor(model => model.ScheduleDate)
        @Html.HiddenFor(model => model.TimeStart)
        @Html.HiddenFor(model => model.AppointmentId)
        @Html.HiddenFor(model => model.AppointmentTestId)
        @Html.HiddenFor(model => model.TimeStart)
        @Html.HiddenFor(model => model.CurrentUserId)
        @Html.HiddenFor(model => model.CurrentUserName)
        @Html.HiddenFor(model => model.CurrentUserType)
        @Html.HiddenFor(model => model.PermissionId)
        
        @for (int i = 0; i < Model.Resources.Count; i++)
        {            
            @Html.HiddenFor(model => Model.Resources[i].Text)
            @Html.HiddenFor(model => Model.Resources[i].Value)               
        }

        <div class="form-horizontal">

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.CurrentUserName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    <p class="form-control-static">@Html.DisplayFor(model => model.CurrentUserName)</p>
                    @Html.ValidationMessageFor(model => model.CurrentUserName, "", new { @class = "text-danger" })
                </div>

                @Html.LabelFor(model => model.AssignedToUserId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.AssignedToUserId, new SelectList(Model.Resources, "Value", "Text", Model.AssignedToUserId), "Choose One" ,new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.AssignedToUserId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PatientFullName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <p class="form-control-static">@Html.DisplayFor(model => model.PatientFullName)</p>
                </div>
            </div>
            
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.ScheduleDate, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <p class="form-control-static">@Model.ScheduleDate.ToShortDateString()</p>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.TestName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <p class="form-control-static">@Html.DisplayFor(model => model.TestName)</p>
                </div>
            </div>
            


        </div><!-- end form horizontal-->
    </div><!--Modal body end-->
    @Html.ModalFooter("Reassign", "blue")
}