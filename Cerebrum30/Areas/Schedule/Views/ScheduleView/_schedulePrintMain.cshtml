@model Cerebrum.ViewModels.Schedule.VMSchedulePrintMain

@Html.ModalHeader("Schedule Printing")

<div id="schedule-print-List-container-id" class="modal-body">
    <div id="schedule-print-search">
        @(await Html.PartialAsync("_schedulePrintSearch", Model.PrintSearch))
    </div>
    <hr />
    <div class="text-right" id="schedule-print-contrainer">
        <button type="button" class="btn btn-default btn-sm btn-print-div noprint" onclick="printAppointmentList('schedule-print-List-container-id');"><span class="glyphicon glyphicon-print default-text-color"></span> Print </button>
        @(await Html.PartialAsync("_schedulePrintPaging", Model.PrintSearch))
    </div>
    <div id="schedule-print-List-container">
        @if (Model.Appointments.Any())
        {

            <div class="panel panel-info">
                <div class="panel-heading">
                    <h3 class="panel-title">Appointments <span class="badge cbadge">@Model.Appointments.Count()</span></h3>
                </div>
                <div style="overflow-x:auto;" class="content-height300">
                    <table id="table-schedule-print-apps" class="table  .table-striped  .table-bordered  .table-hover ">
                        <thead>
                            <tr>
                                <th style="width:20%;">
                                    
                                    <a class="th-sortable-header" data-sort-order="@Model.PrintSearch.SortPatientName" href="#">
                                        @Html.DisplayNameFor(model => model.Appointments[0].patientName)
                                    </a>
                                </th>
                                <th>
                                    @Html.DisplayNameFor(model => model.Appointments[0].patientDOB)
                                </th>
                                <th>
                                    <a class="th-sortable-header" data-sort-order="@Model.PrintSearch.SortDate" href="#">
                                        Appointment Time
                                    </a>
                                </th>
                                <th>
                                    Double Booking
                                </th>
                                <th>
                                    <a class="th-sortable-header" data-sort-order="@Model.PrintSearch.SortDoctor" href="#">
                                        @Html.DisplayNameFor(model => model.Appointments[0].Doctor)
                                    </a>
                                </th>
                                <th>
                                    <a class="th-sortable-header" data-sort-order="@Model.PrintSearch.SortRefDoctor" href="#">
                                        @Html.DisplayNameFor(model => model.Appointments[0].RefDoctor)
                                    </a>
                                </th>

                                <th>
                                    <a class="th-sortable-header" data-sort-order="@Model.PrintSearch.SortAppType" href="#">
                                        @Html.DisplayNameFor(model => model.Appointments[0].appointmentType)
                                    </a>
                                </th>
                               
                                <th>
                                    <a class="th-sortable-header" data-sort-order="@Model.PrintSearch.SortTests" href="#">
                                        Test (Duration)
                                    </a>
                                </th>

                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.Appointments)
                            {
                                <tr>
                                    <td>
                                        @item.patientName
                                    </td>
                                    <td>
                                        @item.patientDOB
                                    </td>
                                    <td>
                                        @Html.Label(item.appointmentDateTime.ToShortTimeString())
                                    </td>

                                    <td>

                                        @if (item.IsDoubleBooking == true)
                                        {
                                            <span class="glyphicon glyphicon-asterisk"></span>
                                        }
                                    </td>
                                    <td>
                                        @item.Doctor
                                    </td>
                                    <td>
                                        @item.RefDoctor
                                    </td>
                                    <td>
                                        @item.appointmentType
                                    </td>
                                   
                                    <td>
                                        @item.Tests
                                    </td>

                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>

        }
    </div>
</div>

@Html.ModalFooter(isInfoModal: true)




<script type="text/javascript">


    function printAppointmentList(divId) {

        var content = document.getElementById(divId).innerHTML;
        var mywindow = window.open('', 'Print', 'height=600,width=800');
        mywindow.document.write('<html><head><title>Print</title>');
        mywindow.document.write('<link rel="stylesheet" href="/Content/bootstrap.css" type="text/css" media="print">');
        mywindow.document.write('</head><body >');
        mywindow.document.write(content);
        mywindow.document.write('</body></html>');
        mywindow.focus()
        mywindow.print();
        return true;
    }


</script>