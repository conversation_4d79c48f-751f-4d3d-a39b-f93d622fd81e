﻿@model Cerebrum.ViewModels.Schedule.VMAppointment

@Html.HiddenFor(model => model.Id)
@Html.HiddenFor(model => model.PracticeId)
@Html.HiddenFor(model => model.OfficeId)
@Html.HiddenFor(model => model.PatientId)
@Html.HiddenFor(model => model.PatientFullName)
@Html.HiddenFor(model => model.ReferralDoctorId)
@Html.HiddenFor(model => model.ReferralDoctor)
@Html.HiddenFor(model => model.PracticeDoctorId)
@Html.HiddenFor(model => model.AppointmentTypeId)
@Html.HiddenFor(model => model.AppointmentType)
@Html.HiddenFor(model => model.AppointmentParentTypeId)
@Html.HiddenFor(model => model.AppointmentParentTypeName)
@Html.HiddenFor(model => model.ResourceId)
@Html.HiddenFor(model => model.ResourceName)
@Html.HiddenFor(model => model.ResourceTypeId)
@Html.HiddenFor(model => model.PaymentMethodId)
@Html.HiddenFor(model => model.Purpose)
@Html.HiddenFor(model => model.Notes)
@Html.HiddenFor(model => model.ActionOnAbnormal)
@Html.HiddenFor(model => model.HasOptions)
@Html.HiddenFor(model => model.DisplayOptions)
@Html.HiddenFor(model => model.WeekDayOnly)
@Html.HiddenFor(model => model.AppointmentDate)
@Html.HiddenFor(model => model.AppointmentTime)
@Html.HiddenFor(model => model.WaitingListId)
@Html.HiddenFor(model => model.IsCancel)
@Html.HiddenFor(model => model.BookRequestType)
@Html.HiddenFor(model => model.RequisitionId)
@Html.HiddenFor(model => model.CancellationList)
@Html.HiddenFor(model => model.IsImported)
@Html.HiddenFor(model => model.RecordType)
@Html.HiddenFor(model => model.AppointmentPriorityId)
@Html.HiddenFor(model => model.ServiceRequestId)
@for (int i = 0; i < Model.Tests.Count(); i++)
{
    @Html.EditorFor(model => Model.Tests[i], "VMHiddenAppTest", String.Format("{0}[{1}]", "Tests", i))
}

@for (int i = 0; i < Model.PreConditions.Count(); i++)
{
    @Html.EditorFor(model => Model.PreConditions[i], "VMHiddenPrecondition", String.Format("{0}[{1}]", "PreConditions", i))
}     