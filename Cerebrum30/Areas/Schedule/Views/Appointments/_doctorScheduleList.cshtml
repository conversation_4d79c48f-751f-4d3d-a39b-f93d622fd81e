@model IEnumerable<Cerebrum.ViewModels.Schedule.VMDoctorSchedule>

<div class="content-height500">
    <table class="table">
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Officename)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Date)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.StartTime)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.FinisihTime)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.Comments)
            </th>
            <th></th>
        </tr>

        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @item.Officename
                </td>
                <td>
                    @item.Date
                </td>
                <td>
                    @item.StartTime
                </td>
                <td>
                    @item.FinisihTime
                </td>
                <td>
                    @item.Comments
                </td>
                <td>
                    @Html.ActionLink("Daysheet", "day","appointments", new { area="schedule", officeId = item.OfficeId, Date = item.Date.ToShortDateString(), DoctorId = item.DoctotId },null) |
                    @Html.ActionLink("Schedule", "index", "appointments", new { area = "schedule", officeId = item.OfficeId, Date = item.Date.ToShortDateString() }, null)
                    
                </td>
            </tr>
        }

    </table>

</div>