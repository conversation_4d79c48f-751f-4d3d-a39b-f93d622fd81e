@model Cerebrum.ViewModels.Schedule.VMAppointment
<style>
    body {
        background-color: #f8f8f8 !important;
    }

    .radio {
        margin-top: auto;
        margin-bottom: auto;
    }

    table tr:nth-child(even) {
        background: none !important;
    }

    table tr:nth-child(odd) {
        background: none !important;
    }

    .table > thead > tr > th,
    .table > tbody > tr > th,
    .table > tfoot > tr > th,
    .table > thead > tr > td,
    .table > tbody > tr > td,
    .table > tfoot > tr > td {
        padding: 8px;
        line-height: 1.42857143;
        vertical-align: middle;
        border-top: 1px solid #ddd;
    }

    .modal {
        overflow-y: auto;
    }

    .table-hover > tbody > tr:hover {
        background-color: #f5f5f5 !important;
    }

    .label-bold {
        font-weight: bold !important;
    }
</style>

@Html.ModalHeader("Reject Appointment")
@*<div class="modal fade" id="reject-appointment-modal-container">
        <div class="modal-dialog">
            <div class="modal-content">

            </div>
        </div>
    </div>*@
<div class="modal-body content-height500">
    <div class="row" style="padding-left:16px">

    </div>
    <div class="row" style="padding-top:16px; padding-left:16px">
        <div class="col-md-6">
            <div class="row">
                <lable class="control-label label-bold">Patient</lable>
                @Html.TextBoxFor(model => model.PatientFullName, htmlAttributes: new { disabled = "disabled", @class = "form-control" })
            </div>
            <div class="row" style="padding-top:4px">
                <lable class="control-label label-bold">Appointment date and time</lable>
                @Html.TextBoxFor(model => model.AppointmentDate, htmlAttributes: new { disabled = "disabled", @class = "form-control" })
            </div>
        </div>
        <div class="col-md-6">
            <lable class="control-label"><span class="label-bold">Reasons to Reject</span> (<span class="text-danger">max length is 1000 characters</span>)</lable>
            <textarea class="form-control" cols="63" rows="4" id="notes" maxlength="1000"></textarea>
        </div>
    </div>
    <div class="row" style="padding-top:20px">
        <div class="modal-footer">
            <div class="pull-right">
                <input type="hidden" id="patientId" value="@Model.PatientId" />
                <input type="hidden" id="appointmentId" value="@Model.Id" />
                <button type="button" class="btn btn-success btn-sm" id="btn-reject-appointment">
                    Reject
                    <span style="visibility:hidden;padding-left:2px;" class="span-loading-reject-appointment">
                        <img src="~/Content/Images/loading.gif" style="width:20px;" />
                    </span>
                </button>
                <button type="button" class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

