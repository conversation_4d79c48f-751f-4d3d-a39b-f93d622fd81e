
@Html.ModalHeader("Swipe Healthcard")

<div class="modal-body">

    <div class="form-group form-group-sm">
        <input id="txt-healthcard" name="txt-healthcard" type="text" class="form-control" />
    </div>
    <div id="patient-arrived" class="alert alert-success" role="alert"></div>
    <div class="hc-info"></div>
    <button type="submit" id="btn-addpatientswipe" class="btn btn-primary btn-sm">Add New</button>
    
</div>

@Html.ModalFooter(isInfoModal: true)