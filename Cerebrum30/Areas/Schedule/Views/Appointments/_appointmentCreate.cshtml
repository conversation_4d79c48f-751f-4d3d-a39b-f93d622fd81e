@model Cerebrum.ViewModels.Schedule.VMAppointment
@{ 
    bool showAppointmentPriority = CerebrumUser.IsAppointmentPriorityEnabled && Convert.ToBoolean(ViewBag.IsPriorityExists) && CerebrumUser.IsPracticeAppointmentPriorityEnabled;
}

@using (Html.BeginForm("create", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @id = "frm-appointment-create", @class = "sch-app-form" }))
{
    var showResourceName = Model.PracticeDoctorId <= 0 && Model.ResourceId > 0 ? "" : "hidden";

    @Html.ModalHeader("Create Appointment")
    <div class="modal-body">
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.HiddenFor(model => model.FhirId)
        @Html.HiddenFor(model => model.PracticeId)
        @Html.HiddenFor(model => model.ResourceId)
        @Html.HiddenFor(model => model.ResourceName)
        @Html.HiddenFor(model => model.PatientId)
        @Html.HiddenFor(model => model.PatientFhirId)
        @Html.HiddenFor(model => model.ReferralDoctorId)
        @Html.HiddenFor(model => model.DisplayOptions)
        @Html.HiddenFor(model => model.BookRequestType)
        @Html.HiddenFor(model => model.RequisitionId)
        @Html.HiddenFor(model => model.IsImported)
        @Html.HiddenFor(model => model.AppointmentParentTypeName)
        <div class="form-horizontal">


            <div class="@showResourceName" id="app-schedule-resource-name">
                <div class="form-group form-group-sm">
                    @Html.LabelFor(model => model.ResourceName, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-4">
                        <p id="resource-name-display" class="form-control-static">@Html.DisplayFor(model => model.ResourceName)</p>
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Office, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.OfficeId, new SelectList(ViewBag.PracticeOffices, "Id", "Name", Model.OfficeId), new { @class = "form-control form-office-drop-down" })
                    @Html.ValidationMessageFor(model => model.OfficeId, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AppointmentDate, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.AppointmentDate, new { htmlAttributes = new { @class = "form-control date-picker", @readonly = "readonly" } })
                    @Html.ValidationMessageFor(model => model.AppointmentDate, "", new { @class = "text-danger" })
                </div>
                <div class="col-md-4">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => model.WeekDayOnly) <span class="checkbox-text">@Html.DisplayNameFor(model => model.WeekDayOnly)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.WeekDayOnly, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AppointmentTime, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.AppointmentTime, new { htmlAttributes = new { @class = "form-control time-autocomplete" } })
                    <p class="help-block">Select time from the list</p>
                    @Html.ValidationMessageFor(model => model.AppointmentTime, "", new { @class = "text-danger" })
                </div>

                <div class="col-md-4">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => model.ForceBooking) <span class="checkbox-text">@Html.DisplayNameFor(model => model.ForceBooking)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.ForceBooking, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PracticeDoctorId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.PracticeDoctorId, new SelectList(ViewBag.PracticeDoctors, "PracticeDoctorId", "FullNameReversed", Model.PracticeDoctorId), new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.PracticeDoctorId, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PatientFullName, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-4">
                    <div style="min-width:250px;" class="pull-left">
                        @Html.EditorFor(model => model.PatientFullName, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    <div class="pull-left">
                        <a style="margin-left:5px;" class="btn btn-xs btn-default btn-add-patient" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Add New Patient" href="#"><span class="glyphicon glyphicon-plus-sign text-primary"></span></a>
                    </div>
                    <div class="clearfix"></div>
                    <p class="help-block">Select patient from the list</p>
                    @Html.ValidationMessageFor(model => model.PatientFullName, "", new { @class = "text-danger" })
                    @Html.ValidationMessageFor(model => model.PatientId, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.ReferralDoctor, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <div style="min-width:250px;" class="pull-left">
                        @Html.EditorFor(model => model.ReferralDoctor, new { htmlAttributes = new { @class = "form-control" } })
                    </div>
                    <div class="pull-left">
                        <a style="margin-left:5px;" class="btn btn-xs btn-default btn-add-ext-doctor" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Add New Doctor" href="#"><span class="glyphicon glyphicon-plus-sign text-primary"></span></a>
                    </div>
                    <div class="pull-left">
                        <span data-external-doc-id="@Model.ReferralDoctorId"
                              data-patient-name="@Model.PatientFullName"
                              data-patient-record-id="@Model.PatientId"
                              data-doctor-type="@AwareMD.Cerebrum.Shared.Enums.DocType.Referral"
                              data-appointment-id="0"
                              data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Edit referral doctor report contact information"
                              class="glyphicon glyphicon-home btn-get-exdoc-report-contact c-pointer"></span>
                    </div>
                    <div class="clearfix"></div>
                    <p class="help-block">Select referral doctor from the list</p>
                    @Html.ValidationMessageFor(model => model.ReferralDoctor, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.AppointmentTypeId, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.AppointmentTypeId, new SelectList(ViewBag.AppointmentTypeItems, "Id", "Name", "AppointmentType", Model.AppointmentTypeId), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.AppointmentTypeId, "", new { @class = "text-danger" })
                </div>
                @if (showAppointmentPriority)
                {
                    @Html.LabelFor(model => model.AppointmentPriorityId, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-4">
                        @Html.DropDownListFor(model => model.AppointmentPriorityId, new SelectList(ViewBag.AppointmentPriorityItems, "Id", "PriorityName", Model.AppointmentPriorityId), "", new { @class = "form-control" })
                    </div>
                }
            </div>
            <div class="form-group form-group-sm" config-cat="integrations_ocean-e-referral" style="display:none;">
                @Html.LabelFor(model => model.ServiceRequestId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <wc-service-request-select 
                    style-class="form-control c3-select"
                    id="wc-service-request-select"
                    input-id="ServiceRequestId" 
                    input-name="ServiceRequestId" 
                    patient-id="@Model.PatientFhirId" 
                    service-request-id="@Model.ServiceRequestId"/>
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Tests, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div id="selected-office-tests">
                        @{ await Html.RenderPartialAsync("_appointmentSelectedTestsList", Model.Tests);}
                    </div>
                    <div>@Html.ValidationMessageFor(model => model.Tests, "", new { @class = "text-danger" })</div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PreConditions, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    <div class="btn-group appointment-preconditions" data-toggle="buttons">
                        @Html.EditorFor(model => model.PreConditions)
                    </div>
                </div>
            </div>



            <div class="form-group form-group-sm">
                <div class="col-md-2">
                </div>
                <div class="col-md-2">
                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => model.ActionOnAbnormal) <span class="checkbox-text">@Html.DisplayNameFor(model => model.ActionOnAbnormal)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.ActionOnAbnormal, "", new { @class = "text-danger" })
                    </div>

                    <div class="checkbox">
                        <label>
                            @Html.EditorFor(model => model.CancellationList) <span class="checkbox-text">@Html.DisplayNameFor(model => model.CancellationList)</span>
                        </label>
                        @Html.ValidationMessageFor(model => model.CancellationList, "", new { @class = "text-danger" })
                    </div>


                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PaymentMethodId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.PaymentMethodId, new SelectList(ViewBag.PaymentTypes, "Id", "Name"), "Choose One", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.PaymentMethodId, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Purpose, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.TextAreaFor(model => model.Purpose, new { @class = "form-control auto-expand", maxlength = 500 })
                    @Html.ValidationMessageFor(model => model.Purpose, "", new { @class = "text-danger" })
                </div>
                @Html.LabelFor(model => model.Notes, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.TextAreaFor(model => model.Notes, new { @class = "form-control auto-expand", maxlength = 1000 })
                    @Html.ValidationMessageFor(model => model.Notes, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.WaitingListId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-4">
                    @Html.DropDownListFor(model => model.WaitingListId, new SelectList(ViewBag.WaitingListStatuses, "Id", "Description", Model.WaitingListId), "", new { @class = "form-control" })
                    @Html.ValidationMessageFor(model => model.WaitingListId, "", new { @class = "text-danger" })
                </div>
            </div>



        </div><!-- end form horizontal-->
    </div><!--Modal body end-->
    @Html.ModalFooter("Create Appointment", "blue")
}

