﻿@model Cerebrum.ViewModels.Schedule.VMCommentSearch

@using (Html.BeginForm("commentssearch", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @class = "form-inline", @id = "frm-comment-search" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.PatientId)
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.CommentStart, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.CommentStart, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.CommentEnd, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.CommentEnd, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>
    <button type="submit" style="margin-right:5px;" class="btn btn-default btn-xs">Search</button>

}
<div>
    @Html.ValidationMessageFor(model => model.CommentStart, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.CommentEnd, "", new { @class = "text-danger" })
</div>