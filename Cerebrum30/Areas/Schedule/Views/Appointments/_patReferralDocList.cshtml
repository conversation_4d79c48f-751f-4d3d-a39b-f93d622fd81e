@model IEnumerable<Cerebrum.ViewModels.Schedule.VMReferralDocument>

@{ 
    var totalDocs = Model.Count();
    var docNumber = 1;
}
 
<div class="panel panel-info content-height300">
    <div class="panel-heading">
        <h3 class="panel-title">Referral Documents <span  id ="spantotalreferdocnumber" class="badge cbadge">@totalDocs</span></h3>
    </div>
    <table id="table-ref-documents" class="table">
        <thead>
            <tr>
                <th style="width:25%;">
                    @Html.DisplayNameFor(model => model.FileName)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.FileDescription)
                </th>
                <th style="width:10%;">

                </th>     
                <th style="width:10%;">

                </th>                          
            </tr>
        </thead>
    <tbody>
       
        @foreach (var item in Model) {
            <tr>
                <td>
                    @item.FileName
                </td>
                <td>
                    @item.FileDescription
                </td>
                @*<td>
                    <button data-document-total="@totalDocs" data-document-num="@docNumber" data-document-id="@item.Id" data-document-path="@item.FilePath" type="button" class="btn btn-xs btn-primary btn-view-document">View</button>
                </td>*@  
                <td>
                    <a target="_blank" href="/Measurements/Measurement/RetreiveFile?path=@item.FilePath.Replace("\\" ,"|").Replace(":", "||")&appointmentID=@item.ApppintmentID">@item.FilePath</a>
                    @*@Html.ActionLink(@item.FilePath, "RetreiveFile", "Measurement", new { @Area="Measurements", path=item.FilePath, token=item.Token, appointmentID=item.ApppintmentID   })*@
                </td>
                <td>
                    <button data-document-total="@totalDocs" data-document-num="@docNumber" data-document-id="@item.Id" data-document-path="@item.FilePath" type="button" class="btn btn-xs btn-info btn-inactive-document">Inactive</button>
                </td>                
            </tr>
            docNumber++;
        }
    </tbody>
    </table>
</div>