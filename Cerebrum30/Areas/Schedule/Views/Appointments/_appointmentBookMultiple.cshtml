@model Cerebrum.ViewModels.Schedule.VMAppointmentBook

@using (Html.BeginForm("BookAppointmentOptions", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @class= "appointment-book" }))
{
    //var prefix = ViewData.TemplateInfo.HtmlFieldPrefix;
    //ViewData.TemplateInfo.HtmlFieldPrefix = String.Empty;
    var appTypes = Model.AppointmentOptions.GroupBy(g => g.AppointmentTypeName).ToList();
    int totalAppTypes = appTypes.Count();
    int index = 0;
    string appMessage = totalAppTypes > 1 ? "Book Appointments" : "Book Appointment";

    @Html.ModalHeader( appMessage + " for " + Model.PatientName)
    <div class="modal-body">    

        <div id="appointment-options">                    
            
                @for (int i = 0; i < totalAppTypes; i++)
                {
                    var appointmentType = appTypes[i].Key.ToString();
                    var options = appTypes[i].ToList();

                    <div class="app-type-group">
                        @if (totalAppTypes > 1)
                        {
                            <div><h3>@appointmentType</h3></div>
                        }

                        @for (int j = 0; j < options.Count(); j++)
                        {                            
                            @Html.EditorFor(model => options[j], null, String.Format("{0}[{1}]", "AppointmentOptions", index))
                            index++;
                        }                        
                    </div>
                }                
        </div>
    </div><!--Modal body end-->
    @Html.ModalFooter(appMessage, "blue")

    //ViewData.TemplateInfo.HtmlFieldPrefix = prefix;
}