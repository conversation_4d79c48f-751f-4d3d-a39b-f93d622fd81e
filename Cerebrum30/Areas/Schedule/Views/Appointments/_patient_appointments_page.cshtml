@model Cerebrum.ViewModels.Schedule.VMPatientAppointmentPage
@Html.ModalHeader("Appointments")
<style>
    ul.horizontal-list {
        min-width: 696px;
        list-style: none;
        padding: 5px;
    }

        ul.horizontal-list li {
            display: inline;
        }
</style>
<div>
    @if (Model != null)
    {

        <div class="panel panel-info content-height300">
            <div class="panel-heading">
                <h3 class="panel-title"> Total <span class="badge cbadge">@Model.TotalRows</span></h3>

            </div>
            <div class="panel-body">
                <div id="patient-appointment-list">
                    @Html.RenderPartialAsync("_patient_appointments_list", Model);}
                </div>

            </div>
        </div>
        <div>
            <ul class="nav nav-pills horizontal-list">

                @for (int i = 1; i <= Model.TotalPages; i++)
                {
                    var active = Model.PageNum == i ? "active" : "";
                    <li class="apage-item appointments-page @active" data-page="@i" data-patient="@Model.PatientRecordId" data-size="@Model.PageSize" data-url="@Url.Action("GetPatientsAllAppointments", "Appointments",new {area= "Schedule" })"><a class="audit-page">@i</a> </li>
                }
            </ul>
        </div>
        <input type="hidden" id="hdn_name" value="@Model.PatientFullName" />

                        }
</div>
<script>
    var formatName = function (arg) {
        var formattedName = arg;
        if (arg.indexOf(',') != -1) {
            var arr = arg.split(',');
            formattedName = arr[0].toUpperCase() + ', ' + arr[1];
        }

        return formattedName;

    }

    $(document).ready(function () {
        var modalTitle = 'Appointments';
        if ($('#hdn_name').val().length > 0) {
            modalTitle = modalTitle + ' for ' + formatName($('#hdn_name').val());
        }
        $('#patientAppsModal .modal-title').html(modalTitle);

        $(document).on('click', 'li.appointments-page', function (e) {
            e.preventDefault();
            var hasClass = 'active';
            var _url = $(this).data("url");
            var _pageSize = $(this).data("size");
            var _currentPage = $(this).data("page");
            var _patient = $(this).data("patient");

            var _data = { patientId: _patient, pageNum: _currentPage };

            $('.horizontal-list').find('li.appointments-page').removeClass(hasClass);

            if (true !== $(this).hasClass(hasClass)) {

                // Show page as active selected
                $('.horizontal-list').find('li.appointments-page').each(function (index) {
                    var otherPage = $(this).data('page');
                    if (otherPage === _currentPage) {
                        $(this).addClass(hasClass);
                    }
                });

                $("#ajax-loader").show();
                $.ajax({
                    url: _url,
                    type: 'GET',
                    data: _data,
                    success: function (result) {
                        $("#ajax-loader").hide();
                        $('#patient-appointment-list').html(result);
                    }
                });
            }
        });
    });

</script>
@Html.ModalFooter(isInfoModal: true)
