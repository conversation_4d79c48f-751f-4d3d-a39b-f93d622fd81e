@model List<Cerebrum.ViewModels.Schedule.VMBookingComment>
@using Cerebrum.ViewModels.Schedule;

@{
    VMAppointment appointment = (VMAppointment)ViewBag.Appointment;

    var appointmentBookingComment = (Cerebrum.ViewModels.Schedule.DTO.AppointmentBookingCommentDto)ViewBag.AppointmentBookingComment;
    string comments = string.Empty;
    if (!string.IsNullOrEmpty(appointmentBookingComment?.BookingCommentNote))
    {
        comments = appointmentBookingComment.BookingCommentNote;
    }
}

@using (Html.BeginForm("SaveBookingComments", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @id = "frm-booking-comments-edit", @class = "sch-app-form" }))
{
    var ul = "";
    var phoneNumbers = new List<string>();

    if (!String.IsNullOrWhiteSpace(appointment.PhoneNumber))
    {
        phoneNumbers = appointment.PhoneNumber.Split(',').ToList();

        string listItems = "";

        foreach (var item in phoneNumbers)
        {
            listItems += "<li>" + item + "</li>";
        }

        ul = "<ul>" + listItems + "</ul>";
    }

    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
        <div class="pull-left"><h4 class="modal-title"><span>Booking Comments for @appointment.PatientFullName </span></h4></div>
        <div style="padding-top:7px;padding-left:15px;" class="pull-left">

            @if (!String.IsNullOrWhiteSpace(appointment.DateOfBirth))
            {
                <span style="padding-right:5px;"><strong>DOB:</strong> @appointment.DateOfBirth</span>
            }

            @if (!String.IsNullOrWhiteSpace(appointment.HealthCard))
            {
                <span style="padding-right:5px;"><strong>HealthCard:</strong> @appointment.HealthCard</span>
            }

            <span style="padding-right:5px;"><strong>Appointment Date:</strong> @appointment.AppointmentDate</span>

            @if (!String.IsNullOrWhiteSpace(ul))
            {
                <span style="color:#428bca;" class="c-pointer" data-html="true" data-toggle="tooltip" data-placement="bottom" title="@ul">Phone numbers</span>
            }

        </div>
        <div class="clearfix"></div>
    </div>

    <div class="modal-body">
        @(await Html.PartialAsync("_validationSummary"))
        @Html.AntiForgeryToken()
        @Html.Hidden("AppointmentId", appointment.Id)

        <div class="row">
            @foreach (var item in Model)
            {
                <div class="col-md-6">
                    <div class="col-md-1">@Html.CheckBox("bookingCommentId" + item.id, item.isSelected, new { @class = "checkbox chk-booking-comments", @data = @item.id })</div>
                    <div class="col-md-11" style="padding-left:1px;">@item.bookingComment.commentHeader</div>
                </div>
            }
        </div>
        <br /><br />
        <div class="row">
            <div class="col-md-1">Comments</div>
            <div class="col-md-11">
                @Html.TextArea("BookingCommentNote", comments, new
                       {
                           @class = "form-control",
                           @rows = 5,
                           //@cols = 400,
                           autocmplete = "off",
                           style = "width: 400px;",
                           @maxlength = 1000

                       })
                <span class="text-danger">max length is 1000 characters</span>
            </div>
        </div>
    </div>
    @Html.ModalFooter("Save", "blue")

}
