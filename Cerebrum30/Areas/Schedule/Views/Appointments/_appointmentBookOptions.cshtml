@model Cerebrum.ViewModels.Schedule.VMAppointment

        @{ 
            var actionUrl = Model.Id > 0 ? "edit" : "create";
        }
   
        @using (Html.BeginForm("AppointmentOptionSearch", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @id = "frm-appointmentbook-search" }))
        {
            @Html.AntiForgeryToken()
            @(await Html.PartialAsync("_appointmentHidden", Model))           
        }
       
        @using (Html.BeginForm(actionUrl, "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @id = "frm-appointmentbook-options" }))
        {
            @Html.ModalHeader("Book Appointment for " + Model.PatientFullName)
            <div class="modal-body">               

                @Html.AntiForgeryToken()
                @(await Html.PartialAsync("_appointmentHidden", Model))
                @*adding dates hidden here as well*@
                @*@Html.HiddenFor(model => model.AppointmentDate, new { @id = "AppointmentDateOption" })
                @Html.HiddenFor(model => model.AppointmentTime)*@
                @{ 
                    DateTime nextDate = Model.AppointmentDate;
                    if(Model.HasOptions && Model.Options.Count()>0)
                    {
                        nextDate = Model.Options.LastOrDefault().RequestedDate;
                    }
                }
                <div class="row">
                    <div class="col-md-6">
                    @{ var disablePrevious = Model.AppointmentDate.ToShortDateString() == System.DateTime.Now.ToShortDateString() ? "disabled" : ""; }
                    <button data-requested-date="@nextDate" data-url="@Url.Action("AppointmentSearchPrevious","appointments", new { area="schedule" })" type="button" id="btn-appprevious-options" class="btn btn-xs btn-primary modal-submit-btn @disablePrevious">Previous</button>
                    </div>
                    <div class="col-md-6 text-right">
                    <button data-requested-date="@nextDate" data-url="@Url.Action("AppointmentSearchNext","appointments", new { area="schedule" })" type="button" id="btn-appnext-options" class="btn btn-xs btn-primary modal-submit-btn">Next</button>
                    </div>
                </div>
                <div class="pull-left">
                    <h4>@String.Format("{0:dddd, MMMM d, yyyy}", Model.AppointmentDate) @Html.DisplayFor(model => model.AppointmentTime) - <strong>@Html.DisplayFor(model => model.Office)</strong></h4> 
                </div>
                <div class="pull-right">
                    @if (Model.WeekDayOnly)
                    {
                        <h4> @Model.AppointmentDate.DayOfWeek's only</h4>
                    }                    
                </div>
                <div class="clearfix"></div>
                
               
                @(await Html.PartialAsync("_validationSummary"))
                <div id="appointment-options">
                    <div class="app-type-group">
                        @for (int i = 0; i < Model.Options.Count(); i++)
                        {
                            var option = Model.Options[i];
                            var inputName = String.Format("{0}[{1}]", "Options", i);
                            @Html.EditorFor(model => Model.Options[i], "VMAppOption", String.Format("{0}[{1}]", "Options", i))
                        }
                    </div>
                </div>                

            </div><!--Modal body end-->
            @*@Html.ModalFooter("Select","blue")*@
            @Html.ModalFooter(isInfoModal: true)
        }
  




   
