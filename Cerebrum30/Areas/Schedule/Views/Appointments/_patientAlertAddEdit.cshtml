﻿@model Cerebrum.ViewModels.Patient.PatientMessageAlert
@{
    string btnSaveAlertText = "Save Alert";
    if (Model.PatientAlertId == 0)
    {
        Model.IsActive = true;
        btnSaveAlertText = "Add Alert";
    }
    string strChecked = Model.IsActive ? "checked='checked'" : string.Empty;
    string endDate = string.Empty;
    string startDate = (Model.StartDate).ToString("MM/dd/yyyy").Replace("-", "/");

    if (Model.EndDate.HasValue)
    {
        endDate = ((DateTime)Model.EndDate).ToString("MM/dd/yyyy").Replace("-", "/");
    }
}

@using (Html.BeginForm("SavePatientAlert", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @class = "form-inline", @id = "frm-save-patient-alert" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.PatientId)
    @Html.HiddenFor(model => model.PatientAlertId)
    <div class="form-group form-group-sm font-size-11">
        <div class="row">
            <div class="col-md-4">
                @Html.LabelFor(model => model.StartDate, htmlAttributes: new { @class = "control-label" })
                @Html.EditorFor(model => model.StartDate, new { htmlAttributes = new { @class = "form-control date-picker", @Value = @startDate } })
            </div>
            <div class="col-md-4">
                @Html.LabelFor(model => model.EndDate, htmlAttributes: new { @class = "control-label" })
                @Html.EditorFor(model => model.EndDate, new { htmlAttributes = new { @class = "form-control date-picker", @Value = @endDate, @autocomplete = "off" } })
            </div>
            <div class="col-md-2" style="margin-top:15px;">
                <span>Active </span>
                @Html.CheckBoxFor(model => model.IsActive)
            </div>
        </div>
        <br />
        <div class="row">
            <div class="col-md-8">
                @Html.LabelFor(model => model.AlertMessage, htmlAttributes: new { @class = "control-label" })
                @Html.TextAreaFor(model => model.AlertMessage, new { @class = "specific-textarea", @rows = 7, @maxlength = 250, @placeholder = "Enter Alert Message (max 250 characters)..." })
            </div>
            <div class="col-md-12">
                <button type="submit" id="btn-save-patient-alert" style="margin-right:5px;" class="btn btn-primary btn-xs">@btnSaveAlertText</button>
                <button type="button" id="btn-clear-patient-alert" style="margin-right:5px;" class="btn btn-default btn-xs">Clear Inputs</button>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <span id="span-error-patient-alert" class="text-danger"></span>
            </div>
        </div>
    </div>
}
<br />   