@model Cerebrum.ViewModels.Schedule.VMCCDoctorsMain
<style>
    .cc-doc-textbox{
        width:200px;
        margin-right:10px;
    }
</style>


 <div id="ccdoctors-no-form">

    <div class="form-inline">
        <div class="form-group form-group-sm">
            @Html.Label("Doctor", htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-4">
                @Html.AntiForgeryToken()
                @Html.Hidden("ExternalDoctorId", 0)
                @Html.Hidden("ExternalDoctorLocationId", 0)
                @Html.Hidden("ExternalDoctorAddressId", 0)
                @Html.Hidden("ExternalDoctorPhoneNumberId", 0)
                @Html.TextBox("DoctorFullName", "", htmlAttributes: new { @class = "form-control cc-doc-textbox" })
            </div>
            <div class="col-md-2">
                <button style="margin-left:15px;" data-patientrecord-id="@Model.PatientId" data-patient-name="@Model.PatientFullName" data-app-id="@Model.AppointmentId" data-external-doctor-id="0" data-token="" data-url="@Url.Action("AddCCDoctor","Appointments",new { area="schedule" })" id="btn-add-cc-doctor" type="button" class="btn btn-default btn-xs">Add Doctor</button>
            </div>
            <div class="col-md-4">

            </div>
        </div>
    </div>
</div>

<div id="ccdoctorsList-container" data-url="@Url.Action("GetPatientCCDoctors", "Appointments", new { area = "schedule", patientId = Model.PatientId, appointmentId = Model.AppointmentId })">
    @Html.RenderPartialAsync("_ccDoctorsList", Model.CCDoctors);}
</div>


<script type="text/javascript">
    $(function () {
        loadPatientccDoctorsElement("#ccdoctors-no-form #DoctorFullName", "#ccdoctors-no-form #ExternalDoctorId");
    });
</script>