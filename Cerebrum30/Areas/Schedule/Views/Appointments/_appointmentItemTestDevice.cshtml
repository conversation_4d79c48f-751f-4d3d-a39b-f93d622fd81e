﻿@model Cerebrum.ViewModels.Schedule.VMAppointmentItemTest

@if (Model.RequireDevice)
{
    <div class="device-popover">
        <div class="btn-popover-container">
            <span class="glyphicon glyphicon-phone popover-btn c-pointer"></span>
            <div class="btn-popover-title">
                <span class="default-text-color">Patient Device</span>
            </div>
            <div class="btn-popover-content">
                @using (Html.BeginForm("AssignDevice", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @class = "frm-test-device default-text-color", @id = "frm-add-assign-device" }))
                {
                    @*<form class="frm-test-device default-text-color" data-app-id="@Model.AppointmentId" method="post" role="form" action="">*@
                    @Html.AntiForgeryToken()
                    @Html.HiddenFor(model => Model.AppointmentTestId)
                    @Html.HiddenFor(model => model.AppointmentId)
                    @Html.HiddenFor(model => model.TestId)
                    @Html.HiddenFor(model => model.RequireDevice)
                    @Html.HiddenFor(model => model.DeviceNumberHid)
                    @Html.HiddenFor(model => model.isDDclick)
                    @Html.HiddenFor(model => model.btnName)

                    <div class="form-horizontal">
                        <div class="msg-div text-danger">

                        </div>

                        <div class="form-group form-group-sm">
                            <label class="control-label col-md-3">Device</label>
                            <div class="col-md-8">
                                @Html.EditorFor(model => model.DeviceNumber, new { htmlAttributes = new { @class = "form-control custom-input-sm device-auto-fill" } })
                            </div>
                        </div>


                        <div class="form-group form-group-sm">
                            <label class="control-label col-md-3">Tech </label>
                            <div class="col-md-8">
                                @Html.DropDownListFor(model => Model.officeTechId, new SelectList(Model.TestResources, "UserId", "UserFullName", Model.officeTechId), new { @class = "form-control" })
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <div class="col-md-3"></div>
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary btn-xs" id="AssignDeviceId" name="Assign" value="Assign">Assign</button>
                                <button type="submit" class="btn btn-primary btn-xs" id="DeassignDeviceId"  name="Deassign" value="Deassign">Deassign</button>
                                <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
                            </div>
                        </div>
                        <div class="form-group form-group-sm">
                            <span id="messageId">@Html.LabelFor(x => x.message, @Model.message ?? "")</span>
                        </div>
                    </div>
                    @*</form>*@
                }

            </div>
        </div>
    </div>

}