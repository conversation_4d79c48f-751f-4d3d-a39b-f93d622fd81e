﻿@model Cerebrum.ViewModels.Schedule.VMSchedulePrintSearch

@using (Html.BeginForm("scheduleprintsearch", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @class = "form-inline", @id = "frm-scheduleprint-search" }))
{
    @Html.AntiForgeryToken()    
    @Html.HiddenFor(model => model.CurrentSortOrder)
    @Html.HiddenFor(model => model.SortPatientName)
    @Html.HiddenFor(model => model.SortDOB)
    @Html.HiddenFor(model => model.SortMRN)
    @Html.HiddenFor(model => model.SortDoctor)
    @Html.HiddenFor(model => model.SortAppType)
    @Html.HiddenFor(model => model.SortDate)
    @Html.HiddenFor(model => model.SortOffice)
    @Html.HiddenFor(model => model.Page)

    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.Date, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.Date, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.OfficeId, htmlAttributes: new { @class = "control-label" })
        @Html.DropDownList("OfficeId", new SelectList(ViewBag.Offices, "Id", "Name", Model.OfficeId), new { @class = "form-control" })
    </div>

   <div class="form-group form-group-sm">
        <label class="control-label" for="DoctorId">Doctor(s)</label>
        @Html.DropDownList("DoctorId", new SelectList(ViewBag.PracticeDoctors, "PracticeDoctorId", "FullName", Model.DoctorId), "All", new { @class = "form-control" })
    </div>

        <button id="sch-printsearch-submit" type="submit" style="margin-right:5px;" class="btn btn-default btn-xs">Search</button>

        }
        <div>
            @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
        </div>
