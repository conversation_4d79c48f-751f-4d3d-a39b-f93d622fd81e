@model IEnumerable<Cerebrum.ViewModels.Schedule.VMAppointment>
@Html.ModalHeader("Appointments")
<div class="modal-body content-height500">
    <div class="panel panel-info content-height300">
        <div class="panel-heading">
            <h3 class="panel-title"> Total <span class="badge cbadge">@Model.Count()</span></h3>
        </div>
        <table class="table">
            <tr>
                <th>
                    @Html.DisplayNameFor(model => model.AppointmentDate)
                </th>

                <th>
                    @Html.DisplayNameFor(model => model.AppointmentType)
                </th>
               
                <th>
                    @Html.DisplayNameFor(model => model.PracticeDoctor)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Office)
                </th>

                <th>
                    @Html.DisplayNameFor(model => model.Notes)
                </th>

                <th>
                    @Html.DisplayNameFor(model => model.ReferralDoctor)
                </th>

                <th>
                    @Html.DisplayNameFor(model => model.Tests) 
                </th>

                <th>
                    @Html.Label("Cons")
                </th>

                <th>
                    @Html.Label("Diag")
                </th>

                <th class="@Html.DisplayNameFor(model => model.AppointmentStatus).ToString().ToLower()">
                    @Html.DisplayNameFor(model => model.AppointmentStatus)
                </th>

                <th>
                    @Html.Label("Letter")
                </th>
               
            </tr>

            @foreach (var item in Model.OrderByDescending(o => o.AppointmentDate))
            {
                <tr>
                    <td>          
                        <a  role="button" class="btn-link appointment-edit"  data-modal-url="/Schedule/appointments/edit?appointmentId=@item.Id"> <u> @item.AppointmentDate @item.AppointmentTime </u></a> 
                       <br />
                        @Html.ActionLink("Day Sheet", "index", new { area = "Schedule", controller = "daysheet", OfficeId = item.OfficeId, ScheduleTypeId = 3, Date = item.AppointmentDate }, new { @class = "btn-link" , @style = "text-decoration: underline;" })
                    
                        &nbsp;&nbsp;
                        <a class="btn-link" href="/Requisition?PatientRecordId=@item.PatientId&PracticeDoctorId=@item.PracticeDoctorId&officeId=@item.OfficeId&AppointmentId=@item.Id"> <u>Flow Sheet</u></a>   
                    
                    </td>

                    <td>@item.AppointmentType</td>
                    
                    <td>@item.PracticeDoctor</td>
                    <td>@item.Office</td>
                    <td>@item.Notes</td>
                    <td>@item.ReferralDoctor</td>
                    <td>@item.RequestedTest</td>
                    <td>@item.ConsultCode</td>                    
                    <td>@item.DiagnoseCode</td>
                    <td style="text-align:left" class="@item.AppointmentStatus">@item.AppointmentStatus</td>
                    <td>@if (!string.IsNullOrWhiteSpace(item.LetterURL))
                        {
                            <a class="btn " href="@item.LetterURL">Letter</a>
                        }</td>                  
                </tr>
            }
        </table>
    </div>
</div>
@{
    if (Model != null && Model.Count() > 0)
    {
        var firstitem = Model.First();
        @*<label style="margin-left: 15px;"> Patient Name : </label>  <label> @firstitem.PatientFullName</label>*@
        <input type="hidden" id="hdn_name" value="@firstitem.PatientFullName" />
    }
}

<script>
    var formatName = function (arg) {
        var formattedName = arg;
        if (arg.indexOf(',') != -1) {
            var arr = arg.split(',');
            formattedName = arr[0].toUpperCase() + ', ' + arr[1];
        }

        return formattedName;

    }

    $(document).ready(function () {
        var modalTitle = 'Appointments';
        if ($('#hdn_name').val().length > 0) {
            modalTitle = modalTitle + ' for ' + formatName($('#hdn_name').val());
        }
        $('#patientAppsModal .modal-title').html(modalTitle);
    });
</script>
@Html.ModalFooter(isInfoModal: true)