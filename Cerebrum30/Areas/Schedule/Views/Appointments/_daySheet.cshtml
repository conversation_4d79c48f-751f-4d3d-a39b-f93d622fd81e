@model Cerebrum.ViewModels.Schedule.VMDaySheet
@{ 
    var filterAppStatuses = Model.AppointmentStatuses.Where(s => (s.Text.ToLower() != "waitlist" && s.Text.ToLower() != "cancellationlist")).ToList();
    var cReturnUrl = Html.GetReturnUrl();
}
<script src="~/Scripts/cerebrum3-healthcard.js"></script>

<div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <h3 class="lbl-datestamp">@String.Format("{0:dddd, MMMM d, yyyy}", Model.SelectedDate) <small><span class="lbl-appointments highlight">@*Total Appointments (@Model.Appointments.Count())*@</span></small></h3>       
    </div>    
     <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">     
         <div id="ph_msg" style="display: none; text-align: center;"></div>
     </div>
</div>
    <script type="text/javascript">
    $(document).ready(function () {   //display the below container in '_daysheet' view instead   
        var clone = $('._toClone').clone();
        clone.show().appendTo($("#_placeHolder9"));  
    });
    </script>

    <div class="_toClone text-right" style="display: none"><!-- text-right nav-side-sub-right -->
        @*<a class="btn btn-default btn-sm btn-add-ext-doctor"><span class="glyphicon glyphicon-user default-text-color"> </span> Add Doctor</a>*@
        <a class="btn btn-default btn-sm btn-add-patient"><span class="glyphicon glyphicon-user default-text-color"> </span> Add Patient</a>
        @*<a class="btn btn-default btn-sm btn-edit-patient"><span class="glyphicon glyphicon-user default-text-color"> </span> Edit Patient(14)</a>*@
        <a class="btn btn-default btn-sm btn-schedule-print" data-modal-url="@Url.Action("GetSchedulePrint", "Appointments", new { area = "schedule", Date = Model.SelectedDate.ToShortDateString(), OfficeId = Model.Office.Id })"><span class="glyphicon glyphicon-print default-text-color"> </span> Print</a>
        <a class="btn btn-default btn-sm btn-view-swipehealthcard" data-modal-url="@Url.Action("GetSwipeHealthcard", "Appointments", new { area = "schedule" })"><span class="glyphicon glyphicon-credit-card default-text-color"> </span> Swipe Card</a>
        @if (CerebrumUser.HasPermission("UploadReports"))
        {
            <a class="btn btn-default btn-sm" href="@Url.Action("assignment", "externaldocument", new { area = "externaldocument", externaldocument = "fax", officeId = Model.Office.Id, practiceId = Model.Office.PracticeId })">
                <span class="glyphicon glyphicon-folder-open default-text-color"> </span>&nbsp; Upload Reports</a>
        }
        @if (CerebrumUser.HasPermission("Billing,BillingAdmin"))
        {
            string unbillButtonDisplay = "none";
            if (Model.DaySheetBilled)
            {
                unbillButtonDisplay = "inline-block";
            }
            <a class="btn btn-default btn-sm btn-view-prebill-office" data-bill-date="@Model.SelectedDate.ToString("MM/dd/yyyy", System.Globalization.CultureInfo.InvariantCulture)" data-bill-office-id="@Model.Office.Id"><span class="glyphicon glyphicon-usd default-text-color"> </span> Bill</a>
            <a class="btn btn-default btn-sm btn-view-unbill-office" style="display: @unbillButtonDisplay;" data-bill-date="@Model.SelectedDate.ToString("MM/dd/yyyy", System.Globalization.CultureInfo.InvariantCulture)" data-bill-office-id="@Model.Office.Id"><span class="glyphicon glyphicon-usd default-text-color"> </span> UnBill</a>
        }
    </div>


<div class="ds-filter-container">@*Filters*@   
    <div class="row">
        <div class="col-md-12">           
                <div class="lbl-filter __00956 col-lg-1 col-md-1 col-sm-12">Filters</div>
                <div class="form-inline        col-lg-9 col-md-11 col-sm-12">
                    @using (Html.BeginForm("getdaysheetappointments", "appointments", new { area = "schedule" }, FormMethod.Get, true, new { @id = "frm-day-filters" }))
                    {
                        <div class="form-group form-group-sm">
                            @Html.Hidden("OfficeId", Model.Office.Id)
                            @Html.Hidden("ScheduleTypeId", Model.ScheduleType.Id)
                            @Html.Hidden("Date", Model.SelectedDate.ToShortDateString())
                            @Html.Hidden("FilterPatient", Model.Request.FilterPatient)
                            @Html.Hidden("PageSize", Model.Request.PageSize)
                            
                            <label class="control-label" for="DoctorId">Doctor(s)</label>
                            @Html.DropDownList("DoctorId", new SelectList(Model.PracticeDoctors, "PracticeDoctorId", "LastFirstName", Model.Request.DoctorId), "All", new { @class = "form-control" })
                            <a href="#" style="margin-right:5px;" data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Doctor's Schedule" class="btn-view-docschedule" data-modal-url="@Url.Action("GetDoctorSchedule", "appointments", new { area = "schedule" })"><span class="glyphicon glyphicon-sunglasses"></span></a>
                            <label class="control-label" for="TechId">Technician(s)</label>
                            @Html.DropDownList("TechId", new SelectList(Model.OfficeTechs, "Value", "Text", Model.Request.TechId), new { @class = "form-control" })
                            <label class="control-label" for="TestId">Test Type(s)</label>
                            @Html.DropDownList("TestId", new SelectList(Model.TestFilters, "Value", "Text", Model.Request.TestId), new { @class = "form-control" })
                            <label class="control-label" for="AppointmentStatusId">Status</label>
                            @Html.DropDownList("AppointmentStatusId", new SelectList(filterAppStatuses, "Id", "Description", Model.Request.AppointmentStatusId), "All", new { @class = "form-control" })
                            <div class="checkbox">
                                <label>                                   
                                    @Html.CheckBox("Expected",Model.Request.Expected) Show expected
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>                                   
                                    @Html.CheckBox("ShowOrders", Model.Request.ShowOrders) Show orders
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    @*@Html.CheckBoxFor(model => model.ExcludeTestOnly) Exclude test only*@
                                    @Html.CheckBox("ExcludeTestOnly", Model.Request.ExcludeTestOnly) Exclude test only
                                </label>
                            </div>

                            <div class="checkbox">
                                <label>
                                    @*@Html.CheckBoxFor(model => model.ExcludeCancelled) Exclude Cancelled*@
                                    @Html.CheckBox("ExcludeCancelled", Model.Request.ExcludeCancelled) Exclude Cancelled
                                </label>
                            </div>

                            @*<button class="btn btn-default btn-sm btn-spacing">Filter</button>*@
                        </div>

                    }                    
                </div>
                
                <div class="form-inline col-lg-2 col-md-12 col-sm-12  filter-by-patient "> 
                    <div class="form-group-sm pull-right"> <!-- pull-right | pull-left -->
                        @*<label class="control-label" for="TechId">Technician</label>*@
                        <input type="text" class="form-control" id="ds-patient-filter" name="ds-patient-filter" value="@Model.Request.FilterPatient" placeholder="Filter by patient" />
                    </div>
                </div>                 
                  
                       
            @*</div>*@
        </div>
        @*<div class="col-md-4 text-right">
        <a class="btn btn-default btn-sm btn-view-swipehealthcard btn-spacing" data-modal-url="@Url.Action("GetSwipeHealthcard", "Appointments", new { area="schedule" })"><span class="glyphicon glyphicon-credit-card default-text-color"> </span> Swipe Card</a>
        <a class="btn btn-default btn-sm" href="@Url.Action("assignment", "externaldocument", new { area="externaldocument", externaldocument= "fax", officeId=Model.Office.Id })"><span class="glyphicon glyphicon-folder-open default-text-color"> </span> Upload Reports</a>
    </div>*@
    </div>
 </div>

<div id="daysheet-wrapper" data-selected-date="@Model.SelectedDate.ToShortDateString()">

    @Html.RenderPartialAsync("_appointmentList", Model.Appointments);}

</div>

<div id="app-confirmations" class="hidden">
    <ul class="ul-appstatus">
        @foreach (var item in Model.AppointmentConfirmations)
        {
            <li class="app-status-item" data-appconfirm-desc="@item.Description" data-appconfirm-color="@item.Color" data-appconfirm-id="@item.Id">                
                <div>@item.Description</div>
            </li>
        }

    </ul>
    <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
</div>

<div id="app-statuses" class="hidden">
    <ul class="ul-appstatus">
        @*@foreach (var item in Model.AppointmentStatuses)*@
        @foreach (var item in filterAppStatuses)        
        {
            <li class="app-status-item" data-appstatus-desc="@item.Description" data-appstatus-color="@item.Color" data-appstatus-id="@item.Id">
                <div class="pull-left legend-color-container @item.Color"></div>
                <div>@item.Description</div>
            </li>
        }

    </ul>
    <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>
</div>

<div id="app-test-statuses" class="hidden">
    <ul class="ul-appstatus">
        @foreach (var item in Model.TestStatuses)
        {
            <li class="app-status-item" data-appstatus-desc="@item.Status" data-appstatus-color="@item.Color" data-appstatus-id="@item.Id">
                <div class="pull-left legend-color-container @item.Color"></div>
                <div>@item.Status</div>
            </li>
        }

    </ul>
    @if (CerebrumUser.HasRole("PracticeAdmin") || CerebrumUser.HasRole("Admin"))
    {
        <button type="button" class="btn btn-danger btn-xs app-test-remove">Remove</button>
    }
    <button type="button" class="btn btn-default btn-xs btn-popover-close">Close</button>    
</div>

<div id="practice-tests" class="hidden">
    <ul class="ul-appstatus">
        @foreach (var item in Model.PracticeTests.Where(t=> t.Name.ToLower() != "vp"))
        {
            <li class="app-status-item" data-test-id="@item.TestId">
                <div>@item.Name</div>
            </li>
        }

    </ul>
    <button type="button" class="btn btn-default btn-xs btn-popover-close _66765">Close</button>
</div>

<div id="app-pay-methods" class="hidden">
    <ul class="ul-appstatus">
        @foreach (var item in Model.PaymentMethods)
        {
            <li class="app-status-item" data-pay-desc="@item.Name" data-pmethod-id="@item.Id">
                <div>@item.Name</div>
            </li>
        }

    </ul>
    <button type="button" class="btn btn-default btn-xs btn-popover-close _9980">Close</button>
</div>

<div id="office-techs" class="hidden">
    @Html.DropDownList("lk-office-tech", new SelectList(Model.OfficeTechs, "Value", "Text"), new { @class = "form-control" })
</div>

<div class="modal fade" id="billing-message-modal" role="dialog">
    <div class="modal-dialog" style="width: 900px;">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Message</h4>
            </div>
            <div class="modal-body">
                <div id="billing-message-modal-header"></div>
                <div class="modal-body" id="billing-message-modal-body" style="overflow-y: scroll; height:320px;"></div>
            </div>
            <div class="modal-footer">
                <div class="col-md-8" id="billing-message-modal-footer"></div>
                <div class="col-md-4">
                    <div id="billing-message-modal-continue-buttons">
                        <button class="btn btn-default btn-sm btn-view-bill-office" data-dismiss="modal" style="width: 64px;" data-bill-date="@Model.SelectedDate.ToString("MM/dd/yyyy", System.Globalization.CultureInfo.InvariantCulture)" data-bill-office-id="@Model.Office.Id">Yes</button>
                        <button class="btn btn-default btn-sm" data-dismiss="modal" style="margin-left: 80px; width: 64px;">No</button>
                    </div>
                    <div id="billing-message-modal-close-button">
                        <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<div id="requisitionStatusDialog" name="requisitionStatusDialog" style="display:none; margin-top: 8px; ">
    @foreach (var requisitionStatus in Model.requisitionStatus.OrderBy(status => status.SortOrder).ToList())
    {
        <div class="row" style="padding-top: 8px;">
            <div class="col-sm-2 text-right"><input type="radio" id="requisitionStatus" name="requisitionStatus" value="@requisitionStatus.Id"></div>
            <div class="col-sm-6">@requisitionStatus.Name</div>
            <div class="col-sm-2 text-left" style="background-color: @requisitionStatus.Color; height: 16px; width: 8px;"></div>
        </div>
    }
    <div class="row" style="padding-top: 18px;">
        <div class="col-sm-2 text-right"></div>
        <div class="col-sm-4">Test Date</div>
        <div class="col-sm-6 text-left"><input type="text" id="requisitionTestDate" name="requisitionTestDate" value="" style="width: 128px;"></div>
    </div>
    <div class="row" style="padding: 24px 12px 0 12px;">
        <div class="col-sm-4">Comment</div>
    </div>
    <div class="row" style="padding: 4px 4px 0 12px;">
        <div class="col-sm-12">
            <textarea rows="2" style="width: 100%; resize: vertical;" maxlength="3000" id="requisitionComment" name="requisitionComment"></textarea>
        </div>
    </div>
</div>