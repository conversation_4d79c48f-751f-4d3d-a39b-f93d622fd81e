@model Cerebrum.ViewModels.Schedule.VMPatientAppointmentPage

<table class="table">
    <tr>
    <tr>
        <th>#</th>
        <th>
            Date
        </th>

        <th>
            Appointment Type
        </th>

        <th>
            Doctor
        </th>
        <th>
            Office
        </th>

        <th>
            Notes
        </th>

        <th>
            Referral Doctor
        </th>

        <th>
            Tests
        </th>

        <th>
            Consult Code
        </th>

        <th>
            Diagnose Code
        </th>

        <th>
            Status
        </th>

        <th>
            Letter URL
        </th>

    </tr>
    @{
        var count = 1;
    }
    @foreach (var item in Model.Appointments)
    {
        <tr>
            @{
                var page = Model.PageNum == 1 ? (count++) : (Model.PageSize * (Model.PageNum - 1)) + (count++);
            }
            <td>@(page)</td>
            <td>
                <a role="button" class="btn-link appointment-edit" data-modal-url="/Schedule/appointments/edit?appointmentId=@item.Id"> <u> @item.AppointmentDate @item.AppointmentTime </u></a> 
                <br />
                @Html.ActionLink("Day Sheet", "index", new { area = "Schedule", controller = "daysheet", OfficeId = item.OfficeId, ScheduleTypeId = 3, Date = item.AppointmentDate }, new { @class = "btn-link", @style = "text-decoration: underline;" })

                &nbsp;&nbsp;
                <a class="btn-link" href="/Requisition?PatientRecordId=@item.PatientId&PracticeDoctorId=@item.PracticeDoctorId&officeId=@item.OfficeId&AppointmentId=@item.Id"> <u>Flow Sheet</u></a>

            </td>

            <td>@item.AppointmentType</td>

            <td>@item.PracticeDoctor</td>
            <td>@item.Office</td>
            <td>@item.Notes</td>
            <td>@item.ReferralDoctor</td>
            <td>@item.RequestedTest</td>
            <td>@item.ConsultCode</td>
            <td>@item.DiagnoseCode</td>
            <td style="text-align:left" class="@item.AppointmentStatus">@item.AppointmentStatus</td>
            <td>
                @if (!string.IsNullOrWhiteSpace(item.LetterURL))
                {
                    <a class="btn " href="@item.LetterURL">Letter</a>
                }
            </td>
        </tr>
                }

</table>
