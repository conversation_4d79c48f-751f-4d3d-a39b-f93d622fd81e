@model Cerebrum.ViewModels.Schedule.VMSchedule

@{
    ViewBag.Title = "Appointments";
    Layout = "~/Areas/Schedule/Views/Shared/_LayoutSchedule.cshtml";
}

@if (CerebrumUser.UserOffices.Any())
{
    @(await Html.PartialAsync("_scheduleBar", Model))

    <div id="schedule-container">
        @if (Model.Office != null)
        {
            if (Model.Office.HasSchedule)
            {
                @(await Html.PartialAsync("_schedule", Model))
            }
            else
            {
                @(await Html.PartialAsync("_noSchedule"))
            }
        }
        else
        {
            @(await Html.PartialAsync("_noOffice"))
        }

    </div> @*schedule container end*@
}
else
{
    @(await Html.PartialAsync("_noUserOffice"))
}

@*<div class="row row-top-padding row-bottom-padding">
    <div class="col-md-12">
        <div class="form-inline">
            @using (Html.BeginForm("index", "appointments", new { area = "schedule" }, FormMethod.Get, new { @id = "frm-office-date" }))
            {
                var officeId = Model.Office != null ? Model.Office.Id : 0;
                <div class="form-group form-group-sm">
                    <label class="control-label" for="OfficeId">Office</label>
                    @Html.DropDownList("OfficeId", new SelectList(Model.Offices, "Id", "Name", officeId), "Select an office", new { @class = "form-control" })
                    @Html.ValidationMessage("OfficeId", new { @class = "text-danger" })
                </div>
                <div class="form-group form-group-sm">

                    @Html.Hidden("ScheduleTypeId", Model.ScheduleType.Id)
                    <label class="control-label" for="Date">Date</label>
                    @Html.TextBox("Date", String.Format("{0:MM/dd/yyyy}", Model.SelectedDate), new { @class = "form-control date-picker input-sm" })

                    <button class="btn btn-default btn-sm office-schedule btn-spacing"><span class="glyphicon glyphicon-refresh text-primary"></span> Refresh</button>
                </div>

            }

        </div>

    </div>
</div>*@