@model Cerebrum.ViewModels.Schedule.VMCommentsMain

@Html.ModalHeader("Comments for "+Model.PatientFullName)

<div class="modal-body">
    <div id="comment-search">
        @(await Html.PartialAsync("_commentSearch",Model.CommentSearch))
    </div>
    @*@if(CerebrumUser.HasPermission("OtherActions", "SaveDoctorComments"))
    {*@ 
        <hr/>
        <div id="comment-new">
            @(await Html.PartialAsync("_commentAdd", Model.CommentAdd))
        </div>
    @*}*@
    <div id="commentList-container">
        @(await Html.PartialAsync("_commentList",Model.Comments))
    </div>
</div>

@Html.ModalFooter(isInfoModal: true)