@model Cerebrum.ViewModels.Schedule.VMAppointment


@using (Html.BeginForm("edit", "appointments", new { area = "schedule" }, FormMethod.Post, true, new { @id = "frm-appointment-edit", @class = "sch-app-form" }))
{
    @Html.ModalHeader("Edit Appointment for " + Model.PatientFullName)
    <div class="modal-body">
        <blockquote>
            <p class="text-danger">
                This past appointment is imported, cannot be modified.
            </p>
        </blockquote>

    </div>
    @Html.ModalFooter(isInfoModal: true)
}
