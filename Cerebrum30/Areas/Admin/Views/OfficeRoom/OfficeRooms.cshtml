@model IEnumerable<Cerebrum.ViewModels.Schedule.VMOfficeRoom>

@if (Model != null && Model.Any())
{
    <table class="table">
        <tr>
            <th></th>
            <th>
                @Html.DisplayNameFor(model => model.roomType)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.OfficeName)
            </th>

        </tr>

        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @Html.ActionLink("Delete", "Delete", new { id = item.Id }, new { @class = "btn-delete-office-room", data_id = item.Id })
                </td>
                <td>
                    @item.roomType
                </td>
                <td>
                    @item.OfficeName
                </td>

            </tr>
        }

    </table>
}