﻿@model IEnumerable<Cerebrum.ViewModels.Schedule.VMOfficeRoom>

@{
    ViewBag.Title = "Office Rooms";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}

<h2>Office Rooms</h2>

<div class="form-group form-inline form-group-sm">
    <label class="control-label" for="OfficeId">Office</label>
    @Html.DropDownList("OffId", new SelectList(ViewBag.Offices, "Id", "Name"), new { @class = "form-control office-selection", data_url="" })
    <button id="btn-view-rooms" data-url="@Url.Action("OfficeRooms","OfficeRoom",new { area="admin" })" type="button" style="margin-right:5px;" class="btn btn-default btn-xs">View Rooms</button>    
</div>

<div class="panel panel-default">
    <div class="panel-heading">New Office Room </div>
    <div class="panel-body form-inline">

        @using (Html.BeginForm("Create", "OfficeRoom", new { area = "admin" }, FormMethod.Post, true, new { @id = "frm-office-room-create" }))
        {

            @Html.AntiForgeryToken()
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            <div class="form-group form-inline form-group-sm">
                <label class="control-label" for="OfficeId">Office</label>
                @Html.DropDownList("OfficeId", new SelectList(ViewBag.Offices, "Id", "Name"), new { @class = "form-control office-selection", data_url = "" })
                <label class="control-label" for="OfficeId">Room Type</label>
                @Html.DropDownList("roomTypeId", new SelectList(ViewBag.OfficeRoomTypeItems, "Id", "Type"), "Choose One", new { @class = "form-control office-selection", data_url = "" })
            </div>
            <div class="form-group">
                <br />
                <div class="col-md-offset-2 col-md-3">
                    <input type="submit" value="Add" class="btn btn-default btn-office-room-type" />
                </div>
            </div>
        }
    </div>
</div>

<div id="room-list">
    @(await Html.PartialAsync("OfficeRooms", Model));
</div>
<script type="text/javascript">
    $(function () {
        $(document).on("click", "#btn-view-rooms", function () {
            var url = $(this).data('url');
            var officeId = $("#OffId").val();
            $.ajax({
                url: url, type: "GET", data: { id: officeId },
                success: function (result) {
                    $("#room-list").html(result);
                }

            })
        });
        $(document).on("click", ".btn-delete-office-room", function (e) {
            e.preventDefault();
            var deurl=$(this).attr("href");
            var dataid = $(this).data("id");

            ajaxCall(deurl, { id: dataid }, false, function (result) {
                location.reload();
            });
        });
        $(document).on('submit', '#frm-office-room-create', function (e) {
            e.preventDefault();
            var officeid = $("#OfficeId").val();
            var roomTypeId = $("#roomTypeId").val();

            $('.btn-office-room-type').prop('disable', true);

            ajaxCall(this.action, { officeid: officeid, roomTypeId: roomTypeId }, false, function (result) {
                location.reload();
            });
        });
    });
</script>