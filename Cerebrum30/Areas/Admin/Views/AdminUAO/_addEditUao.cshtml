@model Cerebrum.ViewModels.AdminUAO.VMAdminUao
@using Cerebrum.ViewModels.Practice
@{
    Layout = null;
    var header = "Add new UAO";
    string strChecked = "checked";

    if (Model?.Id > 0)
    {
        header = "Edit UAO";
        if (!Model.IsActive)
        {
            strChecked = string.Empty;
        }
    }

    var uaoTypes = (List<SelectListItem>)ViewBag.UaoTypes;

    List<VMPractice> practices = ((List<VMPractice>)ViewBag.ListOfPractices).Where(p => p.OntarioHealthClientId != null && !p.OntarioHealthClientId.Equals("")).ToList();
    var popoverTextPractice = "List of Practices that have Ontario Health ClientId.<br /> If you want to add another practice here, please update practice providing Ontario Health ClientId";
    List<SelectListItem> availableServices = (List<SelectListItem>)ViewBag.ListOfAvailableServices;
}
<script>
    $(function () {
        $('[data-toggle="popover"]').popover({
            trigger: 'hover',
            html: true,
            container: 'body'
        });
    });
</script>
@Html.ModalHeader(header)
<div class="col-md-12">
    @Html.AntiForgeryToken()
    <input type="hidden" id="Id" name="Id" value="@Model.Id" />
    <table class="table table-condensed table-bordered table-responsive">
        <tr>
            <td>@Html.LabelFor(model => model.UAOName, htmlAttributes: new { @class = "control-label" })</td>
            <td><div class="col-md-8">@Html.EditorFor(model => model.UAOName, new { htmlAttributes = new { @class = "form-control", @maxlength = 40 } })</div></td>
        </tr>
        <tr>
            <td>@Html.LabelFor(model => model.UAO, htmlAttributes: new { @class = "control-label" })</td>
            <td><div class="col-md-8">@Html.EditorFor(model => model.UAO, new { htmlAttributes = new { @class = "form-control", @maxlength = 60 } })</div></td>
        </tr>
        <tr>
            <td>@Html.LabelFor(model => model.LegalName, htmlAttributes: new { @class = "control-label" })</td>
            <td><div class="col-md-8">@Html.EditorFor(model => model.LegalName, new { htmlAttributes = new { @class = "form-control", @maxlength = 200 } })</div></td>
        </tr>
        <tr>
            <td>@Html.LabelFor(model => model.Address1, htmlAttributes: new { @class = "control-label" })</td>
            <td><div class="col-md-8">@Html.EditorFor(model => model.Address1, new { htmlAttributes = new { @class = "form-control", @maxlength = 100 } })</div></td>
        </tr>
        <tr>
            <td>@Html.LabelFor(model => model.Address2, htmlAttributes: new { @class = "control-label" })</td>
            <td><div class="col-md-8">@Html.EditorFor(model => model.Address2, new { htmlAttributes = new { @class = "form-control", @maxlength = 100 } })</div></td>
        </tr>
        <tr>
            <td>@Html.LabelFor(model => model.City, htmlAttributes: new { @class = "control-label" })</td>
            <td><div class="col-md-8">@Html.EditorFor(model => model.City, new { htmlAttributes = new { @class = "form-control", @maxlength = 100 } })</div></td>
        </tr>
        <tr>
            <td><label class="control-label" title="@Html.DisplayNameFor(model => model.Province)">@Html.DisplayNameFor(model => model.Province)</label></td>
            <td>
                <div class="col-md-4">@Html.DropDownListFor(model => model.Province, new SelectList(ViewBag.LKProvinces, "Value", "Text", Model.Province), new { @class = "form-control" })</div>
            </td>
        </tr>
        <tr>
            <td>@Html.LabelFor(model => model.PostalCode, htmlAttributes: new { @class = "control-label" })</td>
            <td><div class="col-md-4">@Html.EditorFor(model => model.PostalCode, new { htmlAttributes = new { @class = "form-control", @maxlength = 100 } })</div></td>
        </tr>
        <tr>
            <td>@Html.LabelFor(model => model.Phone, htmlAttributes: new { @class = "control-label" })</td>
            <td><div class="col-md-4">@Html.EditorFor(model => model.Phone, new { htmlAttributes = new { @class = "form-control", @maxlength = 20 } })</div></td>
        </tr>
        <tr>
            <td>@Html.Label("UAO Type", htmlAttributes: new { @class = "control-label" })</td>
            <td>
                <div class="col-md-8">
                    <select class="form-control" id="UAOTypeId" name="UAOTypeId">
                        <option value="">Choose One</option>
                        @foreach (var item in uaoTypes)
                        {
                            string selectedOption = "";

                            if (item.Value == Model.UAOTypeId.ToString())
                            {
                                selectedOption = " selected";
                            }
                            <option value="@item.Value" selected="@(selectedOption == " selected")">@item.Text</option>
                        }
                    </select>
                </div>
            </td>
        </tr>
        <tr>
            <td>@Html.Label("Practice", htmlAttributes: new { @class = "control-label" })</td>
            <td>
                <div class="col-md-8">
                    <select class="form-control" id="UaoPractice" name="UaoPractice">
                        <option value="0">Select Practice</option>
                        @foreach (var item in practices)
                        {
                            string selectedOption = "";

                            if (item.Id == Model.PracticeId)
                            {
                                selectedOption = " selected";
                            }
                            <option value="@item.Id" selected="@(selectedOption == " selected")">@item.PracticeName</option>
                        }
                    </select>
                </div>
                <div class='glyphicon glyphicon-info-sign c-pointer' style="padding-right:0px;margin-right:0px;" data-placement='right' data-toggle="popover" title="<b>Information</b>"
                     data-content="@popoverTextPractice"></div>
            </td>
        </tr>
        <tr>
            <td>@Html.LabelFor(model => model.IsActive)</td>
            <td><div class="col-md-8"><input type="checkbox" id="IsActive" name="IsActive" @strChecked></div></td>
        </tr>
        <tr>
            <td><label for="SelectedServices">Services</label></td>
            <td>
                @foreach (var item in availableServices)
                {


                <div class="col-md-2">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" 
                                   name="SelectedServices" 
                                   @if (Model.SelectedServiceIds.Contains(Convert.ToInt32(item.Value)))
                                   {
                                       @: checked 
                                   }
                                   value="@item.Value" />@item.Text
                        </label>
                    </div>
                </div>

                }

            </td>
        </tr>
    </table>
    <div class='text-danger' id="div-error-add-edit-uao-admin"></div>
</div>
<hr />
<br />
<div class="modal-footer">
    <button type="button" class="btn btn-default btn-sm" id="btn-save-uao-admin">Save</button>
    <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Close</button>
</div>
