@model List<Cerebrum.ViewModels.AdminUAO.VMAdminUao>
@{
    int i = 1;
}
<script>
    $(document).ready(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
<table class="table table-condensed table-bordered table-responsive">
    <thead>
        <tr>
            <th class="text-center">#</th>
            <th>UAO Name</th>
            <th>UAO Value</th>
            <th>Legal Name</th>
            <th>Address</th>
            <th>UAO Type</th>
            <th>Practice</th>
            <th class="text-center">Active</th>
            <th>Created Date</th>
            <th>Updated Date</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var item in Model)
        {
            string strChecked = string.Empty;
            string strDateUpdated = string.Empty;
            string address = item.Address1;
            string country = "Canada";

            if (!string.IsNullOrEmpty(item.Address2))
            {
                address = address + " " + item.Address2;
            }
            if (item.Province.ToString().Substring(0, 2) != "CA")
            {
                country = "USA";
            }
            if (address.Length > 0)
            {
                address = address + ", " + item.City + ", " + item.PostalCode + ", " + item.Province.ToString().Substring(2, 2) + " " + country;
            }

            if (item.IsActive)
            {
                strChecked = "checked";
            }
            if (item.DateUpdated.HasValue)
            {
                DateTime dateUpdated = ((DateTime)item.DateUpdated);
                strDateUpdated = dateUpdated.ToString("MMM dd, yyyy h:mm tt");
            }
        <tr>
            <td class="text-center">@i</td>
            <td>@item.UAOName</td>
            <td>@item.UAO</td>
            <td>@Html.Truncate(item.LegalName, 30, true, false)</td>
            <td>@Html.Truncate(address, 30, true, false)</td>
            <td>@item.UAOTypeName</td>
            <td>@item.PracticeName</td>
            <td class="text-center">
                <input type="checkbox" onclick="return false;" readonly @strChecked />
            </td>
            <td>@item.DateCreated.ToString("MMM dd, yyyy h:mm tt")</td>
            <td>@strDateUpdated</td>
            <td><input type="button" class="btn-link pull-right btn-admin-edit-uao" value="Edit" data-id="@item.Id"></td>
        </tr>
            i++;
        }
    </tbody>
</table>
