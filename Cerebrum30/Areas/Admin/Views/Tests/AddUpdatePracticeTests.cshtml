﻿<div class="container form-horizontal col-lg-12">
    <h4>Add Test to Practices</h4>
    <p>Press 'Ctrl' button and Click on Practice. </p>
    @using (Html.BeginForm("AddUpdatePracticeTests", "Tests", new { area = "Admin", controller = "Tests" }))
    {
        <input type="hidden" id="testId" value="@ViewBag.testId" />
        <div class="form-group col-sm-10">
            <div class="checkbox col-sm-10">
                @Html.ListBox("practiceTests", new MultiSelectList(ViewBag.Practices, "Id", "name", ViewBag.SelectedPracticeTests, "practices"), new { multiple = "multiple", @class = "multiple", size = 15 })
            </div>
            <br />
            <p>Press 'Save' button to save your selection </p>
        </div>
    }
</div>
