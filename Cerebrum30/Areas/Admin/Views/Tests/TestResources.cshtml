@model IEnumerable<Cerebrum.ViewModels.Test.VMTestResource>

<p>
    @Html.ActionLink("Add New Permission", "AddNewPermission", new { id = int.Parse(ViewBag.TestId + "") })
</p>
@if (Model != null && Model.Count() > 0)
{
    <table class="table">
        <tr>

            <th>
                @Html.DisplayNameFor(model => model.permission)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.testResourceType)
            </th>

            <th>@Html.DisplayNameFor(model => model.nurseRequired)</th>
            <th>@Html.DisplayNameFor(model => model.doctorRequiredInOffice)</th>
            <th>@Html.DisplayNameFor(model => model.isPerformedInGroup)</th>
            <th>@Html.DisplayNameFor(model => model.AssignedAppointmentDoctor)</th>
            <th></th>
        </tr>

        @foreach (var item in Model)
        {
            <tr>

                <td>
                    @item.permission.name
                </td>
                <td>
                    @item.testResourceType.testResourceName
                </td>

                <td>
                    @item.nurseRequired
                </td>
                <td>
                    @item.doctorRequiredInOffice
                </td>
                <td>
                    @item.isPerformedInGroup
                </td>
                <td>
                    @item.AssignedAppointmentDoctor
                </td>
                <td>
                    <button type='button' data-button='{"url": "/admin/tests/DeleteTestResource/", "id": "@item.Id"}'>Delete</button>
                </td>
            </tr>
        }

    </table>
}