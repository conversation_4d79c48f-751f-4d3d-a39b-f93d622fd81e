@model Cerebrum.ViewModels.Test.VMTest
@{
    ViewBag.Title = "Tests/Details (Admin)";
    ViewBag.ModuleName = "Tests/Details (Admin)";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

<style>
    dt, dd {
        /*height: 34px;
        vertical-align: middle !important;*/
    }
</style>

<script>



    $(function () {
        $('button').click(function () {
            var data = $.parseJSON($(this).attr('data-button'));
            $("#dialog-confirm").html('<p><span class="ui-icon ui-icon-alert" style="float:left; margin:12px 12px 20px 0;"></span>These items will be permanently deleted and cannot be recovered.<br /> Are you sure?</p>');
            $("#dialog-confirm").dialog({
                resizable: false,
                height: "auto",
                width: 400,
                modal: true,
                title: 'Delete',
                buttons: {
                    "Delete": function () {
                        $.post(data.url, { id: data.id }, function (data) {
                            location.reload('/admin/tests/');
                        });
                        $(this).dialog("close");
                    },
                    Cancel: function () {
                        $(this).dialog("close");
                    }
                }
            });
        });
    });
</script>
<div style="margin-top:15px"></div>
<h4>Details: @Html.DisplayFor(model => model.testFullName)</h4>
    @*<h4>Details</h4>*@
    <hr />
<div>

    <dl class="dl-horizontal">
        <dt>@Html.DisplayNameFor(model => model.color)</dt> <dd style="color:@Model.color">@Html.DisplayFor(model => model.color)</dd>

        <dt>@Html.DisplayNameFor(model => model.testFullName)</dt> <dd>@Html.DisplayFor(model => model.testFullName)</dd>

        <dt>@Html.DisplayNameFor(model => model.testShortName)</dt> <dd>@Html.DisplayFor(model => model.testShortName)</dd>

        <dt>@Html.DisplayNameFor(model => model.order)</dt> <dd>@Html.DisplayFor(model => model.order)</dd>

        <dt>@Html.DisplayNameFor(model => model.duration)</dt>  <dd>@Html.DisplayFor(model => model.duration)</dd>

        <dt>@Html.DisplayNameFor(model => model.IsRadiology)</dt>  <dd>@Html.DisplayFor(model => model.IsRadiology)</dd>

        @*<dt>&nbsp;</dt>  <dd>@Html.ActionLink("Edit", "Edit", new { id = Model.Id }) | @Html.ActionLink("Back to List", "Index")</dd>*@
    </dl>
</div>

<hr />
<div>@Html.ActionLink("Edit", "Edit" , new { id=Model.Id }) | @Html.ActionLink("Back to List", "Index")</div>
<div>@Html.Action("TestResources", new {areas = "admin", controller = "tests", id = Model.Id })</div>
<div id="dialog-confirm" title="Empty the recycle bin?"></div>