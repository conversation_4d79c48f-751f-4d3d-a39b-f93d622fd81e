@model Cerebrum.ViewModels.Test.VMTestResource

@{
    //ViewBag.Title = "AddNewPermission";
    //Layout = "~/Views/Shared/_Layout.cshtml";
    ViewBag.Title = "Tests/AddNewPermission (Admin)";
    ViewBag.ModuleName = "Tests/AddNewPermission (Admin)";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}



<div style="margin-top:15px"></div>
<h4>AddNewPermission</h4>


<!--<h2>@ViewBag.TestName</h2>
<h3>Add New Permission 1</h3>
    -->

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()

    <div class="form-horizontal">

        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.permissionId, htmlAttributes: new { @class = "control-label col-md-1" })
            <div class="col-md-2">
                @Html.DropDownList("permissionId", null, htmlAttributes: new { @class = "form-control" })
                @Html.HiddenFor(model => model.testId, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.permissionId, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.testResourceTypeId, htmlAttributes: new { @class = "control-label col-md-1" })
            <div class="col-md-2">
                @Html.DropDownList("testResourceTypeId", null, htmlAttributes: new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.testResourceTypeId, "", new { @class = "text-danger" })
            </div>
        </div>


        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.nurseRequired, htmlAttributes: new { @class = "control-label col-md-1" })
            <div class="col-md-2">
                <div class="checkbox">
                    @Html.EditorFor(model => model.nurseRequired)
                    @Html.ValidationMessageFor(model => model.nurseRequired, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.doctorRequiredInOffice, htmlAttributes: new { @class = "control-label col-md-1" })
            <div class="col-md-2">
                <div class="checkbox">
                    @Html.EditorFor(model => model.doctorRequiredInOffice)
                    @Html.ValidationMessageFor(model => model.doctorRequiredInOffice, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.isPerformedInGroup, htmlAttributes: new { @class = "control-label col-md-1" })
            <div class="col-md-2">
                <div class="checkbox">
                    @Html.EditorFor(model => model.isPerformedInGroup)
                    @Html.ValidationMessageFor(model => model.isPerformedInGroup, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.AssignedAppointmentDoctor, htmlAttributes: new { @class = "control-label col-md-1" })
            <div class="col-md-2">
                <div class="checkbox">
                    @Html.EditorFor(model => model.AssignedAppointmentDoctor)
                    @Html.ValidationMessageFor(model => model.AssignedAppointmentDoctor, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="form-group form-group-sm">
            <div class="col-md-offset-1 col-md-11">
                <input type="submit" value="Create" class="btn btn-default btn-sm btn-primary" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Back to List", "Index")
</div>
