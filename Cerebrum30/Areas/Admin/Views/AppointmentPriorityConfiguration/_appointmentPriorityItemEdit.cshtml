@model Cerebrum.ViewModels.Admin.VMAppointmentPriority
@{
    Layout = null;
    string chkDefault = Model.IsDefault ? "checked" : string.Empty;
    string chkActive = Model.IsActive ? "checked" : string.Empty;
    string headerText = Model.Id == 0 ? "Add Priority" : "Edit Priority";
}
@Html.ModalHeader(headerText)
@Html.AntiForgeryToken()

<table class="table table-condensed table-bordered table-responsive">
    <thead>
        <tr>
            <th>Priority</th>
            <th class="text-center">Rank</th>
            <th class="text-center">Default</th>
            <th class="text-center">Active</th>
        </tr>
    </thead>
    <tr>
        <td><input type="text" id="txtPriorityName" class="form-control" maxlength="20" value="@Model.PriorityName" /></td>
        <td class="text-center">
            <select class="form-control" id="ddlRank" name="ddlRank">

                @for (int i = 1; i < 10; i++)
                {
                    string selectedOption = (i == Model.Rank) ? "selected" : "";
                    <option value="@i" selected="@(selectedOption == "selected")">@i</option>
                }
            </select>
        </td>
        <td class="text-center">
            <input id="chkPriorityDefault" type="checkbox" @chkDefault />
        </td>
        <td class="text-center">
            <input id="chkPriorityActive" type="checkbox" @chkActive />
        </td>
    </tr>
</table>

<div class="modal-footer">
    <span id="span-error-save-priority" class="text-danger pull-left"></span>
    <button type="button" data-priority-id="@Model.Id" class="btn btn-info btn-sm" id="btn-save-priority">Save</button>
    <button type="button" class="btn btn-default btn-sm pull-right" data-dismiss="modal">Cancel</button>
</div>

