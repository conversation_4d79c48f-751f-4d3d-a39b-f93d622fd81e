﻿@model Cerebrum.ViewModels.Audit.VMAuditPageSearch

@using (Html.BeginForm("accessedpages", "audit", new { area = "admin" }, FormMethod.Get, true, new { @class = "form-inline", @id = "frm-pageaccess-search" }))
{    
    
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.Start, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.Start, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.End, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.End, new { htmlAttributes = new { @class = "form-control date-picker" } })
    </div>
    <div class="form-group form-group-sm">
        @Html.LabelFor(model => model.Username, htmlAttributes: new { @class = "control-label" })
        @Html.EditorFor(model => model.Username, new { htmlAttributes = new { @class = "form-control" } })
    </div>    
    <button type="submit" style="margin-right:5px;margin-left:5px;" class="btn btn-default btn-sm">Search</button>

}
<div>
    @Html.ValidationMessageFor(model => model.Start, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.End, "", new { @class = "text-danger" })
    @Html.ValidationMessageFor(model => model.Username, "", new { @class = "text-danger" })    
</div>