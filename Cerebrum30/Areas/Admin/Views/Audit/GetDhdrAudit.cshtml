﻿@model Cerebrum.ViewModels.Audit.VMAuditDhdrPageSearch

@{
    ViewBag.Title = "GetDhdrAudit";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";

    string fromDate = string.Empty;
    string toDate = string.Empty;

    fromDate = ((DateTime)Model.Start).ToString("MM/dd/yyyy").Replace("-", "/");
    toDate = ((DateTime)Model.End).ToString("MM/dd/yyyy").Replace("-", "/");
}
@section scripts {
    <script src="~/Areas/Admin/scripts/get-dhdr-audit.js?@DateTime.Now"></script>
}
@section customcss{
    <link href="~/Areas/Admin/Content/get-dhdr-audit.css?@DateTime.Now" rel="stylesheet" />
}
<div class="div-dhdr-log">
    <h2>DHDR Audit Log</h2>
    <div>
        @Html.AntiForgeryToken()
        <table class="table table-striped">
            <tr>
                <td>
                    <div class="form-group">
                        <div class="col-md-2">
                            @Html.LabelFor(model => model.Start, htmlAttributes: new { @class = "control-label col-md-12" })
                            @Html.EditorFor(model => model.Start, new { htmlAttributes = new { @class = "date form-control date-picker datepicker", @Value = @fromDate } })
                            @Html.ValidationMessageFor(model => model.Start, "", new { @class = "text-danger" })
                        </div>
                        <div class="col-md-2">
                            @Html.LabelFor(model => model.End, htmlAttributes: new { @class = "control-label col-md-12" })
                            @Html.EditorFor(model => model.End, new { htmlAttributes = new { @class = "date form-control date-picker datepicker", @Value = @toDate } })
                            @Html.ValidationMessageFor(model => model.End, "", new { @class = "text-danger" })
                        </div>
                        <div class="col-md-2">
                            @Html.LabelFor(model => model.LastName, htmlAttributes: new { @class = "control-label col-md-12" })
                            @Html.EditorFor(model => model.LastName, new { htmlAttributes = new { @class = "form-control" } })
                            @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                        </div>
                        <div class="col-md-2">
                            @Html.LabelFor(model => model.HealthcardNumber, htmlAttributes: new { @class = "control-label col-md-12" })
                            @Html.EditorFor(model => model.HealthcardNumber, new { htmlAttributes = new { @class = "form-control", @maxlength = 10 } })
                            @Html.ValidationMessageFor(model => model.HealthcardNumber, "", new { @class = "text-danger" })
                        </div>
                        <div class="col-md-2">
                            @Html.LabelFor(model => model.InteractingService, htmlAttributes: new { @class = "control-label col-md-12" })
                            <select id="InteractingService" name="InteractingService" class="form-control">
                                <option value="">ALL</option>
                                <option value="PCOI">PCOI</option>
                                <option value="DHDR EHR Service">DHDR EHR Service</option>
                                
                            </select>
                        </div>
                        <div class="col-md-2" style="margin-top:19px;">
                            <input type="submit" value="Search" class="btn btn-default" id="btn-dhdr-log-search" />
                            <span style="visibility:hidden; padding-top:1px;" id="span-loading-dhdr-log-search-result">
                                <img src="~/Content/Images/loading.gif" style="width:20px;" />
                            </span>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
        <div id="div-dhdr-log-search-result" class="div-dhdr-log-search-result">
        </div>
    </div>
</div>
