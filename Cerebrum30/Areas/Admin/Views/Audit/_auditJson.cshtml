@model Cerebrum.ViewModels.Audit.VMAudiJson

<div class="panel panel-default">
    <div class="panel-heading">Changes</div>
    <div class="panel-body">
        <table class="table">
            <tr>
                <th>@Html.DisplayNameFor(model => model.TableName)</th>
                <th>@Html.DisplayNameFor(model => model.primaryKey)</th>
            </tr>
            <tr>
                <td>@Html.DisplayFor(model => model.TableName)</td>
                <td>@Html.DisplayFor(model => model.primaryValue)</td>
            </tr>            
        </table>
        <table class="table table-condensed">
            <tr>
                <th>Column Name</th>
                <th>Old Value</th>
                <th>New Value</th>
            </tr>
            @foreach (var item in Model.Changes)
            {
                <tr>
                    <td>@item.CN</td>
                    <td>@item.OV</td>
                    <td>@item.NV</td>
                </tr>
            }
        </table>
    </div>
</div>
