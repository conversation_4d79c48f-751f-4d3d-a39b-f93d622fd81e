﻿@model Cerebrum.ViewModels.Audit.VMAuditSearch
<div class="panel panel-info">
    <div class="panel-heading">Search</div>
    <div class="panel-body">
        @using (Html.BeginForm("Index", "Audit"))
        {
            @Html.AntiForgeryToken()

            <div class="form-inline">
                @Html.ValidationSummary(true, "", new { @class = "text-danger" })
                <div class="form-group">
                    @Html.LabelFor(model => model.startDate, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.startDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
                        @Html.ValidationMessageFor(model => model.startDate, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.endDate, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.endDate, new { htmlAttributes = new { @class = "form-control date-picker" } })
                        @Html.ValidationMessageFor(model => model.endDate, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.IPAddress, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.IPAddress, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.IPAddress, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.UserName, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.UserName, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.HiddenFor(model => model.UserId)
                        @Html.ValidationMessageFor(model => model.UserName, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.patientName, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.patientName, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.HiddenFor(model => model.PatientRecordId)
                        @Html.ValidationMessageFor(model => model.PatientRecordId, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.Content, htmlAttributes: new { @class = "control-label col-md-3" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.Content, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.Content, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-offset-2 col-md-10">
                        <input type="submit" value="Search" class="btn btn-default" />
                    </div>
                </div>
            </div>
        }
    </div>
    <hr />
</div>
