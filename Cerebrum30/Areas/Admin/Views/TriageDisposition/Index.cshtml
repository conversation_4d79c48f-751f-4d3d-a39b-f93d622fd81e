@model IEnumerable<Cerebrum.ViewModels.Triage.VMTriageDisposition>

@{
    ViewBag.Title = "Index";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>Triage Dispositions [@Model.Count()]</h2>

<p>
    @Html.ActionLink("Create New", "Create")
</p>
<table class="table">
    <tr>
        <th></th>
        <th>
            @Html.DisplayNameFor(model => model.message)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.displayOrder)
        </th>
        <th></th>
    </tr>
    @{ int cnt = 1;}
@foreach (var item in Model) {
    <tr>
       <td>@(cnt++)</td>
        <td>
            @item.message
        </td>
        <td>
            @item.displayOrder
        </td>
        <td>
            @Html.ActionLink("Edit", "Edit", new { id=item.Id }) |
            @Html.ActionLink("Delete", "Delete", new { id=item.Id })
        </td>
    </tr>
}

</table>
