@model Cerebrum.ViewModels.Triage.VMTriageDisposition

@{
    ViewBag.Title = "_form";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<h2>@( Model == null ? "New" : "Edit")</h2>

@using (Html.BeginForm()) 
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <h4>Triage Disposition</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        
        <div class="form-group">
            @Html.LabelFor(model => model.message, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.TextAreaFor(model => model.message, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.message, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.displayOrder, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.displayOrder, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.displayOrder, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                @Html.HiddenFor(model => model.Id)
                @Html.HiddenFor(model => model.PracticeId)
                <input type="submit" value="@( Model == null ? "Add" : "Update")" class="btn btn-default" />
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Back to List", "Index")
</div>
