@model IEnumerable<Cerebrum.ViewModels.Practice.VMPracticeAppointmentTypeMain>

@{
    ViewBag.Title = "Appointment Types";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}

<h2>Appointment Types</h2>
@(await Html.PartialAsync("Create", new { area = "admin" }))
<div class="panel panel-default">
    <div class="panel-heading">Practice Appointment Types</div>
    <div class="panel-body form-inline">
        @if(Model!=null && Model.Count() > 0) { 
        <table class="table">
            <tr>
                <th></th>
                <th>
                    @Html.DisplayNameFor(model => model.AppointmentType)
                </th>
                <th>
                    @Html.DisplayNameFor(model => model.Duration)
                </th>
            </tr>

            @foreach (var item in Model)
            {
                <tr>
                    <td>
                        @Html.ActionLink("Delete", "Delete", new { id = item.Id }, new { @class = "btn-delete-app-type", data_id = item.Id })
                    </td>
                    <td>
                        @item.AppointmentType
                    </td>
                    <td>
                        @item.Duration
                    </td>
                </tr>
            }
        </table>
        }
        </div>
    </div>
<hr />
        <script>
            $(function () {
                $(document).on("click", ".btn-delete-app-type", function (e) {
                    e.preventDefault();
                    var deurl = $(this).attr("href");
                    var dataid = $(this).data("id");

                    ajaxCall(deurl, { id: dataid }, false, function (result) {
                        location.reload();
                    });
                });
                $(document).on('submit', '#frm-pract-app-type-create', function (e) {
                    e.preventDefault();
                    var selectedAppTypes = [];
                    $('#apt-type input:checked').each(function () {
                        selectedAppTypes.push($(this).val());
                    });
                    $('.btn-app-type-add').prop('disable', true);
                   
                    //console.log(selectedAppTypes);
                    $.ajax({
                        url: this.action,
                        type: this.method,
                        data: {appointmentTypes: selectedAppTypes },
                        success: function (result) {
                            location.reload();
                        }
                    });
                });
            });
        </script>
