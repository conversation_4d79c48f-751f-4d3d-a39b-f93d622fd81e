@model IEnumerable<Cerebrum.ViewModels.Office.VMOfficeRoomType>

@{
    ViewBag.Title = "Room Types";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}

<h2>Office Room Types</h2>
<div class="panel panel-default">
    <div class="panel-heading">New Office Room Type</div>
    <div class="panel-body form-inline">

       @using (Html.BeginForm("Create","OfficeRoomType", new { area = "admin" }, FormMethod.Post, true, new { @id = "frm-room-type-create" }))
       {

        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group">
            Room Type
            <div class="col-md-13">
                <input type="text" class="form-control" id="Type" name="Type" />
            </div>
        </div>
        <div class="form-group">
            <br />
            <div class="col-md-offset-2 col-md-3">
                <input type="submit" value="Add" class="btn btn-default btn-room-type" />
            </div>
        </div>
       }
    </div>
</div>
@if (Model != null && Model.Any())
{
<table class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.Type)
        </th>
        <th></th>
    </tr>

    @foreach (var item in Model)
    {
        <tr class="<EMAIL>">
            <td>
                <input type="text" id="@item.Id" name="type" value="@item.Type" readonly class="form-control room-type">
            </td>
            <td>
                @Html.ActionLink("Update", "Edit", new { id = item.Id }, new { @class = "btn-update", data_edit_id = item.Id })
            </td>
        </tr>
    }

</table>
}
<script>
    $(function () {
        $(document).on("dblclick", ".room-type", function () {
            $(this).attr('readonly',false);
        });

        $(document).on('submit', '#frm-room-type-create', function (e) {
            e.preventDefault();

            $('.btn-room-type').prop('disable', true);

            ajaxCall(this.action, $(this).serialize(), false, function (result) {
                location.reload();
            });
        });

        $(document).on("click", ".btn-update", function (e) {
            e.preventDefault();
            var editId = $(this).data("edit-id");
            var cls = ".edit-" + editId;

            var rtype = $(cls).find("#" + editId);

            if(!rtype.is('[readonly]')) {
                
                var url = $(this).attr("href");

                $.ajax({
                    url: url,
                    type: "Post",
                    headers: { "RequestVerificationToken": requestVerificationToken },
                    data: {
                        id: editId,
                        type: rtype.val()
                    },success: function (result) {
                        if (result.success) {
                            showNotificationMessage("success", result.message);
                        } 
                    },
                    complete: function () {
                        location.reload();
                    }
                });
            }
        });
    });
</script>