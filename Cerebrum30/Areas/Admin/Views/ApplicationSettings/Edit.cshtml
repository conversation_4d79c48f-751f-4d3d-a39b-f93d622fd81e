﻿@model Cerebrum.ViewModels.ApplicationSetting.VMApplicationSetting


@using (Html.BeginForm("Edit", "ApplicationSettings", FormMethod.Post, new { @id="frm-edit-user-key" }))
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.id)

        <div class="form-group">
            @Html.LabelFor(model => model.key, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.key, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.key, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.value, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.TextAreaFor(model => model.value, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.value, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.isActive, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                <div class="checkbox">
                    @Html.EditorFor(model => model.isActive)
                    @Html.ValidationMessageFor(model => model.isActive, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                @Html.HiddenFor(model => model.lastModifiedBy)
                @Html.HiddenFor(model => model.lastModifiedByUser)
                @Html.HiddenFor(model => model.dateCreated)
                @Html.HiddenFor(model => model.dateLastModified)
            </div>
        </div>
    </div>
}

@section Scripts {
    @Scripts.Render("~/bundles/jqueryval")
}
