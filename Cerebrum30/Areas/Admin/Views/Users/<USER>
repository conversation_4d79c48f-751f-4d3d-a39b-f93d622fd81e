@model IEnumerable<Cerebrum.ViewModels.User.VMLandingPageItem>


<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h4 class="modal-title"><span id="saved-title">Landing Pages</span></h4>
</div>

<div class="modal-body">
    <table class="table">
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.Description)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.IsUserDefault)
            </th>
            <th></th>
        </tr>

        @foreach (var item in Model)
        {
            <tr>
                <td>
                    @item.Description
                </td>
                <td>
                    @item.IsUserDefault
                </td>
                <td>
                    @if (item.IsUserDefault)
                    {
                        <span>Default</span>
                    }
                    else
                    {
                        using (Html.BeginForm("SaveLandingPage", "users", new { area = "admin" }, FormMethod.Post, true, new { @class = "frm-set-user-page" }))
                        {
                            @Html.AntiForgeryToken()
                            @Html.Hidden("LandingPageId", item.LandingPageId)

                            <button type="submit" class="btn btn-xs btn-primary">Set As Default</button>
                        }

                    }
                </td>
            </tr>
        }

    </table>
</div>
<div class="modal-footer">    
    <button class="btn btn-default btn-sm" data-dismiss="modal">Close</button>
</div>
