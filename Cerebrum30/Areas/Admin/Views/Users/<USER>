﻿@model Cerebrum.ViewModels.User.VMUserPatientAccess

@using (Html.BeginForm("userpatientaccess", "users", new { area = "admin" }, FormMethod.Post, true, new { @class = "", @id = "frm-add-user-patient" }))
{
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.PatientId)
    @Html.HiddenFor(model => model.UserId)

    <div class="form-inline">
        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.UserFullName, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.UserFullName, new { htmlAttributes = new { @class = "form-control " } })
        </div>
        <div class="form-group form-group-sm">
            @Html.LabelFor(model => model.PatientFullName, htmlAttributes: new { @class = "control-label" })
            @Html.EditorFor(model => model.PatientFullName, new { htmlAttributes = new { @class = "form-control" } })
        </div>        
        <button type="submit" style="margin-right:5px;margin-left:5px;" class="btn btn-default btn-sm">Add</button>
    </div>

    <div class="form-horizontal">
        <div class="form-group form-group-sm">
            <div class="col-md-10">
                <div class="checkbox">
                    <label>
                        @Html.EditorFor(model => model.DenyAllExcept) <span class="checkbox-text">@Html.DisplayNameFor(model => model.DenyAllExcept)</span>
                    </label>
                    
                </div>
            </div>
        </div>
    </div>
}
<div>
    <div>
        @Html.ValidationMessageFor(model => model.UserId, "", new { @class = "text-danger" })
    </div>
    <div>
        @Html.ValidationMessageFor(model => model.PatientId, "", new { @class = "text-danger" })
    </div>
    <div>
        @Html.ValidationMessageFor(model => model.DenyAllExcept, "", new { @class = "text-danger" })
    </div>
    <div>
        @Html.ValidationMessageFor(model => model.UserFullName, "", new { @class = "text-danger" })
    </div>
    <div>
        @Html.ValidationMessageFor(model => model.PatientFullName, "", new { @class = "text-danger" })
    </div>
</div>