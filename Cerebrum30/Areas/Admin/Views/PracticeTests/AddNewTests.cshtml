﻿<div class="container form-horizontal col-lg-12">
    <h4>Add Test to Practices</h4>
    <p>Press 'Ctrl' button and Click on Test to Select. </p>
    @using (Html.BeginForm("AddNewTests", "PracticeTests", new { area = "Admin", controller = "PracticeTests" }))
    {
        <input type="hidden" id="practiceId" value="@ViewBag.PracticeId" />
        <div class="form-group col-sm-10">
            <div class="checkbox col-sm-10">
                @Html.ListBox("tests", new MultiSelectList(ViewBag.Tests, "Id", "name"), new { multiple = "multiple", @class = "multiple", size = 15 })
            </div>
            <br />
            <p>Press 'Save' button to save your selection </p>
        </div>
    }
</div>
