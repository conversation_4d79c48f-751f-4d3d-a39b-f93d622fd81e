@model IEnumerable<Cerebrum.ViewModels.HL7.VMHL7LostReport>

@{
    ViewBag.Title = "HL7 Lost Reports";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

<h2>HL7 Lost Reports</h2>
<p>
    <div class="panel panel-default">
        <div class="panel-heading">Search HL7 Lost Report</div>
        <div class="panel-body form-inline">
            @using (Html.BeginForm("Index", "HL7LostReports", new { area = "Admin" }, FormMethod.Post, true, new { @id = "frm-HL7LostReports-search" }))
            {
                @Html.AntiForgeryToken()
                <div class="form-group form-group-sm">
                    @Html.Label("From Date", "", new { @class = "control-label" })
                    @Html.Editor("frmdt", new { htmlAttributes = new { @class = "form-control date-picker" } })
                </div>
                <div class="form-group form-group-sm">
                    @Html.Label("To Date", "", new { @class = "control-label" })
                    @Html.Editor("todt", new { htmlAttributes = new { @class = "form-control date-picker" } })
                </div>
                <div class="form-group form-group-sm">
                    @Html.Label("Lab", "", new { @class = "control-label" })
                    @Html.DropDownList("Lab", new SelectList(ViewBag.Labs, "Value", "Text"), new { htmlAttributes = new { @class = "form-control" } })
                </div>
                <button id="btn-search-lost-report" type="submit" style="margin-right:5px;" class="btn btn-default btn-xs">Search</button>
            }
        </div>
    </div>
    @if (CerebrumUser.HasPermission("Super Admin"))
    {
        <div class="panel panel-default">
            <div class="panel-heading">Upload LifeLabs HL7 file</div>
            <div class="panel-body form-inline">
                @using (Html.BeginForm("UploadLabFile", "HL7LostReports", new { area = "Admin" }, FormMethod.Post, true, new { @enctype = "multipart/form-data", @id = "frm-HL7LostReports-upload" }))
                {
                    @Html.AntiForgeryToken()
                    <div class="form-group form-group-sm">
                        @Html.Label("File Name", "", new { @class = "control-label" })

                        <input type="file" name="file" id="file" />
                    </div>
                    <div class="form-group form-group-sm">
                        <br />
                        <input type="submit" value="Upload File" />
                    </div>
                }
            </div>
        </div>
    }
</p>
@if (Model != null)
{
    <a href="#">Total <span class="badge">@Model.Count()</span></a>
    <table class="table">
        <tr>
            <th>#</th>
            <th>
                @Html.DisplayNameFor(model => model.Lab)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.AccessionNumber)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.PatientOHIP)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.PatientLastName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.PatientFirstName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.ReportSaved)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.CreateDateTime)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.LastModifiedDateTime)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.LastModifiedByUserId)
            </th>
            <th></th>
        </tr>

        @foreach (var item in Model)
        {
            @(await Html.PartialAsync("ReportInfo", item))
        }

    </table>
}
<br />
<br />
<br />
<br />
<script src="~/Areas/Admin/scripts/HL7LostReports.js"></script>