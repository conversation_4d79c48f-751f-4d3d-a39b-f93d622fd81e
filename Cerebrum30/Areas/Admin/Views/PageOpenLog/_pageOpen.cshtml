﻿@model Cerebrum.ViewModels.User.VMPageOpenLog
@if (Model != null)
{
    <div class="panel panel-default">
        <div class="panel-heading">Log Info</div>
        <div class="panel-body">
            <table class="table">
                <tr>
                    <th>@Html.DisplayNameFor(model => model.userName)</th>
                    <th>@Html.DisplayNameFor(model => model.sessionId)</th>
                    <th>@Html.DisplayNameFor(model => model.status)</th>
                    <th>@Html.DisplayNameFor(model => model.IPAddress)</th>
                    <th>@Html.DisplayNameFor(model => model.ComputerName)</th>
                    <th>@Html.DisplayNameFor(model => model.Port)</th>
                    <th>@Html.DisplayNameFor(model => model.Browser)</th>
                </tr>
                <tr>
                    <td>@Html.DisplayFor(model => model.userName)</td>
                    <td>@Html.DisplayFor(model => model.sessionId)</td>
                    <td>@Html.DisplayFor(model => model.status)</td>
                    <td>@Html.DisplayFor(model => model.IPAddress)</td>
                    <td>@Html.DisplayFor(model => model.ComputerName)</td>
                    <td>@Html.DisplayFor(model => model.Port)</td>
                    <td>@Html.DisplayFor(model => model.Browser)</td>
                </tr>
            </table>
        </div>
    </div>
}
