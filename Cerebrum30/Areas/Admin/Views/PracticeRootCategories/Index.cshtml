﻿@model Cerebrum.ViewModels.Practice.VMPracticeRootCatMain
@{
    ViewBag.Title = "Pracice Root Categories";
    ViewBag.ModuleName = "Root Categories (Admin)";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}
<link href="~/Areas/Admin/Content/pracrootcat.css" rel="stylesheet" />
<script src="~/Areas/Admin/scripts/admincategories.js"></script>
<div id="div-main-practice-cat-holder">
    <div id="frm-prac-test-group" class="form-inline">
        <div class="form-group form-group-sm">
            <label class="control-label" for="GroupId">Group(s)</label>
            @Html.DropDownList("GroupId", new SelectList(Model.TestGroups, "Value", "Text"), "Choose One", new { @class = "form-control" })
        </div>
        <div class="form-group form-group-sm hidden hidden-form-group-row">            
            <div id="frm-add-new-template">
                <div class="form-group form-group-sm">
                    <input class="form-control" type="text" id="TemplateName" name="TemplateName" />
                    <button type="button" id="btn-practice-newtemplate"
                            class="btn btn-default btn-xs btn-default modal-submit-btn">
                        Add Template
                    </button>
                </div>
            </div>           
        </div>
    </div>
    @*@using (Html.BeginForm("GetPracticeTemplates", "PracticeRootCategories", new { area = "admin" }, FormMethod.Get, new { @class = "form-inline", @id = "frm-prac-test-group" }))
    {
        <div class="form-group form-group-sm">
            <label class="control-label" for="GroupId">Group(s)</label>
            @Html.DropDownList("GroupId", new SelectList(Model.TestGroups, "Value", "Text"), "Choose One", new { @class = "form-control" })
        </div>

    }*@

    <div id="div-practice-templates">

    </div>
    <div id="div-practice-rootcategories">

    </div>

</div>