﻿@model Cerebrum.ViewModels.TestBase.VMTemplateDoctorMain
@{
    var totalDoctors = Model.TemplateDoctors.Count();
}

<div class="panel panel-default">
    <div class="panel-heading">Total (@totalDoctors)</div>
    <div class="panel-body">
    @using (Html.BeginForm("SavePracticeTemplateDoctors", "PracticeRootCategories", new { area = "admin" }, FormMethod.Post, true, new { @id = "frm-save-practice-template-doctors" }))
    {
        <div class="content-height500">
            @Html.HiddenFor(model => model.PracticeTemplateId)
            @Html.HiddenFor(model => model.GroupId)
            <table id="tbl-practice-template-doctors" class="table table-condensed table-bordered">
                <thead>
                    <tr>
                        <th>
                            Doctor
                        </th>
                        <th>
                            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Activates this template for the doctor(s)">Active</span>
                            <input type="checkbox" data-toggle-class="chk-visible-active" class="cb-toggleICON" />
                        </th>
                        <th>
                            <span data-cb-tp="tooltip" data-cb-tp-placement="bottom" data-cb-tp-title="Sets this template as default for the current test group for the doctor(s)">Default</span>
                            <input type="checkbox" data-toggle-class="chk-visible-default" class="cb-toggleICON" />
                        </th>

                    </tr>
                </thead>
                @for(int i=0;i<Model.TemplateDoctors.Count();i++)
            {
            <tr>
                <td>
                    @Html.HiddenFor(x => x.TemplateDoctors[i].PracticeTemplateId)
                    @Html.HiddenFor(x => x.TemplateDoctors[i].ExternalDoctorId)
                    @Html.HiddenFor(x => x.TemplateDoctors[i].DoctorTemplateId)
                    @Html.HiddenFor(x => x.TemplateDoctors[i].FirstName)
                    @Html.HiddenFor(x => x.TemplateDoctors[i].LastName)

                    @Html.DisplayFor(x => x.TemplateDoctors[i].LastName) @Html.DisplayFor(x => x.TemplateDoctors[i].FirstName)
                </td>
                <td>
                    @Html.CheckBoxFor(x => x.TemplateDoctors[i].IsActive, new { @class = "chk-visible-active" })
                </td>
                <td>
                    @Html.CheckBoxFor(x => x.TemplateDoctors[i].IsDefault, new { @class = "chk-visible-default" })
                </td>

            </tr>
            }
            </table>
        </div>
        <div style="margin-top:15px;" class="text-right">
            <button type="submit" class="btn btn-primary btn-sm c-pointer modal-submit-btn">Save</button>
        </div>
    }        
    </div>
</div>