@model Cerebrum.ViewModels.OfficeDailyRegister.IndexResponse

@{
    ViewBag.Title = "Clinic Daily Register";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}

<script src="~/Areas/Admin/scripts/OfficeDailyRegister.js"></script>

<div class="col-sm-12">
    <h2>Clinic Daily Register</h2>
</div>
<div class="col-sm-12" style="margin-top: 32px;">
    <div class="col-sm-1"><label>Start</label></div>
    <div class="col-sm-11">
        <input type="text" class="form-control input-sm" id="officeDailyRegisterStart" style="width: 360px; display: inline-block;" value="@DateTime.Now.AddDays(-7).ToString("MM/dd/yyyy")">
        <span style="display: inline-block;">(only search data within one week)</span>
    </div>
</div>
<div class="col-sm-12" style="margin-top: 8px;">
    <div class="col-sm-1"><label>End</label></div>
    <div class="col-sm-11">
        <input type="text" class="form-control input-sm" id="officeDailyRegisterEnd" style="width: 360px; display: inline-block;" value="@DateTime.Now.ToString("MM/dd/yyyy")">
        <span style="display: inline-block;">(only search data within one week)</span>
    </div>
</div>
<div class="col-sm-12" style="margin-top: 8px;">
    <div class="col-sm-1"><label>Office</label></div>
    <div class="col-sm-11" id="dailyRegisterOfficeIds" name="dailyRegisterOfficeIds" style="overflow-y: scroll; height:88px; background-color: #ffffff; margin-left: 16px; width: 360px;">
        @foreach (var office in Model.offices)
        {
            <div><input type="checkbox" id="<EMAIL>" name="<EMAIL>" value="@office.value" onclick="showOfficeRooms(this)">&nbsp;&nbsp;&nbsp; @office.text</div>
        }
    </div>
</div>
<div class="col-sm-12" style="margin-top: 8px;">
    <div class="col-sm-1"><label>Resource</label></div>
    <div class="col-sm-11" id="dailyRegisterRoomIds" name="dailyRegisterRoomIds" style="overflow-y: scroll; height:88px; background-color: #ffffff; margin-left: 16px; width: 360px;">
        @foreach (var room in Model.rooms)
        {
            <div><input type="checkbox" id="<EMAIL>" name="<EMAIL>" value="@room.userId" data-office-id="@room.officeId">&nbsp;&nbsp;&nbsp; @room.name</div>
        }
    </div>
</div>
<div class="col-sm-12" style="margin-top: 8px;">
    <div class="col-sm-1"><label>Exclude Waitlist</label></div>
    <div class="col-sm-11">
        <input type="checkbox" id="chkExcludeWaitlist">        
    </div>
</div>
<div class="col-sm-12" style="margin-top: 24px;">
    <div class="col-sm-1"></div>
    <div class="col-sm-11 text-right" style="margin-left: 16px; width: 360px;">
        <button type="button" class="btn btn-default btn-sm btn-primary" id="buttonOfficeDailyRegisterGetReport" name="buttonOfficeDailyRegisterGetReport">Get Report</button>
    </div>
</div>
@using (Html.BeginForm("GetOfficeDailyRegisterReport", "OfficeDailyRegister", new { area = "admin" }, FormMethod.Post, true, new { @id = "frm-office-daily-register-report", target = "_blank" }))
{
    @Html.AntiForgeryToken()
    @Html.Hidden("officeDailyRegisterReportRequest", "")
}
