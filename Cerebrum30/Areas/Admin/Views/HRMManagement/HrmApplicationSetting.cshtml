﻿@model Cerebrum.ViewModels.HRMImport.HrmSettingData

@if (string.IsNullOrEmpty(Model.errorMessage))
{
    <form id="formHrmApplicationSetting">
        <div class="modal-header ui-draggable-handle">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h4 class="modal-title">HRM System Configuration</h4>
        </div>
        <div class="modal-body">
            <div class="form-horizontal">
                @Html.AntiForgeryToken()
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">Host Address</label>
                    <div class="col-md-4">
                        @Html.EditorFor(model => model.applicationSetting.hrmApplicationSettingFtpUrl, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">TCP Port</label>
                    <div class="col-md-4">
                        @Html.EditorFor(model => model.applicationSetting.hrmApplicationSettingFtpPort, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">SFTP User Name</label>
                    <div class="col-md-4">
                        @Html.EditorFor(model => model.applicationSetting.hrmApplicationSettingFtpUsername, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">SSH Key File</label>
                    <div class="col-md-4">
                        @Html.EditorFor(model => model.applicationSetting.hrmApplicationSettingFtpSshKeyFileName, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                    </div>
                    <div class="col-md-4">
                        <input type="file" id="File" name="File" style="width:280px;" />
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" id="buttonHrmApplicationSettingSave" name="buttonHrmApplicationSettingSave" value="Save" onclick="return buttonHrmApplicationSettingSaveClicked();" class="btn btn-sm modal-submit-btn btn-spacing btn-primary ">Save</button>
            <button class="btn btn-default btn-sm" data-dismiss="modal">Cancel</button>
        </div>
    </form>
}
else
{
    <div class="text-center text-danger">
        <br /><br /><br /><br /><br /><br /><br /><br />
        <h1>@Model.errorMessage</h1>
        <br /><br /><br /><br /><br /><br /><br /><br />
    </div>
}
