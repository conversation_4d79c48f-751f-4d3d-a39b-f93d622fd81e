﻿@model Cerebrum.ViewModels.Common.VMPracticeCommType


<form id="formHrmPracticeSetting">
    @Html.AntiForgeryToken()
    @Html.HiddenFor(model => model.Id)
    @Html.HiddenFor(model => model.PracticeId)
    @Html.HiddenFor(model => model.ExternalCommTypeId)
    @Html.HiddenFor(model => model.ExternalCommCode)
    <div class="modal-header ui-draggable-handle">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
        <h4 class="modal-title">HRM Clinic Configuration</h4>
    </div>
    <div class="modal-body">
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            
            <div class="form-horizontal">
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">FTP Folder</label>
                    <div class="col-md-6">
                        @Html.EditorFor(model => model.FtpFolder, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                        @Html.ValidationMessageFor(model => model.FtpFolder, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">AES Decryption key</label>
                    <div class="col-md-6">
                        @Html.EditorFor(model => model.DecryptionCertificateFile, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                        @Html.ValidationMessageFor(model => model.DecryptionCertificateFile, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">Interval (Minutes)</label>
                    <div class="col-md-6">
                        @Html.EditorFor(model => model.PollingInterval, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                        @Html.ValidationMessageFor(model => model.PollingInterval, "", new { @class = "text-danger" }) 
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4" title="comma seperated emails">HRM Error Recipients</label>
                    <div class="col-md-6">
                        @Html.EditorFor(model => model.RecipientsEmails, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                        @Html.ValidationMessageFor(model => model.RecipientsEmails, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4" title="receive further notifications after initial outage">Alert</label>
                    <div class="col-md-6">
                        @Html.RadioButtonFor(model => model.Alert, true) On
                        <span style="padding-right: 32px;"></span>
                        @Html.RadioButtonFor(model => model.Alert, false) Off
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">HRM Confidentiality Statement</label>
                    <div class="col-md-6">
                        @Html.TextAreaFor(model => model.ConfidentialityStatement, new { @style = "height: auto; width: 100%;", @rows = 5 })
                        @Html.ValidationMessageFor(model => model.ConfidentialityStatement, "", new { @class = "text-danger" })
                    </div>
                </div>
            </div>        
    </div>
    <div class="modal-footer">
        <button type="button" id="buttonHrmPracticeSettingSave" name="buttonHrmPracticeSettingSave" value="Save" class="btn btn-sm modal-submit-btn btn-spacing btn-primary">Save</button>
        <button class="btn btn-default btn-sm" data-dismiss="modal">Cancel</button>
    </div>
</form>
