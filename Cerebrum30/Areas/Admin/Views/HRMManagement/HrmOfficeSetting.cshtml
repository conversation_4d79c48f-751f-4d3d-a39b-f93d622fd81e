﻿@model Cerebrum.ViewModels.HRMImport.HrmSettingData


<form id="formHrmOfficeSetting">
    <div class="modal-header ui-draggable-handle">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
        <h4 class="modal-title">HRM Clinic Configuration</h4>
    </div>
    <div class="modal-body">
        @if (string.IsNullOrEmpty(Model.errorMessage))
        {
            <div class="form-horizontal">
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">FTP Folder</label>
                    <div class="col-md-6">
                        @Html.EditorFor(model => model.officeSetting.ftpFolder, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">AES Decryption key</label>
                    <div class="col-md-6">
                        @Html.EditorFor(model => model.officeSetting.decryptionKey, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">Interval</label>
                    <div class="col-md-6">
                        @Html.EditorFor(model => model.officeSetting.pollingInterval, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4" title="comma seperated emails">HRM Error Recipients</label>
                    <div class="col-md-6">
                        @Html.EditorFor(model => model.officeSetting.errorRecipients, new { htmlAttributes = new { @class = "form-control text-box single-line" } })
                    </div>
                </div>
                <div class="form-group form-group-sm">
                    <label class="control-label col-md-4">HRM Confidentiality Statement</label>
                    <div class="col-md-6">
                        @Html.TextAreaFor(model => model.officeSetting.confidentialityStatement, new { @style = "height: auto; width: 100%;", @rows = 5 })
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="text-center text-danger">
                <br /><br /><br /><br /><br /><br /><br /><br />
                <h1>@Model.errorMessage</h1>
                <br /><br /><br /><br /><br /><br /><br /><br />
            </div>
        }
    </div>
    <div class="modal-footer">
        <button type="button" id="buttonHrmOfficeSettingSave" name="buttonHrmOfficeSettingSave" value="Save" @(string.IsNullOrEmpty(Model.errorMessage) ? "" : "disabled") onclick="return buttonHrmOfficeSettingSaveClicked();" class="btn btn-sm modal-submit-btn btn-spacing btn-primary ">Save</button>
        <button class="btn btn-default btn-sm" data-dismiss="modal">Cancel</button>
    </div>
</form>
