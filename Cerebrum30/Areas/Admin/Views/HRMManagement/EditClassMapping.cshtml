﻿@model Cerebrum.ViewModels.HRM.VMHRMReportSubClassMap

@using (Html.BeginForm("EditClassMapping", "HRMManagement", FormMethod.Post, new { @id = "form-mapping" }))
{
    @Html.AntiForgeryToken()

    <div class="form-horizontal">
        <h4>Map</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })


        <div class="form-group">
            @Html.LabelFor(model => model.SubClassName, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DisplayFor(model => model.SubClassName, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.SubClassName, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.AccompanyingSubClass, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DisplayFor(model => model.AccompanyingSubClass, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.SubClassName, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.ReportClassId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.ReportClassId, new SelectList(Model.ReportClasses, "Value", "Text"), "Select...", new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.ReportClassId, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.looseReportCategoryId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.looseReportCategoryId, new SelectList(Model.Categories, "Id", "category"), "Select...", new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.looseReportCategoryId, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                @Html.HiddenFor(model => model.SubClassId, new { htmlAttributes = new { @class = "form-control" } })
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        <button id="save-mapping" class="btn btn-primary" type="button">Save</button>
    </div>
}



