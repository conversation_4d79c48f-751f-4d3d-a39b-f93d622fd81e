﻿@model Cerebrum.ViewModels.CTS.VMCOUser

<div class="panel panel-default ">
    <div class="panel-heading"><h4>@Model.UserFullName</h4></div>
    <div id="co-practice-config" class="panel-body">

        @using (Html.BeginForm("COUser", "ConnectingOntarioConfig", new { area = "Admin" }, FormMethod.Post, true, new { @id = "frm-co-user-update" }))
        {
            @Html.AntiForgeryToken()

            <div class="form-horizontal">
                <hr />
                @Html.ValidationSummary(true, "", new { @class = "text-danger" })

                <div class="form-group">
                    @Html.LabelFor(model => model.SecretKey, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.PasswordFor(model => model.SecretKey, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.SecretKey, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.UserContextMode, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.UserContextMode, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.UserContextMode, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.OBO, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.OBO, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.OBO, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.rid, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.rid, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.rid, "", new { @class = "text-danger" })
                    </div>
                </div>

                <div class="form-group">
                    @Html.LabelFor(model => model.NameQualifier, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.NameQualifier, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.NameQualifier, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.IdentityProvider, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.IdentityProvider, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.IdentityProvider, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.UserContextID, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.EditorFor(model => model.UserContextID, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.UserContextID, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    @Html.LabelFor(model => model.ConnectingGTASTSPOSTDisabled, htmlAttributes: new { @class = "control-label col-md-2" })
                    <div class="col-md-10">
                        @Html.CheckBoxFor(model => model.ConnectingGTASTSPOSTDisabled, new { htmlAttributes = new { @class = "form-control" } })
                        <span >Uncheck for Test mode</span>
                        @Html.ValidationMessageFor(model => model.ConnectingGTASTSPOSTDisabled, "", new { @class = "text-danger" })
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-offset-2 col-md-10">
                        @Html.HiddenFor(model => model.UserId)
                        @Html.HiddenFor(model => model.UserFullName)
                        @Html.HiddenFor(model => model.DisclosureAgreementStatus)
                        @Html.HiddenFor(model => model.NameId)
                        @Html.HiddenFor(model => model.UserEmail)
                        <input type="submit" value="Save" class="btn btn-default btn-co-update-user" />
                        <input id="co-user-close" type="submit" value="Close" class="btn btn-default btn-close-update-co-user" />
                    </div>
                </div>
            </div>
        }

    </div>
</div>
