﻿@model Cerebrum.ViewModels.CTS.VMCTS_PracticeInfo

@using (Html.BeginForm("Practice", "ConnectingOntarioConfig", new { area = "Admin" }, FormMethod.Post, true, new { @id = "frm-co-practice-config" }))
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <h4>Conneting Ontario Config</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group">
            @Html.LabelFor(model => model.ClinicalContextApplicationID, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.ClinicalContextApplicationID, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ClinicalContextApplicationID, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.ClinicalContextLocalization, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.ClinicalContextLocalization, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ClinicalContextLocalization, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ClinicalContextService, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.ClinicalContextService, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.ClinicalContextService, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.STS_URL, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.STS_URL, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.STS_URL, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                @Html.HiddenFor(model => model.ClinicalContextApplication)
                @Html.HiddenFor(model => model.ShowHideButton)
                <input type="submit" value="Save" class="btn btn-default" id="btn-co-practice-config" />
            </div>
        </div>
    </div>
}
