﻿@model Cerebrum.ViewModels.Schedule.VMAppointmentTypeNew

@using (Html.BeginForm("Edit", "appointmentType", new { area = "admin" }, FormMethod.Post, true, new { @id = "frm-appointment-type-edit" }))
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        @Html.HiddenFor(model => model.Id)

        <div class="form-group">
            @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.AppointmentTypeId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.AppointmentTypeId, new SelectList(ViewBag.ParentAppointmentTypes, "Id", "Name"), "Choose One", new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.AppointmentTypeId, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.VPRequired, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                <div class="checkbox">
                    @Html.EditorFor(model => model.VPRequired)
                    @Html.ValidationMessageFor(model => model.VPRequired, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.duration, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-8">
                @Html.EnumDropDownListFor(model => model.duration, "Select", new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.duration, "", new { @class = "text-danger" })

            </div>
        </div>
    </div>
}
