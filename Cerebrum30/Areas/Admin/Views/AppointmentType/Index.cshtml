@model IEnumerable<Cerebrum.ViewModels.Schedule.VMAppointmentTypeNew>

@{
    ViewBag.Title = "Appointment Types";
    Layout = "~/Views/Shared/_LayoutBase.cshtml";
}

<h2>Appointment Types</h2>

<p>
    <a data-toggle="collapse" data-target="#add-edit-app-type">Add New</a>
    <div id="add-edit-app-type" class="collapse">
        @(await Html.PartialAsync("Create", new { area = "Admin" }))
    </div>
</p>
<table class="table">
    <tr>
        <th></th>
        <th>
            @Html.DisplayNameFor(model => model.Name)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.AppointmentType)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.duration)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.VPRequired)
        </th>

    </tr>

    @foreach (var item in Model)
    {
        <tr>
            <td>
                @Html.ActionLink("Edit", "Edit", new { id = item.Id }, new { @class = "appoitnemnt-type-edit" })
            </td>
            <td>
                @item.Name
            </td>
            <td>
                @item.AppointmentType
            </td>
            <td>
                @item.duration
            </td>
            <td>
                @item.VPRequired
            </td>

        </tr>
    }

</table>
<br /><br />
<div id="EditModal" class="modal fade" role="dialog">
    <div class="modal-dialog">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Edit Appointment Type</h4>
            </div>
            <div class="modal-body">

            </div>
            <div class="modal-footer">
                <button type="button" id="edit-app-type-save" class="btn btn-default" data-dismiss="modal">Save</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>

    </div>
</div>
<script>
    $(function () {
        $(document).on('submit', '#frm-appointment-type-create', function (e) {
            e.preventDefault();

            $('.btn-app-type').prop('disable', true);

            $.ajax({
                url: this.action,
                type: this.method,
                data: $(this).serialize(),
                success: function (result) {
                    location.reload();
                }
            });
        });
        $(document).on('click', '#edit-app-type-save', function (e) {
            $("#frm-appointment-type-edit").submit();
        });
        $(document).on('submit', '#frm-appointment-type-edit', function (e) {
            e.preventDefault();
            $.ajax({
                url: this.action,
                type: this.method,
                data: $(this).serialize(),
                success: function (result) {
                    location.reload();
                }
            });
        });
        $(document).on('click', '.appoitnemnt-type-edit', function (e) {
            e.preventDefault();
            var URL = $(this).attr("href");
            var modal = $("#EditModal");
            var modalbody = modal.find('.modal-body');
            $.ajax({
                type: 'GET',
                url: URL
            })
          .done(function (data) {
              console.log(data);
              modalbody.html(data);
              $("#EditModal").modal("show");
          }).fail(function (jqXHR, textStatus, errorThrown) {
              checkAjaxError(jqXHR, null);
          });
        });
    });
</script>
