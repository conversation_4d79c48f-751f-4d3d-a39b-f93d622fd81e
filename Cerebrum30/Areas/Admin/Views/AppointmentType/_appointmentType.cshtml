﻿@model Cerebrum.ViewModels.Schedule.VMAppointmentTypeNew


@using (Html.BeginForm("Create", "appointmentType", new { area = "admin" }, FormMethod.Post, true, new { @id = "frm-appointment-type-create" }))
{
    @Html.AntiForgeryToken()

    <div class="form-inline">
        <h4>Appointment Type</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group">
            @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-8">
                @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.AppointmentType, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-4">
                @Html.DropDownListFor(model => model.AppointmentTypeId, new SelectList(ViewBag.ParentAppointmentTypes, "Id", "Name"), "Choose One", new { @class = "form-control" })
                @Html.ValidationMessageFor(model => model.AppointmentTypeId, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.VPRequired, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-10">
                <div class="checkbox">
                    @Html.EditorFor(model => model.VPRequired)
                    @Html.ValidationMessageFor(model => model.VPRequired, "", new { @class = "text-danger" })
                </div>
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.duration, htmlAttributes: new { @class = "control-label col-md-3" })
            <div class="col-md-8">
                @Html.EnumDropDownListFor(model => model.duration,"Select",new { @class="form-control"})
                @Html.ValidationMessageFor(model => model.duration, "", new { @class = "text-danger" })

            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                @Html.HiddenFor(model => model.Id)
                <input type="submit" value="Add" class="btn btn-default btn-app-type" />
            </div>
        </div>
    </div>
}
<hr />
<br />
