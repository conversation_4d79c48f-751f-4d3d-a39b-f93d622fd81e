# Build Error Analysis - 2025-07-25 12:05:09
# Cerebrum30 .NET 8 Upgrade Progress

## Total Errors: 2406

## Error Breakdown by Type (Descending Order):

### CS1973 - Dynamic Dispatch Errors (Extension Methods) (868 errors) 🔴 CRITICAL
**Sample Error**: 'IHtmlHelper<List<VMAdminUao>>' has no applicable method named 'PartialAsync' but appears to have an extension method by that name. Extension methods cannot be dynamically dispatched. Consider casting the dynamic arguments or calling the extension method without the extension method syntax.
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 64 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 64 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 40 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 40 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/eConsult/Views/Consult: 33 files


### CS1977 - Lambda Expression Errors (Dynamic Context) (742 errors) 🔴 CRITICAL
**Sample Error**: Cannot use a lambda expression as an argument to a dynamically dispatched operation without first casting it to a delegate or expression tree type.
**Most Affected Directories**:
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 112 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 112 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 31 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 31 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/AdminUser/Views/EMRMetrics: 27 files


### CS1963 - Expression Tree Errors (Dynamic Operations) (346 errors) 🔴 CRITICAL
**Sample Error**: An expression tree may not contain a dynamic operation
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/Shared/EditorTemplates: 30 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/Shared/EditorTemplates: 30 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 22 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 22 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Measurement: 19 files


### CS0103 - Name Does Not Exist Errors (Context/Namespace) (164 errors) 🟠 HIGH
**Sample Error**: The name 'Request' does not exist in the current context
**Most Affected Directories**:
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Patients: 21 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Patients: 21 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Measurement: 14 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Measurement: 14 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Shared: 10 files


### CS8323 - Other Compilation Errors (72 errors) 🟢 LOW
**Sample Error**: Named argument 'htmlAttributes' is used out-of-position but is followed by an unnamed argument
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 14 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 14 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Documents/Views/Uploads: 13 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Documents/Views/Uploads: 13 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Daysheet/Views/Appointments: 9 files


### CS1061 - Missing Member Errors (Method/Property Not Found) (56 errors) 🟡 MEDIUM
**Sample Error**: 'ReportPhrase' does not contain a definition for 'test' and no accessible extension method 'test' accepting a first argument of type 'ReportPhrase' could be found (are you missing a using directive or an assembly reference?)
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Labs/Views/HL7Report: 15 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Labs/Views/HL7Report: 15 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Labs/Views/OLIS: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/DemographicsDK: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Measurement: 3 files


### CS0234 - Other Compilation Errors (54 errors) 🟢 LOW
**Sample Error**: The type or namespace name 'Shared' does not exist in the namespace 'Cerebrum' (are you missing an assembly reference?)
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 27 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Schedule/Views/Appointments: 27 files


### CS1929 - Other Compilation Errors (34 errors) 🟢 LOW
**Sample Error**: 'IHtmlHelper<IEnumerable<Appointment>>' does not contain a definition for 'Action' and the best extension method overload 'UrlHelperExtensions.Action(IUrlHelper, string?, object?)' requires a receiver of type 'Microsoft.AspNetCore.Mvc.IUrlHelper'
**Most Affected Directories**:
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Daysheet/Views/Appointments: 2 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Medications/Views/DHDR: 2 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Medications/Views/DHDR: 2 files


### CS1026 - Other Compilation Errors (28 errors) 🟢 LOW
**Sample Error**: ) expected
**Most Affected Directories**:
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Shared: 5 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/Measurements/Views/Shared: 5 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 3 files
-          /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Views/Account: 3 files
- /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/VP/Views/VP: 3 files


### CS0839 - Other Compilation Errors (10 errors) 🟢 LOW
**Sample Error**: Argument missing


### CS1002 - Other Compilation Errors (8 errors) 🟢 LOW
**Sample Error**: ; expected


### CS0117 - Other Compilation Errors (6 errors) 🟢 LOW
**Sample Error**: 'HttpContext' does not contain a definition for 'Current'


### CS1513 - Other Compilation Errors (6 errors) 🟢 LOW
**Sample Error**: } expected


### CS1729 - Other Compilation Errors (6 errors) 🟢 LOW
**Sample Error**: 'ViewDataDictionary' does not contain a constructor that takes 0 arguments


### CS1003 - Other Compilation Errors (4 errors) 🟢 LOW
**Sample Error**: Syntax error, ',' expected


### CS0246 - Type Not Found Errors (Missing References) (2 errors) 🟡 MEDIUM
**Sample Error**: The type or namespace name 'HtmlString' could not be found (are you missing a using directive or an assembly reference?)

## Recommended Fix Priority Order:

### Phase 1: Dynamic Operation Errors (CS1963/CS1973/CS1977)
These represent ~80% of current errors and are blocking compilation.
- **CS1973** (Dynamic Dispatch): Replace extension method calls on dynamic types
- **CS1963** (Expression Trees): Convert dynamic operations to typed operations  
- **CS1977** (Lambda Expressions): Fix lambda expressions with dynamic dispatch

### Phase 2: Method Signature Errors (CS7036)
- Html.BeginForm parameter fixes
- Other method signature mismatches from .NET Framework to .NET Core migration

### Phase 3: Context/Namespace Errors (CS0103)
- Remaining context access issues
- Missing helper method references

### Phase 4: Reference and Type Errors (CS0246/CS1061/CS0104)
- Assembly reference issues
- Missing member resolution
- Namespace conflict resolution

## Next Steps:
1. Focus on CS1963/CS1973/CS1977 errors first (highest impact)
2. Create helper methods to replace dynamic operations with typed alternatives
3. Update expression tree usage to avoid dynamic operations
4. Fix method signature issues systematically

