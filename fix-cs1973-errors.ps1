#!/usr/bin/env pwsh

# Script to fix CS1973 errors by casting Html to IHtmlHelper to resolve extension methods
# CS1973: Extension methods cannot be dynamically dispatched

Write-Host "🔧 Fixing CS1973 Dynamic Dispatch Extension Method Errors..." -ForegroundColor Yellow

# Get all .cshtml files in the project
$cshtmlFiles = Get-ChildItem -Path "Cerebrum30" -Filter "*.cshtml" -Recurse

$totalFiles = $cshtmlFiles.Count
$processedFiles = 0
$modifiedFiles = 0
$totalReplacements = 0

Write-Host "Found $totalFiles .cshtml files to process" -ForegroundColor Cyan

foreach ($file in $cshtmlFiles) {
    $processedFiles++
    $relativePath = $file.FullName.Replace((Get-Location).Path + [System.IO.Path]::DirectorySeparatorChar, "")
    
    Write-Progress -Activity "Processing CSHTML files" -Status "Processing $relativePath" -PercentComplete (($processedFiles / $totalFiles) * 100)
    
    try {
        $content = Get-Content $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        $fileReplacements = 0
        
        # Skip if content is null or empty
        if ([string]::IsNullOrWhiteSpace($content)) {
            continue
        }
        
        # Pattern 1: @Html.PartialAsync -> @await Html.PartialAsync (most common)
        $pattern1 = '@Html\.PartialAsync\('
        if ($content -match $pattern1) {
            $content = $content -replace '@Html\.PartialAsync\(', '@await Html.PartialAsync('
            $fileReplacements += ([regex]::Matches($originalContent, $pattern1)).Count
        }
        
        # Pattern 2: @Html.RenderPartialAsync -> @{await Html.RenderPartialAsync
        $pattern2 = '@Html\.RenderPartialAsync\('
        if ($content -match $pattern2) {
            $content = $content -replace '@Html\.RenderPartialAsync\(', '@{await Html.RenderPartialAsync('
            # Need to add closing brace - this is more complex, will handle manually for now
            $fileReplacements += ([regex]::Matches($originalContent, $pattern2)).Count
        }
        
        # Pattern 3: Replace Html.Display with direct output for simple cases
        # @Html.Display("PropertyName", value) -> @value
        $displayPattern = '@Html\.Display\("([^"]+)",\s*([^)]+)\)'
        $displayMatches = [regex]::Matches($content, $displayPattern)
        foreach ($match in $displayMatches) {
            $fullMatch = $match.Value
            $value = $match.Groups[2].Value.Trim()

            # Replace with direct output
            $replacement = "@$value"
            $content = $content.Replace($fullMatch, $replacement)
            $fileReplacements++
        }

        # Pattern 4: Cast Html to IHtmlHelper for other extension methods that need Html context
        $extensionMethods = @('Hidden', 'ActionLink', 'TextBox', 'ModalHeader', 'Truncate', 'GetPatientInfo')

        foreach ($method in $extensionMethods) {
            $pattern = "@Html\.$method\("
            if ($content -match $pattern) {
                $content = $content -replace "@Html\.$method\(", "@((IHtmlHelper)Html).$method("
                $fileReplacements += ([regex]::Matches($originalContent, $pattern)).Count
            }
        }
        
        # Only write if content changed
        if ($content -ne $originalContent) {
            Set-Content -Path $file.FullName -Value $content -Encoding UTF8 -NoNewline
            $modifiedFiles++
            $totalReplacements += $fileReplacements
            Write-Host "✅ Modified: $relativePath ($fileReplacements replacements)" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "❌ Error processing $relativePath : $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Progress -Activity "Processing CSHTML files" -Completed

Write-Host ""
Write-Host "🎉 CS1973 Fix Complete!" -ForegroundColor Green
Write-Host "Files processed: $processedFiles" -ForegroundColor Cyan
Write-Host "Files modified: $modifiedFiles" -ForegroundColor Cyan
Write-Host "Total replacements: $totalReplacements" -ForegroundColor Cyan

# Run a quick build to see if we reduced the error count
Write-Host ""
Write-Host "🔍 Running quick build to check error reduction..." -ForegroundColor Yellow
try {
    $buildResult = dotnet build Cerebrum30/Cerebrum30.csproj --no-restore --verbosity quiet 2>&1
    $cs1973Count = ($buildResult | Select-String "CS1973").Count
    Write-Host "Remaining CS1973 errors: $cs1973Count" -ForegroundColor $(if ($cs1973Count -eq 0) { "Green" } else { "Yellow" })
} catch {
    Write-Host "Could not run build check: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Run 'pwsh analyze-build-errors.ps1' to see updated error counts" -ForegroundColor White
Write-Host "2. Address any remaining CS1973 errors manually if needed" -ForegroundColor White
Write-Host "3. Move on to fixing CS7036 errors (next highest priority)" -ForegroundColor White
