param(
    [string]$SolutionPath = "Cerebrum30.sln",
    [string]$LogFile = "cs7036-fixes.log"
)

Write-Host "Starting CS7036 error fixes for Cerebrum30 .NET 8 upgrade..." -ForegroundColor Cyan

# Initialize counters
$totalFilesProcessed = 0
$totalFixesApplied = 0
$errorCount = 0

# Create log file
"CS7036 Error Fixes - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" | Out-File -FilePath $LogFile -Encoding UTF8
"=" * 60 | Out-File -FilePath $LogFile -Append -Encoding UTF8

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage -ForegroundColor $(if($Level -eq "ERROR") {"Red"} elseif($Level -eq "WARN") {"Yellow"} else {"White"})
    $logMessage | Out-File -FilePath $LogFile -Append -Encoding UTF8
}

function Repair-LabelMethods {
    param([string]$FilePath, [string]$Content)

    $originalContent = $Content
    $fixCount = 0

    # Fix Html.Label calls with named htmlAttributes parameter but missing labelText
    # Pattern: @Html.Label("text", htmlAttributes: new {...}) -> @Html.Label("text", "", new {...})
    $pattern1 = '(@Html\.Label\(\s*"[^"]*"\s*,\s*)htmlAttributes:\s*(new\s*\{[^}]*\})\s*\)'
    if ($Content -match $pattern1) {
        $Content = $Content -replace $pattern1, '$1"", $2)'
        $regexMatches = [regex]::Matches($originalContent, $pattern1)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.Label calls with named htmlAttributes missing labelText in $FilePath"
    }

    # Fix Html.Label calls with expression and named htmlAttributes parameter but missing labelText
    # Pattern: @Html.Label(expression, htmlAttributes: new {...}) -> @Html.Label(expression, "", new {...})
    $pattern1b = '(@Html\.Label\(\s*[^",)]+\s*,\s*)htmlAttributes:\s*(new\s*\{[^}]*\})\s*\)'
    if ($Content -match $pattern1b) {
        $Content = $Content -replace $pattern1b, '$1"", $2)'
        $regexMatches = [regex]::Matches($originalContent, $pattern1b)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.Label expression calls with named htmlAttributes missing labelText in $FilePath"
    }

    # Fix Html.Label calls with htmlAttributes but missing labelText
    # Pattern: @Html.Label("text", new {...}) -> @Html.Label("text", "", new {...})
    $pattern2 = '(@Html\.Label\(\s*"[^"]*"\s*,\s*)(new\s*\{[^}]*\})\s*\)'
    if ($Content -match $pattern2) {
        $Content = $Content -replace $pattern2, '$1"", $2)'
        $regexMatches = [regex]::Matches($originalContent, $pattern2)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.Label calls with htmlAttributes missing labelText in $FilePath"
    }

    # Fix Html.Label calls missing labelText parameter - simple case
    # Pattern: @Html.Label("propertyName") -> @Html.Label("propertyName", "")
    $pattern3 = '(@Html\.Label\(\s*"[^"]*"\s*)\)'
    if ($Content -match $pattern3) {
        $Content = $Content -replace $pattern3, '$1, "")'
        $regexMatches = [regex]::Matches($originalContent, $pattern3)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.Label calls missing labelText in $FilePath"
    }

    # Fix Html.Label calls with expression but missing labelText
    # Pattern: @Html.Label(expression) -> @Html.Label(expression, "")
    $pattern4 = '(@Html\.Label\(\s*[^",)]+\s*)\)'
    if ($Content -match $pattern4) {
        $Content = $Content -replace $pattern4, '$1, "")'
        $regexMatches = [regex]::Matches($originalContent, $pattern4)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.Label expression calls missing labelText in $FilePath"
    }

    # Fix Html.LabelFor calls missing htmlAttributes parameter - quoted strings
    # Pattern: @Html.LabelFor(x => x.Property, "text") -> @Html.LabelFor(x => x.Property, "text", new {})
    $pattern5 = '(@Html\.LabelFor\(\s*[^,)]+\s*,\s*"[^"]*"\s*)\)'
    if ($Content -match $pattern5) {
        $Content = $Content -replace $pattern5, '$1, new {})'
        $regexMatches = [regex]::Matches($originalContent, $pattern5)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.LabelFor calls with quoted strings missing htmlAttributes in $FilePath"
    }

    # Fix Html.LabelFor calls missing htmlAttributes parameter - complex expressions
    # Pattern: @Html.LabelFor(x => x.Property, @Model.property ?? "") -> @Html.LabelFor(x => x.Property, @Model.property ?? "", new {})
    $pattern6 = '(@Html\.LabelFor\(\s*[^,)]+\s*,\s*[^,)]+\s*)\)'
    if ($Content -match $pattern6) {
        $Content = $Content -replace $pattern6, '$1, new {})'
        $regexMatches = [regex]::Matches($originalContent, $pattern6)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.LabelFor calls with expressions missing htmlAttributes in $FilePath"
    }

    return @{
        Content = $Content
        FixCount = $fixCount
    }
}

function Repair-DropDownMethods {
    param([string]$FilePath, [string]$Content)

    $originalContent = $Content
    $fixCount = 0

    # Fix Html.DropDownListFor calls with named htmlAttributes parameter but missing optionLabel
    # Pattern: @Html.DropDownListFor(x => x.Property, selectList, htmlAttributes: new {...}) -> @Html.DropDownListFor(x => x.Property, selectList, "", new {...})
    $pattern1 = '(@Html\.DropDownListFor\(\s*[^,)]+\s*,\s*[^,)]+\s*,\s*)htmlAttributes:\s*(new\s*\{[^}]*\})\s*\)'
    if ($Content -match $pattern1) {
        $Content = $Content -replace $pattern1, '$1"", $2)'
        $regexMatches = [regex]::Matches($originalContent, $pattern1)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.DropDownListFor calls with named htmlAttributes missing optionLabel in $FilePath"
    }

    # Fix Html.DropDownListFor calls missing optionLabel parameter - simple case
    # Pattern: @Html.DropDownListFor(x => x.Property, selectList) -> @Html.DropDownListFor(x => x.Property, selectList, "")
    $pattern2 = '(@Html\.DropDownListFor\(\s*[^,)]+\s*,\s*[^,)]+\s*)\)'
    if ($Content -match $pattern2) {
        $Content = $Content -replace $pattern2, '$1, "")'
        $regexMatches = [regex]::Matches($originalContent, $pattern2)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.DropDownListFor calls missing optionLabel in $FilePath"
    }

    # Fix Html.DropDownListFor calls missing htmlAttributes parameter
    # Pattern: @Html.DropDownListFor(x => x.Property, selectList, "optionLabel") -> @Html.DropDownListFor(x => x.Property, selectList, "optionLabel", new {})
    $pattern3 = '(@Html\.DropDownListFor\(\s*[^,)]+\s*,\s*[^,)]+\s*,\s*"[^"]*"\s*)\)'
    if ($Content -match $pattern3) {
        $Content = $Content -replace $pattern3, '$1, new {})'
        $regexMatches = [regex]::Matches($originalContent, $pattern3)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.DropDownListFor calls missing htmlAttributes in $FilePath"
    }

    return @{
        Content = $Content
        FixCount = $fixCount
    }
}

function Repair-EditorMethods {
    param([string]$FilePath, [string]$Content)

    $originalContent = $Content
    $fixCount = 0

    # Fix Html.EditorFor calls missing additionalViewData parameter - quoted strings
    # Pattern: @Html.EditorFor(x => x.Property, "template", "htmlFieldName") -> @Html.EditorFor(x => x.Property, "template", "htmlFieldName", new {})
    $pattern1 = '(@Html\.EditorFor\(\s*[^,)]+\s*,\s*"[^"]*"\s*,\s*"[^"]*"\s*)\)'
    if ($Content -match $pattern1) {
        $Content = $Content -replace $pattern1, '$1, new {})'
        $regexMatches = [regex]::Matches($originalContent, $pattern1)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.EditorFor calls with quoted strings missing additionalViewData in $FilePath"
    }

    # Fix Html.EditorFor calls missing additionalViewData parameter - complex expressions
    # Pattern: @Html.EditorFor(x => x.Property, "template", String.Format(...)) -> @Html.EditorFor(x => x.Property, "template", String.Format(...), new {})
    $pattern2 = '(@Html\.EditorFor\([^,)]+,\s*"[^"]*",\s*String\.Format\([^)]+\))\)'
    if ($Content -match $pattern2) {
        $Content = $Content -replace $pattern2, '$1, new {})'
        $regexMatches = [regex]::Matches($originalContent, $pattern2)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.EditorFor calls with String.Format missing additionalViewData in $FilePath"
    }

    # Fix Html.EditorFor calls missing additionalViewData parameter - general expressions
    # Pattern: @Html.EditorFor(x => x.Property, "template", expression) -> @Html.EditorFor(x => x.Property, "template", expression, new {})
    $pattern3 = '(@Html\.EditorFor\(\s*[^,)]+\s*,\s*"[^"]*"\s*,\s*[^,)]+\s*)\)'
    if ($Content -match $pattern3) {
        $Content = $Content -replace $pattern3, '$1, new {})'
        $regexMatches = [regex]::Matches($originalContent, $pattern3)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.EditorFor calls with expressions missing additionalViewData in $FilePath"
    }

    return @{
        Content = $Content
        FixCount = $fixCount
    }
}

function Repair-RadioButtonMethods {
    param([string]$FilePath, [string]$Content)

    $originalContent = $Content
    $fixCount = 0

    # Fix Html.RadioButtonFor calls missing htmlAttributes parameter
    # Pattern: @Html.RadioButtonFor(x => x.Property, value) -> @Html.RadioButtonFor(x => x.Property, value, new {})
    $pattern1 = '(@Html\.RadioButtonFor\(\s*[^,)]+\s*,\s*[^,)]+\s*)\)'
    if ($Content -match $pattern1) {
        $Content = $Content -replace $pattern1, '$1, new {})'
        $regexMatches = [regex]::Matches($originalContent, $pattern1)
        $fixCount += $regexMatches.Count
        Write-Log "Fixed $($regexMatches.Count) Html.RadioButtonFor calls missing htmlAttributes in $FilePath"
    }

    return @{
        Content = $Content
        FixCount = $fixCount
    }
}

# Get all .cshtml files with CS7036 errors
Write-Log "Scanning for .cshtml files with CS7036 errors..."

# Build to get current errors
$buildOutput = dotnet build $SolutionPath --no-restore --verbosity minimal 2>&1
$cs7036Files = @()

$buildOutput | ForEach-Object {
    $line = $_.ToString()
    if ($line -match '^([^(]+\.cshtml)\(\d+,\d+\):\s+error\s+CS7036') {
        $filePath = $matches[1]
        if ($cs7036Files -notcontains $filePath) {
            $cs7036Files += $filePath
        }
    }
}

Write-Log "Found $($cs7036Files.Count) files with CS7036 errors"

# Process each file
foreach ($filePath in $cs7036Files) {
    try {
        if (-not (Test-Path $filePath)) {
            Write-Log "File not found: $filePath" "WARN"
            continue
        }
        
        Write-Log "Processing: $filePath"
        $totalFilesProcessed++
        
        # Read file content
        $content = Get-Content -Path $filePath -Raw -Encoding UTF8
        $originalContent = $content
        $fileFixCount = 0
        
        # Apply all fixes
        $labelResult = Repair-LabelMethods -FilePath $filePath -Content $content
        $content = $labelResult.Content
        $fileFixCount += $labelResult.FixCount

        $dropdownResult = Repair-DropDownMethods -FilePath $filePath -Content $content
        $content = $dropdownResult.Content
        $fileFixCount += $dropdownResult.FixCount

        $editorResult = Repair-EditorMethods -FilePath $filePath -Content $content
        $content = $editorResult.Content
        $fileFixCount += $editorResult.FixCount

        $radioResult = Repair-RadioButtonMethods -FilePath $filePath -Content $content
        $content = $radioResult.Content
        $fileFixCount += $radioResult.FixCount
        
        # Write back if changes were made
        if ($content -ne $originalContent) {
            Set-Content -Path $filePath -Value $content -Encoding UTF8 -NoNewline
            $totalFixesApplied += $fileFixCount
            Write-Log "Applied $fileFixCount fixes to $filePath" "INFO"
        } else {
            Write-Log "No fixes needed for $filePath" "INFO"
        }
        
    } catch {
        $errorCount++
        Write-Log "Error processing $filePath : $($_.Exception.Message)" "ERROR"
    }
}

# Summary
Write-Log "=" * 60
Write-Log "CS7036 Fix Summary:"
Write-Log "Files processed: $totalFilesProcessed"
Write-Log "Total fixes applied: $totalFixesApplied"
Write-Log "Errors encountered: $errorCount"

# Test build
Write-Log "Testing build after fixes..."
$testBuild = dotnet build $SolutionPath --no-restore --verbosity minimal 2>&1
$remainingCS7036 = ($testBuild | Where-Object { $_.ToString() -match 'CS7036' }).Count

Write-Log "Remaining CS7036 errors: $remainingCS7036"

if ($remainingCS7036 -eq 0) {
    Write-Host "`n✅ All CS7036 errors have been fixed!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  $remainingCS7036 CS7036 errors remain. Manual review may be needed." -ForegroundColor Yellow
}

Write-Host "`nDetailed log saved to: $LogFile" -ForegroundColor Cyan
