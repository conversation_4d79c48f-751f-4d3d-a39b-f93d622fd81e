#!/usr/bin/env pwsh

# Test script to fix CS1973 errors on a small subset of files

Write-Host "🧪 Testing CS1973 fixes on a small subset..." -ForegroundColor Yellow

# Test on just a few files first
$testFiles = @(
    "Cerebrum30/Areas/Admin/Views/AdminUAO/_uaoList.cshtml",
    "Cerebrum30/Views/Patients/_patientReportContact.cshtml",
    "Cerebrum30/Views/Shared/HL7Results.cshtml"
)

$modifiedFiles = 0
$totalReplacements = 0

foreach ($filePath in $testFiles) {
    if (-not (Test-Path $filePath)) {
        Write-Host "⚠️  File not found: $filePath" -ForegroundColor Yellow
        continue
    }
    
    Write-Host "Processing: $filePath" -ForegroundColor Cyan
    
    try {
        $content = Get-Content $filePath -Raw -Encoding UTF8
        $originalContent = $content
        $fileReplacements = 0
        
        # Skip if content is null or empty
        if ([string]::IsNullOrWhiteSpace($content)) {
            continue
        }
        
        # Pattern 1: Cast Html to IHtmlHelper for extension methods that need Html context
        $extensionMethods = @('Hidden', 'ActionLink', 'TextBox', 'Truncate', 'GetPatientInfo', 'DropDownList', 'CheckBox', 'RadioButton', 'Password', 'TextArea', 'ListBox')

        foreach ($method in $extensionMethods) {
            # Very specific pattern: @Html.Method( at start of line or after whitespace
            $pattern = '(^|\s)@Html\.' + [regex]::Escape($method) + '\('
            $replacement = '$1@((IHtmlHelper)Html).' + $method + '('
            
            if ($content -match $pattern) {
                $regexMatches = [regex]::Matches($content, $pattern)
                $content = [regex]::Replace($content, $pattern, $replacement)
                $fileReplacements += $regexMatches.Count
                Write-Host "  - Fixed $($regexMatches.Count) $method calls" -ForegroundColor Green
            }
        }
        
        # Pattern 2: Handle ModalHeader specifically
        $modalHeaderPattern = '@Html\.ModalHeader\('
        if ($content -match $modalHeaderPattern) {
            $matches = [regex]::Matches($content, $modalHeaderPattern)
            $content = $content -replace $modalHeaderPattern, '@((IHtmlHelper)Html).ModalHeader('
            $fileReplacements += $matches.Count
            Write-Host "  - Fixed $($matches.Count) ModalHeader calls" -ForegroundColor Green
        }
        
        # Pattern 3: Fix RenderPartialAsync calls
        $renderPartialPattern = '(?<!@\{ await )@Html\.RenderPartialAsync\('
        if ($content -match $renderPartialPattern) {
            $matches = [regex]::Matches($content, $renderPartialPattern)
            $content = $content -replace $renderPartialPattern, '@{ await Html.RenderPartialAsync('
            $fileReplacements += $matches.Count
            Write-Host "  - Fixed $($matches.Count) RenderPartialAsync calls" -ForegroundColor Green
        }
        
        # Only write if content changed
        if ($content -ne $originalContent) {
            Set-Content -Path $filePath -Value $content -Encoding UTF8 -NoNewline
            $modifiedFiles++
            $totalReplacements += $fileReplacements
            Write-Host "✅ Modified: $filePath ($fileReplacements replacements)" -ForegroundColor Green
        } else {
            Write-Host "  - No changes needed" -ForegroundColor Gray
        }
        
    } catch {
        Write-Host "❌ Error processing $filePath : $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🎉 Test Complete!" -ForegroundColor Green
Write-Host "Files modified: $modifiedFiles" -ForegroundColor Cyan
Write-Host "Total replacements: $totalReplacements" -ForegroundColor Cyan

# Test build on one of the files
Write-Host ""
Write-Host "🔍 Testing build on modified files..." -ForegroundColor Yellow
try {
    $buildResult = dotnet build Cerebrum30/Cerebrum30.csproj --no-restore --verbosity quiet 2>&1
    $cs1973Count = ($buildResult | Select-String "CS1973").Count
    $rz1007Count = ($buildResult | Select-String "RZ1007").Count
    Write-Host "CS1973 errors: $cs1973Count" -ForegroundColor $(if ($cs1973Count -lt 754) { "Green" } else { "Yellow" })
    Write-Host "RZ1007 errors: $rz1007Count" -ForegroundColor $(if ($rz1007Count -eq 0) { "Green" } else { "Red" })
} catch {
    Write-Host "Could not run build check: $($_.Exception.Message)" -ForegroundColor Red
}
