# Fix CS1963 Expression Tree Dynamic Operation Errors
# These errors occur when loop variables are captured in lambda expressions for Html helpers

param(
    [string]$Path = "Cerebrum30",
    [switch]$WhatIf = $false
)

Write-Host "🔧 Fixing CS1963 Expression Tree Dynamic Operation Errors..." -ForegroundColor Cyan
Write-Host "Target Path: $Path" -ForegroundColor Gray
Write-Host "WhatIf Mode: $WhatIf" -ForegroundColor Gray
Write-Host ""

$totalFiles = 0
$totalReplacements = 0

# Get all .cshtml files that have CS1963 errors
$errorFiles = @()
if (Test-Path "raw-errors.csv") {
    $csvContent = Import-Csv "raw-errors.csv" -Header "ErrorCode", "Message", "File", "Details"
    $errorFiles = $csvContent | Where-Object { $_.ErrorCode -eq "CS1963" } | Select-Object -ExpandProperty File | Sort-Object -Unique
}

if ($errorFiles.Count -eq 0) {
    Write-Host "❌ No CS1963 error files found in raw-errors.csv" -ForegroundColor Red
    exit 1
}

Write-Host "📁 Found $($errorFiles.Count) files with CS1963 errors" -ForegroundColor Yellow

foreach ($file in $errorFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "⚠️  File not found: $file" -ForegroundColor Yellow
        continue
    }

    Write-Host "🔍 Processing: $file" -ForegroundColor White
    $content = Get-Content $file -Raw
    $originalContent = $content
    $fileReplacements = 0

    # Pattern 1: Html.HiddenFor with loop variables
    # @Html.HiddenFor(x => Model.Collection[i].Property) -> @Html.Hidden("Collection[" + i + "].Property", Model.Collection[i].Property)
    $pattern1 = '@Html\.HiddenFor\(x\s*=>\s*Model\.([^[]+)\[([^\]]+)\]\.([^)]+)\)'
    if ($content -match $pattern1) {
        $regexMatches1 = [regex]::Matches($content, $pattern1)
        foreach ($match in $regexMatches1) {
            $collection = $match.Groups[1].Value
            $index = $match.Groups[2].Value
            $property = $match.Groups[3].Value
            $replacement = "@Html.Hidden(`"$collection[`" + $index + `"].$property`", Model.$collection[$index].$property)"
            $content = $content -replace [regex]::Escape($match.Value), $replacement
            $fileReplacements++
        }
        Write-Host "  - Fixed $($regexMatches1.Count) HiddenFor calls with single index" -ForegroundColor Green
    }

    # Pattern 2: Html.HiddenFor with nested loop variables
    # @Html.HiddenFor(x => Model.Collection[i].SubCollection[j].Property) -> @Html.Hidden("Collection[" + i + "].SubCollection[" + j + "].Property", Model.Collection[i].SubCollection[j].Property)
    $pattern2 = '@Html\.HiddenFor\(x\s*=>\s*Model\.([^[]+)\[([^\]]+)\]\.([^[]+)\[([^\]]+)\]\.([^)]+)\)'
    if ($content -match $pattern2) {
        $regexMatches2 = [regex]::Matches($content, $pattern2)
        foreach ($match in $regexMatches2) {
            $collection1 = $match.Groups[1].Value
            $index1 = $match.Groups[2].Value
            $collection2 = $match.Groups[3].Value
            $index2 = $match.Groups[4].Value
            $property = $match.Groups[5].Value
            $replacement = "@Html.Hidden(`"$collection1[`" + $index1 + `"].$collection2[`" + $index2 + `"].$property`", Model.$collection1[$index1].$collection2[$index2].$property)"
            $content = $content -replace [regex]::Escape($match.Value), $replacement
            $fileReplacements++
        }
        Write-Host "  - Fixed $($regexMatches2.Count) HiddenFor calls with nested indices" -ForegroundColor Green
    }

    # Pattern 2b: Fix incorrectly replaced nested indices (post-processing fix)
    # "Collection[" + z + "].Measurements[i].Property" -> "Collection[" + z + "].Measurements[" + i + "].Property"
    $pattern2b = '"([^"]+)\[" \+ ([^"]+) \+ "\]\.([^[]+)\[([^\]]+)\]\.([^"]+)"'
    if ($content -match $pattern2b) {
        $regexMatches2b = [regex]::Matches($content, $pattern2b)
        foreach ($match in $regexMatches2b) {
            $collection1 = $match.Groups[1].Value
            $index1 = $match.Groups[2].Value
            $collection2 = $match.Groups[3].Value
            $index2 = $match.Groups[4].Value
            $property = $match.Groups[5].Value
            $replacement = "`"$collection1[`" + $index1 + `"].$collection2[`" + $index2 + `"].$property`""
            $content = $content -replace [regex]::Escape($match.Value), $replacement
            $fileReplacements++
        }
        Write-Host "  - Fixed $($regexMatches2b.Count) incorrectly replaced nested indices" -ForegroundColor Green
    }

    # Pattern 3: Html.CheckBoxFor with loop variables
    # @Html.CheckBoxFor(x => Model.Collection[i].Property) -> @Html.CheckBox("Collection[" + i + "].Property", Model.Collection[i].Property)
    $pattern3 = '@Html\.CheckBoxFor\(x\s*=>\s*Model\.([^[]+)\[([^\]]+)\]\.([^)]+)\)'
    if ($content -match $pattern3) {
        $regexMatches3 = [regex]::Matches($content, $pattern3)
        foreach ($match in $regexMatches3) {
            $collection = $match.Groups[1].Value
            $index = $match.Groups[2].Value
            $property = $match.Groups[3].Value
            $replacement = "@Html.CheckBox(`"$collection[`" + $index + `"].$property`", Model.$collection[$index].$property)"
            $content = $content -replace [regex]::Escape($match.Value), $replacement
            $fileReplacements++
        }
        Write-Host "  - Fixed $($regexMatches3.Count) CheckBoxFor calls" -ForegroundColor Green
    }

    # Pattern 4: Html.TextBoxFor with loop variables
    # @Html.TextBoxFor(x => Model.Collection[i].Property) -> @Html.TextBox("Collection[" + i + "].Property", Model.Collection[i].Property)
    $pattern4 = '@Html\.TextBoxFor\(x\s*=>\s*Model\.([^[]+)\[([^\]]+)\]\.([^)]+)\)'
    if ($content -match $pattern4) {
        $regexMatches4 = [regex]::Matches($content, $pattern4)
        foreach ($match in $regexMatches4) {
            $collection = $match.Groups[1].Value
            $index = $match.Groups[2].Value
            $property = $match.Groups[3].Value
            $replacement = "@Html.TextBox(`"$collection[`" + $index + `"].$property`", Model.$collection[$index].$property)"
            $content = $content -replace [regex]::Escape($match.Value), $replacement
            $fileReplacements++
        }
        Write-Host "  - Fixed $($regexMatches4.Count) TextBoxFor calls" -ForegroundColor Green
    }

    # Pattern 5: Html.TextBoxFor with attributes and loop variables
    # @Html.TextBoxFor(s => item.Value, new { ... }) -> @Html.TextBox("Value", item.Value, new { ... })
    $pattern5 = '@Html\.TextBoxFor\(s\s*=>\s*item\.([^,]+),\s*new\s*\{([^}]+)\}\)'
    if ($content -match $pattern5) {
        $regexMatches5 = [regex]::Matches($content, $pattern5)
        foreach ($match in $regexMatches5) {
            $property = $match.Groups[1].Value
            $attributes = $match.Groups[2].Value
            $replacement = "@Html.TextBox(`"$property`", item.$property, new {$attributes})"
            $content = $content -replace [regex]::Escape($match.Value), $replacement
            $fileReplacements++
        }
        Write-Host "  - Fixed $($regexMatches5.Count) TextBoxFor calls with attributes" -ForegroundColor Green
    }

    # Pattern 6: Html.HiddenFor with triple nested indices
    # @Html.HiddenFor(x => Model.Collection[i].SubCollection[j].SubSubCollection[k].Property)
    $pattern6 = '@Html\.HiddenFor\(x\s*=>\s*Model\.([^[]+)\[([^\]]+)\]\.([^[]+)\[([^\]]+)\]\.([^[]+)\[([^\]]+)\]\.([^)]+)\)'
    if ($content -match $pattern6) {
        $regexMatches6 = [regex]::Matches($content, $pattern6)
        foreach ($match in $regexMatches6) {
            $collection1 = $match.Groups[1].Value
            $index1 = $match.Groups[2].Value
            $collection2 = $match.Groups[3].Value
            $index2 = $match.Groups[4].Value
            $collection3 = $match.Groups[5].Value
            $index3 = $match.Groups[6].Value
            $property = $match.Groups[7].Value
            $replacement = "@Html.Hidden(`"$collection1[`" + $index1 + `"].$collection2[`" + $index2 + `"].$collection3[`" + $index3 + `"].$property`", Model.$collection1[$index1].$collection2[$index2].$collection3[$index3].$property)"
            $content = $content -replace [regex]::Escape($match.Value), $replacement
            $fileReplacements++
        }
        Write-Host "  - Fixed $($regexMatches6.Count) HiddenFor calls with triple nested indices" -ForegroundColor Green
    }

    # Pattern 7: Html.CheckBoxFor with nested indices
    # @Html.CheckBoxFor(x => Model.Collection[i].SubCollection[j].Property)
    $pattern7 = '@Html\.CheckBoxFor\(x\s*=>\s*Model\.([^[]+)\[([^\]]+)\]\.([^[]+)\[([^\]]+)\]\.([^)]+)\)'
    if ($content -match $pattern7) {
        $regexMatches7 = [regex]::Matches($content, $pattern7)
        foreach ($match in $regexMatches7) {
            $collection1 = $match.Groups[1].Value
            $index1 = $match.Groups[2].Value
            $collection2 = $match.Groups[3].Value
            $index2 = $match.Groups[4].Value
            $property = $match.Groups[5].Value
            $replacement = "@Html.CheckBox(`"$collection1[`" + $index1 + `"].$collection2[`" + $index2 + `"].$property`", Model.$collection1[$index1].$collection2[$index2].$property)"
            $content = $content -replace [regex]::Escape($match.Value), $replacement
            $fileReplacements++
        }
        Write-Host "  - Fixed $($regexMatches7.Count) CheckBoxFor calls with nested indices" -ForegroundColor Green
    }

    if ($fileReplacements -gt 0) {
        if (-not $WhatIf) {
            Set-Content $file -Value $content -NoNewline
            Write-Host "✅ Updated $file with $fileReplacements replacements" -ForegroundColor Green
        } else {
            Write-Host "🔍 Would update $file with $fileReplacements replacements" -ForegroundColor Cyan
        }
        $totalFiles++
        $totalReplacements += $fileReplacements
    } else {
        Write-Host "  - No replacements needed" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "📊 Summary:" -ForegroundColor Cyan
Write-Host "  Files processed: $totalFiles" -ForegroundColor White
Write-Host "  Total replacements: $totalReplacements" -ForegroundColor White

if ($totalReplacements -gt 0) {
    Write-Host ""
    Write-Host "🔄 Next steps:" -ForegroundColor Yellow
    Write-Host "  1. Build the solution to verify fixes" -ForegroundColor White
    Write-Host "  2. Run analyze-build-errors.ps1 to check progress" -ForegroundColor White
    Write-Host "  3. Test the affected views manually" -ForegroundColor White
}
